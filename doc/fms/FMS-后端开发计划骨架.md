### FMS 后端开发计划骨架（Hyperf + PHP 7.4）

作者：qinsx

---

## 1. 模块划分与依赖

- 目录模块 Directory ⇄ ACL、日志
- 文件模块 File ⇄ 目录、标签、ACL、分享、日志
- 标签模块 Tag ⇄ 文件
- ACL 模块 Acl ⇄ 目录/文件/主体/条件
- 分享模块 Share ⇄ 文件、ACL
- 回收站模块 Recycle ⇄ 目录/文件
- 审计模块 OperationLog ⇄ 所有业务

```mermaid
graph LR
  Directory-->Acl
  File-->Directory
  File-->Tag
  File-->Acl
  File-->Share
  Share-->Acl
  Recycle-->Directory
  Recycle-->File
  OperationLog-->Directory
  OperationLog-->File
  OperationLog-->Acl
  OperationLog-->Share
```

## 2. 目录结构建议（节选）

- app/Model/TchipBiFms/*
- app/Core/Service/TchipBiFms/*
- app/Controller/TchipBiFms/*
- migrations/*

## 3. 数据库迁移文件（建议命名）

| 表 | 迁移文件名 |
|:-|:-|
| fms_directories | 20250904090001_create_fms_directories_table.php |
| fms_files | 20250904090002_create_fms_files_table.php |
| fms_subjects | 20250904090003_create_fms_subjects_table.php |
| fms_acl_rules | 20250904090004_create_fms_acl_rules_table.php |
| fms_acl_conditions | 20250904090005_create_fms_acl_conditions_table.php |
| fms_tags | 20250904090006_create_fms_tags_table.php |
| fms_file_tags | 20250904090007_create_fms_file_tags_table.php |
| fms_recycle_bin | 20250904090008_create_fms_recycle_bin_table.php |
| fms_operation_logs | 20250904090009_create_fms_operation_logs_table.php |
| fms_file_shares | 20250904090010_create_fms_file_shares_table.php |

## 4. 模型文件（Model）

| 模型 | 路径 |
|:-|:-|
| FmsDirectory | app/Model/TchipBiFms/FmsDirectory.php |
| FmsFile | app/Model/TchipBiFms/FmsFile.php |
| FmsSubject | app/Model/TchipBiFms/FmsSubject.php |
| FmsAclRule | app/Model/TchipBiFms/FmsAclRule.php |
| FmsAclCondition | app/Model/TchipBiFms/FmsAclCondition.php |
| FmsTag | app/Model/TchipBiFms/FmsTag.php |
| FmsFileTag | app/Model/TchipBiFms/FmsFileTag.php |
| FmsRecycleBin | app/Model/TchipBiFms/FmsRecycleBin.php |
| FmsOperationLog | app/Model/TchipBiFms/FmsOperationLog.php |
| FmsFileShare | app/Model/TchipBiFms/FmsFileShare.php |





## 5. 服务层文件（Service）与方法

### DirectoryService

* 路径：`app/Core/Service/TchipBiFms/DirectoryService.php`

| 方法                  | 签名示意                                                                                                                                                                         |
| :------------------ | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| createDirectory     | `createDirectory(int $parentId=null, int $ownerId, string $ownerType, string $name, string $visibility='public'): array`                                                     |
| renameDirectory     | `renameDirectory(int $id, string $name): bool`                                                                                                                               |
| moveDirectory       | `moveDirectory(int $id, int $newParentId): bool`                                                                                                                             |
| updateVisibility    | `updateVisibility(int $id, string $visibility): bool`                                                                                                                        |
| listChildren        | `listChildren(int $parentId=null, array $filter=[], array $op=[], string $sort='id', string $order='DESC', int $limit=10, string $search='', array $searchFields=[]): array` |
| getTree             | `getTree(): array`                                                                                                                                                           |
| softDeleteDirectory | `softDeleteDirectory(int $id, int $deletedBy): bool`                                                                                                                         |
| restoreDirectory    | `restoreDirectory(int $id, int $restoredBy): bool`                                                                                                                           |
| updateSortOrder     | `updateSortOrder(array $idToOrder): bool`                                                                                                                                    |

---

### FileService

* 路径：`app/Core/Service/TchipBiFms/FileService.php`

| 方法               | 签名示意                                                                                                                                                                    |
| :--------------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| uploadFile       | `uploadFile(int $directoryId, string $name, string $path, int $size, string $mimeType, int $createdBy, string $visibility='public'): array`                             |
| downloadFile     | `downloadFile(int $id): array`                                                                                                                                          |
| updateFileMeta   | `updateFileMeta(int $id, array $changes): bool`                                                                                                                         |
| listFiles        | `listFiles(int $directoryId, array $filter=[], array $op=[], string $sort='id', string $order='DESC', int $limit=10, string $search='', array $searchFields=[]): array` |
| softDeleteFile   | `softDeleteFile(int $id, int $deletedBy): bool`                                                                                                                         |
| restoreFile      | `restoreFile(int $id, int $restoredBy): bool`                                                                                                                           |
| listVersions     | `listVersions(int $id): array`                                                                                                                                          |
| revertVersion    | `revertVersion(int $id, int $version, int $userId): bool`                                                                                                               |
| updateVisibility | `updateVisibility(int $id, string $visibility): bool`                                                                                                                   |

---

### TagService

* 路径：`app/Core/Service/TchipBiFms/TagService.php`

| 方法                | 签名示意                                                                                                                                                 |
| :---------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------- |
| createTag         | `createTag(string $name, int $userId): array`                                                                                                        |
| updateTag         | `updateTag(int $id, string $name, int $userId): bool`                                                                                                |
| deleteTag         | `deleteTag(int $id, int $userId): bool`                                                                                                              |
| listTags          | `listTags(array $filter=[], array $op=[], string $sort='id', string $order='DESC', int $limit=10, string $search='', array $searchFields=[]): array` |
| assignTagsToFile  | `assignTagsToFile(int $fileId, array $tagIds, int $userId): bool`                                                                                    |
| removeTagFromFile | `removeTagFromFile(int $fileId, int $tagId, int $userId): bool`                                                                                      |
| listTagsByFile    | `listTagsByFile(int $fileId): array`                                                                                                                 |

---

### AclService

* 路径：`app/Core/Service/TchipBiFms/AclService.php`

| 方法                          | 签名示意                                                                                                                        |
| :-------------------------- | :-------------------------------------------------------------------------------------------------------------------------- |
| listRules                   | `listRules(string $targetType, int $targetId): array`                                                                       |
| createRule                  | `createRule(string $targetType, int $targetId, int $subjectId, int $permissionSet, string $effect, int $priority=0): array` |
| deleteRule                  | `deleteRule(int $ruleId): bool`                                                                                             |
| addCondition                | `addCondition(int $ruleId, string $conditionType, string $conditionValue): array`                                           |
| removeCondition             | `removeCondition(int $conditionId): bool`                                                                                   |
| checkPermission             | `checkPermission(int $userId, string $targetType, int $targetId, int $permissionBit): bool`                                 |
| computeEffectivePermissions | `computeEffectivePermissions(int $userId, string $targetType, int $targetId): int`                                          |
| getSubjectsForUser          | `getSubjectsForUser(int $userId): array`                                                                                    |
| evaluateConditions          | `evaluateConditions(array $conditions, int $fileId=null): bool`                                                             |

---

### ShareService

* 路径：`app/Core/Service/TchipBiFms/ShareService.php`

| 方法               | 签名示意                                                                                                                   |
| :--------------- | :--------------------------------------------------------------------------------------------------------------------- |
| createShare      | `createShare(int $fileId, int $createdBy, bool $isPublic=true, ?string $password=null, ?string $expireAt=null): array` |
| revokeShare      | `revokeShare(int $shareId, int $userId): bool`                                                                         |
| getShareByToken  | `getShareByToken(string $token): array`                                                                                |
| validatePassword | `validatePassword(string $token, ?string $password): bool`                                                             |
| recordVisit      | `recordVisit(int $shareId): void`                                                                                      |

---

### RecycleService

* 路径：`app/Core/Service/TchipBiFms/RecycleService.php`

| 方法          | 签名示意                                                                                                                                                            |
| :---------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| listDeleted | `listDeleted(array $filter=[], array $op=[], string $sort='deleted_at', string $order='DESC', int $limit=10, string $search='', array $searchFields=[]): array` |
| restore     | `restore(string $targetType, int $targetId, int $userId): bool`                                                                                                 |
| purge       | `purge(string $targetType, int $targetId, int $userId): bool`                                                                                                   |

---

### OperationLogService

* 路径：`app/Core/Service/TchipBiFms/OperationLogService.php`

| 方法       | 签名示意                                                                                                                                                         |
| :------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------- |
| log      | `log(int $userId, string $action, string $targetType, int $targetId, array $detail=[]): void`                                                                |
| listLogs | `listLogs(array $filter=[], array $op=[], string $sort='created_at', string $order='DESC', int $limit=10, string $search='', array $searchFields=[]): array` |
| getLog   | `getLog(int $id): array`                                                                                                                                     |

---
说明：Service 层统一继承 BusinessService，在列表方法中使用 `$this->buildparams($filter, $op, $sort, $order, $limit, $query)`

---


## 6. 控制器文件（Controller）与接口

### DirectoryController
- 路径：`app/Controller/TchipBiFms/DirectoryController.php`

| 方法 | HTTP | Path |
|:-|:-|:-|
| index | GET | /api/fms/directories |
| tree | GET | /api/fms/directories/tree |
| store | POST | /api/fms/directories |
| rename | PUT | /api/fms/directories/{id:[0-9]+}/rename |
| move | PUT | /api/fms/directories/{id:[0-9]+}/move |
| updateVisibility | PUT | /api/fms/directories/{id:[0-9]+}/visibility |
| destroy | DELETE | /api/fms/directories/{id:[0-9]+} |
| restore | POST | /api/fms/directories/{id:[0-9]+}/restore |

### FileController
- 路径：`app/Controller/TchipBiFms/FileController.php`

| 方法 | HTTP | Path |
|:-|:-|:-|
| index | GET | /api/fms/files?directory_id=... |
| upload | POST | /api/fms/files |
| download | GET | /api/fms/files/{id:[0-9]+}/download |
| update | PUT | /api/fms/files/{id:[0-9]+} |
| destroy | DELETE | /api/fms/files/{id:[0-9]+} |
| restore | POST | /api/fms/files/{id:[0-9]+}/restore |
| versions | GET | /api/fms/files/{id:[0-9]+}/versions |
| revertVersion | POST | /api/fms/files/{id:[0-9]+}/versions/{version:[0-9]+}/revert |
| updateVisibility | PUT | /api/fms/files/{id:[0-9]+}/visibility |

### TagController
- 路径：`app/Controller/TchipBiFms/TagController.php`

| 方法 | HTTP | Path |
|:-|:-|:-|
| index | GET | /api/fms/tags |
| store | POST | /api/fms/tags |
| update | PUT | /api/fms/tags/{id:[0-9]+} |
| destroy | DELETE | /api/fms/tags/{id:[0-9]+} |
| assign | POST | /api/fms/tags/assign |
| unassign | POST | /api/fms/tags/unassign |
| listByFile | GET | /api/fms/files/{id:[0-9]+}/tags |

### AclController
- 路径：`app/Controller/TchipBiFms/AclController.php`

| 方法 | HTTP | Path |
|:-|:-|:-|
| listRules | GET | /api/fms/acl/rules?target_type=&target_id= |
| createRule | POST | /api/fms/acl/rules |
| deleteRule | DELETE | /api/fms/acl/rules/{id:[0-9]+} |
| addCondition | POST | /api/fms/acl/rules/{id:[0-9]+}/conditions |
| removeCondition | DELETE | /api/fms/acl/conditions/{id:[0-9]+} |
| check | POST | /api/fms/acl/check |
| compute | POST | /api/fms/acl/compute |

### ShareController
- 路径：`app/Controller/TchipBiFms/ShareController.php`

| 方法 | HTTP | Path |
|:-|:-|:-|
| create | POST | /api/fms/shares |
| revoke | DELETE | /api/fms/shares/{id:[0-9]+} |
| showByToken | GET | /api/fms/shares/{token:[A-Za-z0-9_\-]{8,64}} |
| validate | POST | /api/public/shares/{token:[A-Za-z0-9_\-]{8,64}}/verify |
| downloadByToken | GET | /api/public/shares/{token:[A-Za-z0-9_\-]{8,64}}/download |

### RecycleController
- 路径：`app/Controller/TchipBiFms/RecycleController.php`

| 方法 | HTTP | Path |
|:-|:-|:-|
| index | GET | /api/fms/recycle-bin |
| restore | POST | /api/fms/recycle-bin/{id:[0-9]+}/restore |
| purge | DELETE | /api/fms/recycle-bin/{id:[0-9]+} |

### OperationLogController
- 路径：`app/Controller/TchipBiFms/OperationLogController.php`

| 方法 | HTTP | Path |
|:-|:-|:-|
| index | GET | /api/fms/logs |
| show | GET | /api/fms/logs/{id:[0-9]+} |

要求：Controller 仅负责参数获取与校验（使用 `BaseController::getParams()`）、调用 Service、返回统一结构。

## 7. API 约定与权限位

- 权限位（示例）：
  - 1=view, 2=upload, 4=delete, 8=share（可扩展）
- 头部/鉴权：所有需要鉴权的接口通过统一中间件校验用户身份与 ACL。
- 分页查询：参数统一（filter、op、sort、order、limit、search、searchFields），由 Service 的 `buildparams` 处理。

## 8. 开发注意事项

- PHP 7.4：严格类型与注解风格（如 @Listener）
- Carbon::now() 替代 now()
- 路由变量加正则范围，避免 shadowed by 冲突
- 软删通过 `fms_recycle_bin` 统一恢复/清理
- 操作日志：上传/下载/删除/恢复/创建目录/重命名等关键操作全部记录

---

© 2025 tchipbi FMS 后端骨架 | 作者：qinsx


