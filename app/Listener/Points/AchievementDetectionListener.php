<?php

declare(strict_types=1);

namespace App\Listener\Points;

use App\Core\Services\Points\AchievementService;
use App\Event\Points\PointsChangedEvent;
use App\Event\Points\AchievementUnlockedEvent;
use App\Event\Wiki\WikiDocumentCreatedEvent;
use App\Event\Wiki\WikiDocumentDeletedEvent;
use App\Event\Wiki\WikiDocumentLikedEvent;
use App\Event\Wiki\WikiDocumentPinnedEvent;
use App\Event\Wiki\WikiDocumentViewedEvent;
use App\Event\Wiki\WikiDocumentViewMilestoneEvent;
use Hyperf\Event\Annotation\Listener;
use Hyperf\Event\Contract\ListenerInterface;
use Hyperf\Logger\LoggerFactory;
use Psr\Container\ContainerInterface;
use Psr\Log\LoggerInterface;
use Psr\EventDispatcher\EventDispatcherInterface;

/**
 * 成就检测事件监听器
 * 自动检测用户行为并触发成就达成
 * @Listener
 */
class AchievementDetectionListener implements ListenerInterface
{
    private AchievementService $achievementService;
    private LoggerInterface $logger;

    public function __construct(ContainerInterface $container)
    {
        $this->achievementService = $container->get(AchievementService::class);
        $this->logger = $container->get(LoggerFactory::class)->get('achievement');
    }

    public function listen(): array
    {
        return [
            PointsChangedEvent::class,
            WikiDocumentCreatedEvent::class,
            WikiDocumentDeletedEvent::class,
            WikiDocumentLikedEvent::class,
            WikiDocumentPinnedEvent::class,
            WikiDocumentViewedEvent::class,
            WikiDocumentViewMilestoneEvent::class,
        ];
    }

    public function process(object $event): void
    {
        try {
            switch (get_class($event)) {
                case PointsChangedEvent::class:
                    $this->handlePointsChanged($event);
                    break;
                case WikiDocumentCreatedEvent::class:
                    $this->handleDocumentCreated($event);
                    break;
                case WikiDocumentDeletedEvent::class:
                    $this->handleDocumentDeleted($event);
                    break;
                case WikiDocumentLikedEvent::class:
                    $this->handleDocumentLiked($event);
                    break;
                case WikiDocumentPinnedEvent::class:
                    $this->handleDocumentPinned($event);
                    break;
                case WikiDocumentViewedEvent::class:
                    $this->handleDocumentViewed($event);
                    break;
                case WikiDocumentViewMilestoneEvent::class:
                    $this->handleDocumentViewMilestone($event);
                    break;
            }
        } catch (\Exception $e) {
            $this->logger->info('成就检测事件处理失败', [
                'event_class' => get_class($event),
                'event_data' => $event,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 处理积分变化事件
     */
    private function handlePointsChanged(PointsChangedEvent $event): void
    {
        // 检查所有与积分相关的成就
        $this->checkAchievementsByType($event->userId, [
            'total_points',       // 总积分成就
            'daily_points',       // 日积分成就
            'weekly_points',      // 周积分成就
            'monthly_points'      // 月积分成就
        ]);

        $this->logger->info('积分变化成就检测完成', [
            'user_id' => $event->userId,
            'action_type' => $event->actionType,
            'point_change' => $event->pointChange
        ]);
    }

    /**
     * 处理文档创建事件
     */
    private function handleDocumentCreated(WikiDocumentCreatedEvent $event): void
    {
        // 检查文档数量相关成就 - 这些成就直接依赖文档数量而非积分
        $this->checkAchievementsByType($event->userId, [
            'article_count',      // 文档发布数量成就 (萤火之光、知识分子等)
        ]);

        $this->logger->info('文档创建成就检测完成', [
            'user_id' => $event->userId,
            'document_id' => $event->documentId,
            'title' => $event->title
        ]);
    }

    /**
     * 处理文档删除事件
     */
    private function handleDocumentDeleted(WikiDocumentDeletedEvent $event): void
    {
        // 当文档被删除时，需要重新检查文档数量相关的成就
        // 因为用户的文档数量减少了，可能影响已获得的成就
        $this->checkAchievementsByType($event->authorId, [
            'article_count',      // 重新检查文档数量成就
            'essence_count',      // 如果删除的是精华文档，需要重新检查
            'total_views'         // 删除文档会影响总阅读量
        ]);

        $this->logger->info('文档删除成就重新检测完成', [
            'author_id' => $event->authorId,
            'document_id' => $event->documentId,
            'title' => $event->title,
            'operator_id' => $event->operatorId
        ]);
    }

    /**
     * 处理文档点赞事件
     */
    private function handleDocumentLiked(WikiDocumentLikedEvent $event): void
    {
        if ($event->isLiked) {
            // 只在点赞时检查成就（取消点赞不触发）
            $this->checkAchievementsByType($event->authorId, [
                'like_count',         // 获得点赞数成就
                'popularity',         // 人气成就
                'community_favorite'  // 社区喜爱成就
            ]);

            $this->logger->info('文档点赞成就检测完成', [
                'author_id' => $event->authorId,
                'liker_id' => $event->likerId,
                'document_id' => $event->documentId,
                'title' => $event->title
            ]);
        }
    }

    /**
     * 处理文档置顶事件
     */
    private function handleDocumentPinned(WikiDocumentPinnedEvent $event): void
    {
        // 无论是置顶还是取消置顶，都需要重新检查精华文档相关成就
        // 因为这直接影响 essence_count 的数量
        $this->checkAchievementsByType($event->authorId, [
            'essence_count',      // 精华文档成就 (精华达人等)
        ]);

        $this->logger->info('文档置顶状态变更成就检测完成', [
            'author_id' => $event->authorId,
            'operator_id' => $event->operatorId,
            'document_id' => $event->documentId,
            'title' => $event->title,
            'is_pinned' => $event->isPinned
        ]);
    }

    /**
     * 按类型检查成就
     */
    private function checkAchievementsByType(int $userId, array $conditionTypes): void
    {
        foreach ($conditionTypes as $conditionType) {
            try {
                $results = $this->achievementService->checkUserAchievementsByCondition($userId, $conditionType);
                
                foreach ($results as $result) {
                    if ($result['newly_unlocked']) {
                        // 触发成就解锁事件
                        make(\Psr\EventDispatcher\EventDispatcherInterface::class)->dispatch(
                            new AchievementUnlockedEvent(
                                $userId,
                                $result['achievement_id'],
                                $result['achievement_code'],
                                $result['achievement_name'],
                                $result['reward_points'],
                                $result['unlocked_at'],
                                $result
                            )
                        );

                        $this->logger->info('用户解锁新成就', [
                            'user_id' => $userId,
                            'achievement_code' => $result['achievement_code'],
                            'achievement_name' => $result['achievement_name'],
                            'reward_points' => $result['reward_points']
                        ]);
                    }
                }
            } catch (\Exception $e) {
                $this->logger->info('成就检测失败', [
                    'user_id' => $userId,
                    'condition_type' => $conditionType,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * 处理文档阅读事件
     */
    private function handleDocumentViewed(WikiDocumentViewedEvent $event): void
    {
        // 单个阅读的事件暂不做处理

        // // 检查阅读相关成就
        // $this->checkAchievementsByType($event->viewerId, [
        //     'total_views',      // 阅读数量成就
        // ]);

        // $this->logger->info('文档阅读成就检测完成', [
        //     'viewer_id' => $event->viewerId,
        //     'document_id' => $event->documentId,
        //     'title' => $event->title,
        //     'current_view_count' => $event->currentViewCount
        // ]);
    }

    /**
     * 处理文档阅读里程碑事件
     */
    private function handleDocumentViewMilestone(WikiDocumentViewMilestoneEvent $event): void
    {
        // 检查文档作者的阅读量里程碑成就
        $this->checkAchievementsByType($event->authorId, [
            'total_views',     // 阅读量里程碑成就
        ]);

        $this->logger->info('文档阅读里程碑成就检测完成', [
            'author_id' => $event->authorId,
            'document_id' => $event->documentId,
            'title' => $event->title,
            'milestone_value' => $event->milestoneValue,
            'current_view_count' => $event->currentViewCount
        ]);
    }
}