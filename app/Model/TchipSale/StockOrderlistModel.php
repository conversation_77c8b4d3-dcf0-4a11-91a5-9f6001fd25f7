<?php

declare (strict_types=1);

namespace App\Model\TchipSale;

use App\Model\TchipSale\ClientTableModel;
use App\Model\TchipSale\ClientContactTableModel;

/**
 * @property int $id
 * @property string $sn
 * @property int $user_id
 * @property int $client_id
 * @property int $prod_id
 * @property int $platform_id
 * @property int $size_id
 * @property int $sale_id
 * @property int $num
 * @property string $price
 * @property string $curr
 * @property string $paytype
 * @property string $money
 * @property string $saledate
 * @property string $freight
 * @property string $ostype
 * @property string $notes
 * @property int $plan_status
 * @property string $plan_time
 * @property string $ship_time
 * @property \Carbon\Carbon $create_time
 * @property \Carbon\Carbon $update_time
 * @property int $delete_time
 * @property int $status
 * @property int $notice_uid
 * @property int $is_mail
 * @property string $rate
 * @property string $ratemoney
 * @property int $order_id
 * @property int $cargo_status
 * @property int $warehouse_check
 * @property string $stock_company
 * @property string $details_notes
 */
class StockOrderlistModel extends \App\Model\TchipSale\SaleBaseModel
{

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'stock_orderlist';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'sn',
        'user_id',
        'client_id',
        'prod_id',
        'platform_id',
        'size_id',
        'sale_id',
        'num',
        'price',
        'curr',
        'paytype',
        'money',
        'saledate',
        'freight',
        'ostype',
        'notes',
        'plan_status',
        'plan_time',
        'ship_time',
        'create_time',
        'update_time',
        'delete_time',
        'status',
        'notice_uid',
        'is_mail',
        'rate',
        'ratemoney',
        'order_id',
        'cargo_status',
        'warehouse_check',
        'stock_company',
        'details_notes'
    ];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id'              => 'integer',
        'user_id'         => 'integer',
        'client_id'       => 'integer',
        'prod_id'         => 'integer',
        'platform_id'     => 'integer',
        'size_id'         => 'integer',
        'sale_id'         => 'integer',
        'num'             => 'integer',
        'plan_status'     => 'integer',
        'create_time'     => 'datetime',
        'update_time'     => 'datetime',
        'delete_time'     => 'integer',
        'status'          => 'integer',
        'notice_uid'      => 'integer',
        'is_mail'         => 'integer',
        'order_id'        => 'integer',
        'cargo_status'    => 'integer',
        'warehouse_check' => 'integer'
    ];

    /**
     * 关联客户表
     */
    public function client()
    {
        return $this->belongsTo(ClientTableModel::class, 'client_id', 'id');
    }

    /**
     * 关联客户联系信息表
     */
    public function clientContact()
    {
        return $this->belongsTo(ClientContactTableModel::class, 'client_id', 'client_id');
    }
}