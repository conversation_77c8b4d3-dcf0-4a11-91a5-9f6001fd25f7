<?php

declare (strict_types=1);
namespace App\Model\TchipSale;

/**
 * @property int $id 
 * @property string $username 
 * @property string $realname 
 * @property string $password 
 * @property string $email 
 * @property string $status 
 * @property int $access 
 * @property int $login_count 
 * @property int $last_visit 
 * @property int $date_created 
 * @property string $report 
 * @property string $MailPwd 
 * @property string $smtp 
 * @property int $ssl 
 * @property int $port 
 * @property int $out_stock_notice 
 * @property int $stock_notice 
 */
class UserTableModel extends \App\Model\TchipSale\SaleBaseModel
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user_table';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'username',
        'realname',
        'password',
        'email',
        'status',
        'access',
        'login_count',
        'last_visit',
        'date_created',
        'report',
        'MailPwd',
        'smtp',
        'ssl',
        'port',
        'out_stock_notice',
        'stock_notice',
        'sort'
    ];

    /**
     * 禁用时间戳自动管理（该表没有created_at/updated_at字段）
     */
    public $timestamps = false;

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'access' => 'integer',
        'login_count' => 'integer',
        'last_visit' => 'integer',
        'date_created' => 'integer',
        'ssl' => 'integer',
        'port' => 'integer',
        'out_stock_notice' => 'integer',
        'stock_notice' => 'integer',
        'sort' => 'integer'
    ];


}