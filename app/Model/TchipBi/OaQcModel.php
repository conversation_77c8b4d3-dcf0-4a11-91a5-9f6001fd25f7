<?php

declare (strict_types=1);
namespace App\Model\TchipBi;

use App\Constants\OaQcErpCode;
use Hyperf\Database\Model\SoftDeletes;
/**
 * @property int $id 
 * @property int $type 
 * @property string $order_no 
 * @property string $enter_no 
 * @property string $prod_code 
 * @property string $prod_name 
 * @property string $prod_spec 
 * @property int $num 
 * @property int $examine_num 
 * @property int $status 
 * @property string $examine_remark 
 * @property string $result_remark 
 * @property \Carbon\Carbon $created_at 
 * @property \Carbon\Carbon $updated_at 
 * @property string $deleted_at 
 */
class OaQcModel extends \App\Model\Model
{
    use SoftDeletes;
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'oa_qc';
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['type', 'order_no', 'enter_no', 'prod_code', 'prod_name', 'prod_spec',
        'prod_loc', 'num', 'examine_num', 'status', 'handle', 'examine_remark', 'result_remark',
        'examine_option', 'appearance_examine_user', 'function_examine_user', 'erp_date',
        'examine_user', 'prod_pmpc', 'attribution', 'batch', 'factory_name', 'factory_identifier',
        'silk', 'pmpc_category', 'check_date', 'image', 'defective_num', 'qc_type', 'line','real_examine_num', 'is_first',
        'from_no','status_change_at', 'unqualified_handle', 'inspection_flunk_count', 'prod_loc_old','sampling_plan',
        'appearance_inspection_remark','function_inspection_remark','attachments','followers', 'defect_categories', 'order_factory_code'
    ];
    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = ['id' => 'integer', 'type' => 'integer', 'num' => 'integer',
                        'examine_num' => 'integer', 'examine_option' => 'json',
                        'appearance_examine_user' => 'integer', 'function_examine_user' => 'integer',
                        'status' => 'integer', 'created_at' => 'datetime',
                        'updated_at' => 'datetime', 'image' => 'json', 'handle' => 'integer','followers' => 'json',
                        'sampling_plan' => 'string', 'defect_categories' => 'json', 'attachments' => 'json', 'order_factory_code' => 'string',
    ];

    protected $appends = ['status_text', 'handle_text', 'created_day', 'pmpc_category_text'];

    public $pmpcCategoryList = [
        'tpic'   => [
            'name' => '贴片IC类',
            'value' => 'tpic',
        ],
        'pcb'    => [
            'name' => 'PCB类',
            'value'  => 'pcb',
        ],
        'common' => [
            'name' => '通用类',
            'value' => 'common',
        ]
    ];

    public function getStatusTextAttribute()
    {
        $value = '';
        if (isset($this->attributes['status'])) {
            $statusList = OaQcErpCode::STATUS_LIST;
            $statusList = array_column($statusList, null, 'value');
            if (!empty($statusList[$this->attributes['status']]['label'])) {
                $value = $statusList[$this->attributes['status']]['label'];
            }
        }
        return $value;
    }

    public function getHandleTextAttribute()
    {
        $value = '';
        if (!empty($this->attributes['handle'])) {
            $hanldeList = OaQcErpCode::HANLDE_LIST;
            $hanldeList = array_column($hanldeList, null, 'value');
            if (!empty($hanldeList[$this->attributes['handle']]['label'])) {
                $value = $hanldeList[$this->attributes['handle']]['label'];
            }
        }
        return $value;
    }

    public function getCreatedDayAttribute()
    {
        $value = '';
        if (!empty($this->attributes['created_at'])) {
            $value = date('Y-m-d', strtotime($this->attributes['created_at']));
        }
        return $value;
    }

    public function getPmpcCategoryTextAttribute()
    {
        $value = '';
        if (!empty($this->attributes['pmpc_category'])) {
            $value = $this->pmpcCategoryList[$this->attributes['pmpc_category']]['name'] ?? '';
        }
        return $value;
    }

    public function locationByProdLoc()
    {
        return $this->hasOne('\App\Model\TchipBi\ErpWarehouseModel', 'code','prod_loc');
    }

    public function erpSilk()
    {
        return $this->hasOne('\App\Model\TchipBi\ErpProductModel', 'prod_code','prod_code');
    }

    /**
     * 关联特采订单 - 一个QC记录可能对应多个特采订单
     * 通过当前QC记录的ID关联特采订单表的qc_order_id字段
     */
    public function specialOrders()
    {
        return $this->hasMany('\App\Model\TchipBi\OaQcSpecialOrderModel', 'qc_order_id', 'id');
    }

    /**
     * 获取特采订单ID数组的访问器
     * 使用语法糖自动获取关联的特采订单ID
     */
    public function getSpecialOrderIdsAttribute()
    {
        return $this->specialOrders->pluck('id')->toArray();
    }

}