<?php

namespace App\Model\TchipBi;

use Hyperf\DbConnection\Model\Model;
use Hyperf\Database\Model\Relations\BelongsTo;
use Hyperf\Database\Model\Relations\HasMany;
use Hyperf\Database\Model\Relations\HasOne;
use App\Constants\OaQcSpecialOrderCode;
use App\Model\TchipBi\UserModel;

class OaQcSpecialOrderModel extends Model
{
    protected $table = 'oa_qc_special_order';
    protected $primaryKey = 'id';
    protected $keyType = 'int';
    public $timestamps = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'order_no',
        'qc_order_id', // 关联QC记录，通过此字段获取产品信息
        'special_reason',        // 特采原因
        'special_scope',         // 特采范围
        'special_details',       // 特采详情
        'status',
        'current_step',
        'current_approver_id',
        'current_approver_name',
        'final_remark',
        'creator_id',
        'creator_name',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id' => 'integer',
        'qc_order_id' => 'integer',
        // 移除冗余字段的类型转换：defective_num, total_num, defective_rate
        'current_step' => 'integer',
        'current_approver_id' => 'integer',
        'creator_id' => 'integer',
        'expected_completion_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 自动追加到JSON的访问器属性
     */
    protected $appends = [
        'defective_rate'
    ];


    /**
     * 关联QC订单
     */
    public function qcOrder(): BelongsTo
    {
        return $this->belongsTo(OaQcModel::class, 'qc_order_id', 'id');
    }

    /**
     * 关联当前审批人用户信息
     */
    public function currentApprover(): BelongsTo
    {
        return $this->belongsTo(UserModel::class, 'current_approver_id', 'id');
    }

    /**
     * 关联审批记录
     */
    public function approvals(): HasMany
    {
        return $this->hasMany(OaQcSpecialOrderApprovalModel::class, 'order_id', 'id')
            ->orderBy('step', 'asc')
            ->orderBy('created_at', 'asc');
    }

    /**
     * 获取最新审批记录
     */
    public function latestApproval(): HasOne
    {
        return $this->hasOne(OaQcSpecialOrderApprovalModel::class, 'order_id', 'id')
            ->latest('created_at');
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute(): string
    {
        return OaQcSpecialOrderCode::getStatusText($this->status);
    }

    /**
     * 获取当前步骤文本
     */
    public function getCurrentStepTextAttribute(): string
    {
        return OaQcSpecialOrderCode::getStepText($this->current_step);
    }

    /**
     * 生成特采订单号
     */
    public static function generateOrderNo(): string
    {
        $prefix = 'TC';
        $date = date('Ymd');
        $uuid = strtoupper(substr(str_replace('-', '', uniqid()), -6));

        return $prefix . $date . $uuid;
    }

    /**
     * 检查是否可以审批
     */
    public function canApprove(int $userId): bool
    {
        return $this->current_approver_id === $userId &&
               in_array($this->status, ['pending', 'step1', 'step2', 'step3', 'step4']);
    }

    /**
     * 获取下一个审批步骤
     */
    public function getNextStep(): int
    {
        return min($this->current_step + 1, 5);
    }

    /**
     * 检查是否已完成审批
     */
    public function isCompleted(): bool
    {
        return in_array($this->status, ['approved', 'rejected', 'cancelled']);
    }

    // 移除了多余的访问器方法，因为已经通过 with(['qcOrder']) 预加载了关联数据
    // 前端可以直接通过 order.qc_order.prod_name 等方式访问

    /**
     * 获取不合格率（动态计算）
     * 保留此访问器因为需要动态计算
     */
    public function getDefectiveRateAttribute(): float
    {
        // 确保关联数据已加载
        if (!$this->relationLoaded('qcOrder') || !$this->qcOrder) {
            return 0;
        }

        $total = (int)($this->qcOrder->examine_num ?? 0);
        $defective = (int)($this->qcOrder->defective_num ?? 0);

        if ($total <= 0) {
            return 0;
        }

        return round(($defective / $total) * 100, 2);
    }
}