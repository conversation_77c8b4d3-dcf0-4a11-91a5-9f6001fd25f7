<?php

declare (strict_types=1);

namespace App\Model\TchipBi;

use Hyperf\Database\Model\SoftDeletes;
use Qbhy\HyperfAuth\AuthAbility;
use Qbhy\HyperfAuth\Authenticatable;
use Hyperf\Cache\Annotation\Cacheable;
use App\Annotation\ViewCacheAnnotation;
/**
 * @property int $id
 * @property string $workwx_userid 成员UserID
 * @property string $name
 * @property int $mobile
 * @property string $password
 * @property string $salt
 * @property string $department
 * @property string $order 部门内的排序值，默认为0
 * @property string $position
 * @property int $gender 性别:0表示未定义，1表示男性，2表示女性
 * @property string $email
 * @property string $biz_mail
 * @property string $is_leader_in_dept
 * @property string $direct_leader
 * @property string $avatar
 * @property string $thumb_avatar
 * @property string $telephone
 * @property string $alias
 * @property string $extattr
 * @property int $status
 * @property string $qr_code
 * @property \Carbon\Carbon birthday_custom
 * @property string $sex
 * @property string $contact
 * @property string $external_profile
 * @property string $external_position
 * @property string $address
 * @property string $open_userid
 * @property string $main_department
 * @property int $do_report
 * @property int $oa_report_open
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property string $deleted_at
 */
class UserModel extends \App\Model\Model implements Authenticatable
{
    use SoftDeletes, AuthAbility;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'user';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'workwx_userid', 'name', 'mobile', 'password', 'salt', 'department', 'order', 'position', 'gender', 'email', 'biz_mail',
        'is_leader_in_dept', 'direct_leader', 'avatar', 'thumb_avatar', 'telephone', 'alias', 'extattr', 'status',
        'qr_code', 'external_profile', 'external_position', 'address', 'open_userid', 'main_department', 'group_access_ids', 'do_report',
        'index_quota', 'index_fast', 'description', 'sync_work_wechat', 'product_detail_opening_mode', 'product_detail_label_page', 'oa_report_open', 'user_settings',
        'contact', 'sex', 'birthday_custom', 'bi_status'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'id'                => 'integer',
        'mobile'            => 'integer',
        'department'        => 'json',
        'order'             => 'array',
        'is_leader_in_dept' => 'json',
        'direct_leader'     => 'array',
        'extattr'           => 'array',
        'external_profile'  => 'array',
        'gender'            => 'integer',
        'status'            => 'integer',
        'created_at'        => 'datetime',
        'updated_at'        => 'datetime',
        'group_access_ids'  => 'json',
        'do_report'         => 'integer',
        'index_quota'       => 'json',
        'index_fast'        => 'json',
        'birthday_custom'   => 'datetime',
        'bi_status'         => 'integer'
    ];

    protected $hidden = ['password', 'salt', 'hashed_password'];

    protected $appends = ['status_text', 'department_text'];

    // 默认密码
    public $defaultPassword = '123456';

    public function userThird()
    {
        return $this->hasMany('\App\Model\TchipBi\UserThirdModel', 'user_id', 'id');
    }

    // 只获得一条，建议附加条件platform=xxx
    public function third()
    {
        return $this->hasOne('\App\Model\TchipBi\UserThirdModel', 'user_id', 'id');
    }

    public function groupAccess()
    {
        return $this->hasOne('\App\Model\TchipBi\AuthGroupAccessModel', 'user_id', 'id');
    }

    public function groupsAccess()
    {
        return $this->hasMany('\App\Model\TchipBi\AuthGroupAccessModel', 'user_id', 'id');
    }

    public function userDepartMentBind()
    {
        return $this->hasMany('\App\Model\TchipBi\UserDepartmentBindModel', 'user_id', 'id');
    }

    public function setGroupAccessIdsAttribute($value)
    {
        $result=[];
        if (!is_array($value)) {
            $value = json_decode($value, true);
        }
        foreach ($value as $item){
            $result[]=intval($item);
        }
        $this->attributes['group_access_ids'] = json_encode($result);
    }

    public function getStatusTextAttribute()
    {
        $value = '未知状态';
        if (isset($this->attributes['status'])) {
            $status = (int) $this->attributes['status'];
            switch ($status) {
                case 1:
                    $value = '激活';
                    break;
                case 2:
                    $value = '禁用';
                    break;
                case 4:
                    $value = '未激活';
                    break;
                case 5:
                    $value = '退企';
                    break;
                default:
                    $value = '未知';
            }
        }
        return $value;
    }

    public function getDepartmentTextAttribute()
    {
        $value = null;
        if (!empty($this->attributes['department'])) {
            $value = !is_array($this->attributes['department']) ? json_decode($this->attributes['department'], true) : $this->attributes['department'];
            if ($value && is_array($value)) {
                $arr = '';
//                $value = UserDepartmentModel::query()->whereIn('id', $value)->pluck('name')->toArray();
//                $value = $value ? implode(',', $value) : null;
                for ($i = 0; $i < count($value); $i++) {
                    $arr .= $this->departmentTextAttributeCache($value[$i]);
                }
                $value = $arr;
            }
        }
        return $value;
    }

    /**
     * @Cacheable (prefix="user_department_text_", ttl=10)
     */
    public function departmentTextAttributeCache($departmentIds)
    {
        $departments= UserDepartmentModel::query()->where('id', $departmentIds)->pluck('name')->toArray();
        return $departments ? implode(',', $departments) : null;

    }

    /**
     * 关联用户知识豆信息
     */
    public function userPoints()
    {
        return $this->hasOne(\App\Model\Points\UserPointsModel::class, 'user_id', 'id');
    }
}