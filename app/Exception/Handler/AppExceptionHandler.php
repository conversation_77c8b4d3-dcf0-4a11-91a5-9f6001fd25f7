<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
namespace App\Exception\Handler;

use App\Constants\StatusCode;
use App\Core\Utils\Log;
use App\Core\Utils\Response;
use App\Exception\AppException;
use Hyperf\Contract\StdoutLoggerInterface;
use Hyperf\Di\Annotation\Inject;
use Hyperf\ExceptionHandler\ExceptionHandler;
use Hyperf\HttpMessage\Stream\SwooleStream;
use Hyperf\Validation\ValidationException;
use Psr\Http\Message\ResponseInterface;
use Throwable;

class AppExceptionHandler extends ExceptionHandler
{
    /**
     * @var StdoutLoggerInterface
     */
    protected $logger;

    /**
     * @Inject()
     * @var Response
     */
    protected $response;

    public function __construct(StdoutLoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public function handle(Throwable $throwable, ResponseInterface $response)
    {
        // 异常信息处理
        $throwableMsg = sprintf('%s[%s] in %s', $throwable->getMessage(), $throwable->getLine(), $throwable->getFile()).PHP_EOL.$throwable->getTraceAsString();
        $throwable->getPrevious();

        // 判断是否由业务异常类抛出
        if($throwable instanceof AppException){
            $this->stopPropagation();

            // 业务逻辑错误日志处理
            $logData = getLogArguments($throwableMsg);
            $this->logger->info($logData['message'], $logData['context']);

            // 错误为 500 时修改状态码
            if($throwable->getCode() == 500){
                $statusCode = 500;
            }else{
                $statusCode = 200;
            }
            return $this->response->error($throwable->getMessage(), $throwable->getCode(), '', 'json', [], $statusCode);
        }

        // 判断是否由表单验证异常类抛出
        if($throwable instanceof ValidationException){
            $this->stopPropagation();

            // 业务逻辑错误日志处理
            $message = $throwable->validator->errors()->first();
            $logData = getLogArguments($message);
            $this->logger->info($logData['message'], $logData['context']);
            return $this->response->error($message, StatusCode::ERR_SERVER);
        }

        $this->logger->info(sprintf('%s[%s] in %s', $throwable->getMessage(), $throwable->getLine(), $throwable->getFile()));
        $this->logger->info($throwable->getTraceAsString());

        $contents = [
            'code' => StatusCode::ERR_SERVER,
            // 'msg' => $throwable->getMessage(),
            'msg' => 'Internal Server Error.',
            'time' => time(),
        ];
        return $response->withHeader('Server', 'Hyperf')->withStatus(200)->withBody(new SwooleStream(json_encode($contents)));
        //
        // $this->logger->info(sprintf('%s[%s] in %s', $throwable->getMessage(), $throwable->getLine(), $throwable->getFile()));
        // $this->logger->info($throwable->getTraceAsString());
        // return $response->withHeader('Server', 'Hyperf')->withStatus(500)->withBody(new SwooleStream('Internal Server Error.'));
    }

    public function isValid(Throwable $throwable): bool
    {
        return true;
    }
}
