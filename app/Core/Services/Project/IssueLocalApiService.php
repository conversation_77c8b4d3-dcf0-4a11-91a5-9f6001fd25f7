<?php
/**
 * Issue 本地 API 服务
 * 
 * @Copyright T-chip Team.
 * @Date 2025-07-02
 * <AUTHOR>
 * @Description 替代 Redmine API 的本地 Issue 管理服务
 */

namespace App\Core\Services\Project;

use App\Constants\StatusCode;
use App\Core\Services\BusinessService;
use App\Exception\AppException;
use App\Model\Redmine\IssueModel;
use App\Model\Redmine\IssuesExtModel;
use App\Model\Redmine\JournalsModel;
use App\Model\Redmine\JournalDetailsModel;
use App\Model\Redmine\CustomValuesModel;
use App\Model\Redmine\WatchersModel;
use App\Model\Redmine\IssueAssignedModel;
use App\Model\Redmine\AttachmentModel;
use App\Model\Redmine\IssueStatusModel;
use App\Model\Redmine\WorkflowsModel;
use App\Model\Redmine\CustomFieldsModel;
use App\Model\Redmine\UserModel;
use App\Model\Redmine\ProjectModel;
use App\Model\Redmine\TrackersModel;
use App\Model\Redmine\MemberModel;
use App\Model\Redmine\MemberRolesModel;
use App\Event\Redmine\IssueJournalsNoticeEvent;
use Hyperf\Di\Annotation\Inject;
use Hyperf\DbConnection\Db;
use Psr\EventDispatcher\EventDispatcherInterface;
use Carbon\Carbon;
use App\Core\Utils\Log;

class IssueLocalApiService extends BusinessService
{
    /**
     * @Inject()
     * @var IssueModel
     */
    protected $issueModel;

    /**
     * @Inject()
     * @var JournalsModel
     */
    protected $journalsModel;

    /**
     * @Inject()
     * @var EventDispatcherInterface
     */
    protected $eventDispatcher;

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取调整后的时间（往前移8小时）
     * 用于处理历史时区问题
     * 
     * @param string $format 时间格式
     * @return string
     */
    protected function getAdjustedTime(string $format = 'Y-m-d H:i:s'): string
    {
        return Carbon::now()->subHours(8)->format($format);
    }

    /**
     * 获取调整后的Carbon实例（往前移8小时）
     * 用于处理历史时区问题
     * 
     * @return Carbon
     */
    protected function getAdjustedCarbon(): Carbon
    {
        return Carbon::now()->subHours(8);
    }

    /**
     * 创建事项
     * 
     * @param array $params 事项参数
     * @return array
     * @throws AppException
     */
    public function createIssue(array $params): array
    {
        // 由于前端使用方面问题，事项描述特别处理
        $desctiptionHtml = null;
        if (!empty($params['description_html'])) {
            $desctiptionHtml = $params['description_html'];
            unset($params['description_html']);
        }


        // 数据验证
        $this->validateIssueData($params, true);
        
        // 开启事务
        return Db::connection('tchip_redmine')->transaction(function () use ($params, $desctiptionHtml) {
            // 处理默认值
            $issueData = $this->prepareIssueDataForCreate($params);
            
            // 创建事项
            $issue = $this->issueModel::create($issueData);

            // 事项描述特别处理
            if (!empty($desctiptionHtml)) {
                IssuesExtModel::create([
                    'issue_id' => $issue->id,
                    'description_html' => $desctiptionHtml
                ]);
            }
            
            // 如果是根节点，需要更新 root_id 为自己的 id
            if (!$issue->parent_id && $issueData['root_id'] === null) {
                $issue->update(['root_id' => $issue->id]);
            }
            
            // 处理关联数据
            $this->handleIssueRelatedData($issue, $params, true);
            
            // 创建初始Journal
            $this->createInitialJournal($issue);
            
            // 发送通知
            $this->sendCreateNotification($issue, $params);
            
            return [
                'issue' => [
                    'id' => $issue->id,
                    'subject' => $issue->subject
                ]
            ];
        });
    }

    /**
     * 更新事项
     * 
     * @param int $id 事项ID
     * @param array $params 更新参数
     * @return array
     * @throws AppException
     */
    public function updateIssue(int $id, array $params): array
    {
        // 查找事项
        $issue = $this->findIssueById($id);
        
        // 数据验证
        $this->validateIssueData($params, false, $issue);
        
        // 开启事务
        return Db::connection('tchip_redmine')->transaction(function () use ($issue, $params) {
            // 记录变更前状态
            $oldAttributes = $issue->getAttributes();
            
            // 处理乐观锁
            $this->handleOptimisticLocking($issue, $params);
            
            // 准备更新数据
            $updateData = $this->prepareIssueDataForUpdate($params, $issue);
            
            // 更新事项
            $issue->update($updateData);
            
            // 维护嵌套集合字段（如果父节点发生变化）
            $this->maintainNestedSetAfterUpdate($issue, $oldAttributes);
            
            // 处理关联数据
            $this->handleIssueRelatedData($issue, $params, false);
            
            // 创建Journal记录
            $this->createJournalForUpdate($issue, $oldAttributes, $params);
            
            // 发送通知
            $this->sendUpdateNotification($issue, $oldAttributes, $params);
            
            return [
                'issue' => [
                    'id' => $issue->id,
                    'subject' => $issue->subject
                ]
            ];
        });
    }

    /**
     * 删除事项
     * 
     * @param int $id 事项ID
     * @return array
     * @throws AppException
     */
    public function deleteIssue(int $id): array
    {
        // 查找事项
        $issue = $this->findIssueById($id);
        
        // 权限验证
        $this->validateDeletePermission($issue);
        
        // 开启事务
        return Db::connection('tchip_redmine')->transaction(function () use ($issue) {
            // 处理级联删除
            $this->handleCascadeDelete($issue);
            
            // 从嵌套集合中移除节点
            $this->removeNodeFromNestedSet($issue);
            
            // 删除事项
            $issue->delete();
            
            return [
                'message' => '事项删除成功'
            ];
        });
    }

    /**
     * 添加关注人
     * 
     * @param int $issueId 事项ID
     * @param int $userId 用户ID
     * @return array
     * @throws AppException
     */
    public function addWatchers(int $issueId, int $userId): array
    {
        $issue = $this->findIssueById($issueId);
        
        // 检查是否已关注
        $exists = WatchersModel::query()
            ->where('watchable_type', 'Issue')
            ->where('watchable_id', $issueId)
            ->where('user_id', $userId)
            ->exists();
            
        if (!$exists) {
            WatchersModel::create([
                'watchable_type' => 'Issue',
                'watchable_id' => $issueId,
                'user_id' => $userId
            ]);
        }
        
        return ['message' => '关注成功'];
    }

    /**
     * 删除关注人
     * 
     * @param int $issueId 事项ID
     * @param int $userId 用户ID
     * @return array
     * @throws AppException
     */
    public function delWatchers(int $issueId, int $userId): array
    {
        $issue = $this->findIssueById($issueId);
        
        WatchersModel::query()
            ->where('watchable_type', 'Issue')
            ->where('watchable_id', $issueId)
            ->where('user_id', $userId)
            ->delete();
        
        return ['message' => '取消关注成功'];
    }

    /**
     * 根据ID查找事项
     * 
     * @param int $id
     * @return IssueModel
     * @throws AppException
     */
    protected function findIssueById(int $id): IssueModel
    {
        $issue = $this->issueModel::find($id);
        if (!$issue) {
            throw new AppException(StatusCode::ERR_SERVER, '事项不存在');
        }
        return $issue;
    }

    /**
     * 验证事项数据
     * 
     * @param array $params
     * @param bool $isCreate
     * @param IssueModel|null $issue
     * @throws AppException
     */
    protected function validateIssueData(array $params, bool $isCreate, ?IssueModel $issue = null): void
    {
        // 基础字段验证
        $this->validateBasicFields($params, $isCreate);
        
        // 自定义字段验证
        $this->validateCustomFields($params, $issue);
        
        // 权限验证
        $this->validatePermissions($params, $isCreate, $issue);
        
        // 工作流验证
        if (!$isCreate && isset($params['status_id'])) {
            $this->validateWorkflowTransition($issue, $params['status_id']);
        }
    }

    /**
     * 验证基础字段
     */
    protected function validateBasicFields(array $params, bool $isCreate): void
    {
        if ($isCreate) {
            // 新建时必填字段验证
            if (empty($params['subject'])) {
                throw new AppException(StatusCode::VALIDATION_ERROR, '标题不能为空');
            }
            if (empty($params['project_id'])) {
                throw new AppException(StatusCode::VALIDATION_ERROR, '项目不能为空');
            }
            if (empty($params['tracker_id'])) {
                throw new AppException(StatusCode::VALIDATION_ERROR, '跟踪器不能为空');
            }
        }
        
        // 字段长度验证
        if (isset($params['subject']) && mb_strlen($params['subject']) > 255) {
            throw new AppException(StatusCode::VALIDATION_ERROR, '标题长度不能超过255个字符');
        }
        
        // 日期验证
        if (isset($params['start_date']) && isset($params['due_date'])) {
            if ($params['start_date'] && $params['due_date'] && $params['start_date'] > $params['due_date']) {
                throw new AppException(StatusCode::VALIDATION_ERROR, '开始日期不能晚于结束日期');
            }
        }
        
        // 完成度验证
        if (isset($params['done_ratio']) && ($params['done_ratio'] < 0 || $params['done_ratio'] > 100)) {
            throw new AppException(StatusCode::VALIDATION_ERROR, '完成度必须在0-100之间');
        }
    }

    /**
     * 验证自定义字段
     */
    protected function validateCustomFields(array $params, ?IssueModel $issue = null): void
    {
        if (empty($params['custom_fields'])) {
            return;
        }
        
        foreach ($params['custom_fields'] as $customField) {
            $fieldId = $customField['id'] ?? null;
            $value = $customField['value'] ?? '';
            
            if (!$fieldId) {
                continue;
            }
            
            $field = CustomFieldsModel::find($fieldId);
            if (!$field) {
                continue;
            }
            
            // 必填验证
            if ($field->is_required && empty($value)) {
                throw new AppException(StatusCode::VALIDATION_ERROR, "自定义字段 {$field->name} 不能为空");
            }
            
            // 格式验证
            $this->validateCustomFieldFormat($field, $value);
        }
    }

    /**
     * 验证自定义字段格式
     */
    protected function validateCustomFieldFormat(CustomFieldsModel $field, $value): void
    {
        if (empty($value)) {
            return;
        }
        
        switch ($field->field_format) {
            case 'int':
                if (!is_numeric($value)) {
                    throw new AppException(StatusCode::VALIDATION_ERROR, "{$field->name} 必须是数字");
                }
                break;
            case 'date':
                if (!strtotime($value)) {
                    throw new AppException(StatusCode::VALIDATION_ERROR, "{$field->name} 日期格式不正确");
                }
                break;
            case 'string':
                if ($field->max_length && mb_strlen($value) > $field->max_length) {
                    throw new AppException(StatusCode::VALIDATION_ERROR, "{$field->name} 长度不能超过{$field->max_length}个字符");
                }
                if ($field->min_length && mb_strlen($value) < $field->min_length) {
                    throw new AppException(StatusCode::VALIDATION_ERROR, "{$field->name} 长度不能少于{$field->min_length}个字符");
                }
                break;
        }
    }

    /**
     * 验证权限
     */
    protected function validatePermissions(array $params, bool $isCreate, ?IssueModel $issue = null): void
    {
        $userId = getRedmineUserId();
        $user = UserModel::find($userId);
        
        if (!$user) {
            throw new AppException(StatusCode::ERR_SERVER, '用户不存在');
        }
        
        if ($isCreate) {
            // 验证新建权限
            $projectId = $params['project_id'];
            if (!$this->hasProjectPermission($userId, $projectId, 'add_issues')) {
                throw new AppException(StatusCode::ERR_SERVER, '没有创建事项的权限');
            }
        } else {
            // 验证编辑权限
            if (!$this->hasIssueEditPermission($userId, $issue)) {
                throw new AppException(StatusCode::ERR_SERVER, '没有编辑此事项的权限');
            }
        }
    }

    /**
     * 验证工作流状态转换
     */
    protected function validateWorkflowTransition(IssueModel $issue, int $newStatusId): void
    {
        $userId = getRedmineUserId();
        $oldStatusId = $issue->status_id;
        
        if ($oldStatusId == $newStatusId) {
            return; // 状态没有变化
        }
        
        $user = UserModel::find($userId);
        $isAdmin = $user && $user->admin == 1;
        
        if ($isAdmin) {
            return; // 管理员可以随意转换状态
        }
        
        // 获取用户在项目中的角色
        $roles = MemberModel::query()
            ->join('member_roles', 'members.id', '=', 'member_roles.member_id')
            ->where('members.user_id', $userId)
            ->where('members.project_id', $issue->project_id)
            ->pluck('role_id')
            ->toArray();
            
        if (empty($roles)) {
            throw new AppException(StatusCode::ERR_SERVER, '您不是此项目的成员');
        }
        
        // 检查工作流配置
        $workflowExists = WorkflowsModel::query()
            ->where('tracker_id', $issue->tracker_id)
            ->where('old_status_id', $oldStatusId)
            ->where('new_status_id', $newStatusId)
            ->whereIn('role_id', $roles)
            ->where(function ($query) use ($userId, $issue) {
                $query->where('author', 0)  // 不限制作者
                      ->orWhere(function ($q) use ($userId, $issue) {
                          $q->where('author', 1)->where($issue->author_id, $userId);
                      });
            })
            ->where(function ($query) use ($userId, $issue) {
                $query->where('assignee', 0)  // 不限制指派人
                      ->orWhere(function ($q) use ($userId, $issue) {
                          $q->where('assignee', 1)->where($issue->assigned_to_id, $userId);
                      });
            })
            ->exists();
            
        if (!$workflowExists) {
            throw new AppException(StatusCode::ERR_SERVER, '不允许进行此状态转换');
        }
    }

    /**
     * 检查项目权限
     */
    protected function hasProjectPermission(int $userId, int $projectId, string $permission): bool
    {
        $user = UserModel::find($userId);
        if ($user && $user->admin == 1) {
            return true; // 管理员有所有权限
        }
        
        // 检查用户是否是项目成员且有对应权限
        // 这里需要根据实际的权限系统实现
        $member = MemberModel::query()
            ->where('user_id', $userId)
            ->where('project_id', $projectId)
            ->exists();
            
        return $member; // 简化实现，项目成员即有权限
    }

    /**
     * 检查事项编辑权限
     */
    protected function hasIssueEditPermission(int $userId, IssueModel $issue): bool
    {
        $user = UserModel::find($userId);
        if ($user && $user->admin == 1) {
            return true; // 管理员有所有权限
        }
        
        // 检查是否是事项创建者
        if ($issue->author_id == $userId) {
            return $this->hasProjectPermission($userId, $issue->project_id, 'edit_own_issues');
        }
        
        // 检查是否有编辑权限
        return $this->hasProjectPermission($userId, $issue->project_id, 'edit_issues');
    }

    /**
     * 准备创建事项的数据
     */
    protected function prepareIssueDataForCreate(array $params): array
    {
        $userId = getRedmineUserId();
        $now = $this->getAdjustedTime();
        
        $data = [
            'project_id' => $params['project_id'],
            'tracker_id' => $params['tracker_id'],
            'subject' => $params['subject'],
            'description' => $params['description'] ?? '',
            'status_id' => $params['status_id'] ?? $this->getDefaultStatusId($params['tracker_id']),
            'priority_id' => $params['priority_id'] ?? $this->getDefaultPriorityId(),
            'author_id' => $params['author_id'] ?? $userId, // 优先使用传入的author_id，如果没有则使用当前用户
            'created_on' => $now,
            'updated_on' => $now,
            'lock_version' => 0,
        ];
        
        // 可选字段
        $optionalFields = [
            'assigned_to_id', 'category_id', 'fixed_version_id', 
            'parent_id', 'start_date', 'due_date', 'done_ratio', 
            'estimated_hours', 'is_private', 'class_id', 'class_pid'
        ];
        
        foreach ($optionalFields as $field) {
            if (isset($params[$field])) {
                $data[$field] = $params[$field];
            }
        }
        
        // 设置嵌套集合字段
        $this->setNestedSetFields($data);
        
        return $data;
    }

    /**
     * 设置嵌套集合字段 (lft, rgt, root_id)
     */
    protected function setNestedSetFields(array &$data): void
    {
        if (isset($data['parent_id']) && $data['parent_id']) {
            // 有父节点的情况
            $this->setChildNestedSetFields($data, $data['parent_id']);
        } else {
            // 根节点的情况
            $this->setRootNestedSetFields($data);
        }
    }

    /**
     * 设置根节点的嵌套集合字段（按root_id分组）
     */
    protected function setRootNestedSetFields(array &$data): void
    {
        $projectId = $data['project_id'];
        
        Log::get()->info('开始设置根节点嵌套集合字段（按root_id分组）', [
            'project_id' => $projectId,
            'action' => 'set_root_nested_set_fields_grouped'
        ]);
        
        // 对于根节点，每个都从1,2开始（按root_id分组的嵌套集合）
        $data['lft'] = 1;
        $data['rgt'] = 2;
        $data['root_id'] = null; // 创建时先设为null，创建后再更新为自身ID
        
        Log::get()->info('根节点嵌套集合字段设置完成（按root_id分组）', [
            'project_id' => $projectId,
            'final_lft' => $data['lft'],
            'final_rgt' => $data['rgt'],
            'root_id' => $data['root_id'],
            'note' => '每个根节点都从1,2开始，按root_id分组管理'
        ]);
    }

    /**
     * 设置子节点的嵌套集合字段（按root_id分组）
     */
    protected function setChildNestedSetFields(array &$data, int $parentId): void
    {
        $parent = $this->issueModel::find($parentId);
        if (!$parent) {
            throw new AppException(StatusCode::ERR_SERVER, '父事项不存在');
        }

        Log::get()->info('开始设置子节点嵌套集合字段（按root_id分组）', [
            'parent_id' => $parentId,
            'parent_lft' => $parent->lft,
            'parent_rgt' => $parent->rgt,
            'parent_root_id' => $parent->root_id
        ]);

        // 确保父节点有正确的嵌套集合字段
        if ($parent->lft === null || $parent->rgt === null) {
            // 如果父节点的嵌套集合字段为空，先给它设置基本值
            if (!$parent->parent_id) {
                // 父节点是根节点，按root_id分组，从1,2开始
                $parent->update([
                    'lft' => 1,
                    'rgt' => 2,
                    'root_id' => $parent->id
                ]);
            } else {
                // 父节点也是子节点，递归处理
                $parentData = ['parent_id' => $parent->parent_id];
                $this->setChildNestedSetFields($parentData, $parent->parent_id);
                $parent->update([
                    'lft' => $parentData['lft'],
                    'rgt' => $parentData['rgt'],
                    'root_id' => $parentData['root_id']
                ]);
            }
            $parent->refresh();
        }

        // 获取根节点ID
        $rootId = $parent->root_id ?: $parent->id;
        
        // 在父节点的 rgt 位置插入新节点
        $insertPoint = $parent->rgt;
        
        // 更新所有受影响的节点的 lft 和 rgt（限制在同一个root_id内）
        $this->makeSpaceForNewNodeInRootGroup($insertPoint, $parent->project_id, $rootId);
        
        // 设置新节点的值
        $data['lft'] = $insertPoint;
        $data['rgt'] = $insertPoint + 1;
        $data['root_id'] = $rootId;
        
        Log::get()->info('子节点嵌套集合字段设置完成（按root_id分组）', [
            'parent_id' => $parentId,
            'child_lft' => $data['lft'],
            'child_rgt' => $data['rgt'],
            'child_root_id' => $data['root_id'],
            'insert_point' => $insertPoint
        ]);
    }

    /**
     * 为新节点腾出空间
     */
    protected function makeSpaceForNewNode(int $insertPoint, int $projectId): void
    {
        // 所有 lft >= insertPoint 的节点 +2（限制在同一项目内）
        $this->issueModel::where('project_id', $projectId)
            ->where('lft', '>=', $insertPoint)
            ->increment('lft', 2);
            
        // 所有 rgt >= insertPoint 的节点 +2（限制在同一项目内）
        $this->issueModel::where('project_id', $projectId)
            ->where('rgt', '>=', $insertPoint)
            ->increment('rgt', 2);
    }

    /**
     * 为新节点腾出空间（限制在同一个root_id内）
     */
    protected function makeSpaceForNewNodeInRootGroup(int $insertPoint, int $projectId, int $rootId): void
    {
        Log::get()->info('为新节点腾出空间（按root_id分组）', [
            'insert_point' => $insertPoint,
            'project_id' => $projectId,
            'root_id' => $rootId
        ]);
        
        // 所有 lft >= insertPoint 的节点 +2（限制在同一项目和同一root_id内）
        $affectedLft = $this->issueModel::where('project_id', $projectId)
            ->where('root_id', $rootId)
            ->where('lft', '>=', $insertPoint)
            ->increment('lft', 2);
            
        // 所有 rgt >= insertPoint 的节点 +2（限制在同一项目和同一root_id内）
        $affectedRgt = $this->issueModel::where('project_id', $projectId)
            ->where('root_id', $rootId)
            ->where('rgt', '>=', $insertPoint)
            ->increment('rgt', 2);
            
        Log::get()->info('空间腾出完成（按root_id分组）', [
            'affected_lft_count' => $affectedLft,
            'affected_rgt_count' => $affectedRgt,
            'insert_point' => $insertPoint,
            'root_id' => $rootId
        ]);
    }

    /**
     * 重建单个节点的嵌套集合字段
     */
    protected function rebuildNestedSetForNode(IssueModel $node): void
    {
        if ($node->parent_id) {
            // 子节点：基于父节点重建
            $parent = $this->issueModel::find($node->parent_id);
            if ($parent && $parent->lft && $parent->rgt) {
                $data = ['parent_id' => $node->parent_id];
                $this->setChildNestedSetFields($data, $node->parent_id);
                $node->update([
                    'lft' => $data['lft'],
                    'rgt' => $data['rgt'],
                    'root_id' => $data['root_id']
                ]);
            } else {
                // 父节点也有问题，设为根节点
                $data = ['project_id' => $node->project_id];
                $this->setRootNestedSetFields($data);
                $node->update([
                    'lft' => $data['lft'],
                    'rgt' => $data['rgt'],
                    'root_id' => $node->id,
                    'parent_id' => null
                ]);
            }
        } else {
            // 根节点
            $data = ['project_id' => $node->project_id];
            $this->setRootNestedSetFields($data);
            $node->update([
                'lft' => $data['lft'],
                'rgt' => $data['rgt'],
                'root_id' => $node->id
            ]);
        }
    }

    /**
     * 更新事项后维护嵌套集合
     */
    protected function maintainNestedSetAfterUpdate(IssueModel $issue, array $oldAttributes): void
    {
        $oldParentId = $oldAttributes['parent_id'] ?? null;
        $newParentId = $issue->parent_id;
        
        // 如果父节点发生变化，需要重新计算嵌套集合
        if ($oldParentId != $newParentId) {
            $this->moveNodeInNestedSetInRootGroup($issue, $oldParentId, $newParentId);
        }
    }

    /**
     * 公共方法：更新事项后维护嵌套集合（用于外部调用）
     */
    public function updateNestedSetAfterParentChange(IssueModel $issue, array $oldAttributes): void
    {
        $this->maintainNestedSetAfterUpdate($issue, $oldAttributes);
    }

    /**
     * 在嵌套集合中移动节点
     */
    protected function moveNodeInNestedSet(IssueModel $issue, ?int $oldParentId, ?int $newParentId): void
    {
        // 先移除节点（缩小空间）
        $this->removeNodeFromNestedSet($issue);
        
        // 重新计算位置并插入
        if ($newParentId) {
            $data = ['parent_id' => $newParentId, 'project_id' => $issue->project_id];
            $this->setChildNestedSetFields($data, $newParentId);
        } else {
            $data = ['project_id' => $issue->project_id];
            $this->setRootNestedSetFields($data);
            $data['root_id'] = $issue->id; // 成为根节点
        }
        
        // 更新节点
        $issue->update([
            'lft' => $data['lft'],
            'rgt' => $data['rgt'],
            'root_id' => $data['root_id'] ?? $issue->id
        ]);
    }

    /**
     * 在嵌套集合中移动节点（按root_id分组）
     */
    protected function moveNodeInNestedSetInRootGroup(IssueModel $issue, ?int $oldParentId, ?int $newParentId): void
    {
        Log::get()->info('开始移动节点（按root_id分组）', [
            'issue_id' => $issue->id,
            'old_parent_id' => $oldParentId,
            'new_parent_id' => $newParentId,
            'current_root_id' => $issue->root_id
        ]);
        
        // 先移除节点（缩小空间，限制在同一root_id内）
        $this->removeNodeFromNestedSetInRootGroup($issue);
        
        // 重新计算位置并插入
        if ($newParentId) {
            $data = ['parent_id' => $newParentId, 'project_id' => $issue->project_id];
            $this->setChildNestedSetFields($data, $newParentId);
        } else {
            $data = ['project_id' => $issue->project_id];
            $this->setRootNestedSetFields($data);
            $data['root_id'] = $issue->id; // 成为根节点
        }
        
        // 更新节点
        $issue->update([
            'lft' => $data['lft'],
            'rgt' => $data['rgt'],
            'root_id' => $data['root_id'] ?? $issue->id
        ]);
        
        Log::get()->info('节点移动完成（按root_id分组）', [
            'issue_id' => $issue->id,
            'new_lft' => $data['lft'],
            'new_rgt' => $data['rgt'],
            'new_root_id' => $data['root_id'] ?? $issue->id
        ]);
    }

    /**
     * 从嵌套集合中移除节点
     */
    protected function removeNodeFromNestedSet(IssueModel $issue): void
    {
        if ($issue->lft && $issue->rgt) {
            $nodeWidth = $issue->rgt - $issue->lft + 1;
            
            // 所有 lft > issue.rgt 的节点 -nodeWidth（限制在同一项目内）
            $this->issueModel::where('project_id', $issue->project_id)
                ->where('lft', '>', $issue->rgt)
                ->decrement('lft', $nodeWidth);
                
            // 所有 rgt > issue.rgt 的节点 -nodeWidth（限制在同一项目内）
            $this->issueModel::where('project_id', $issue->project_id)
                ->where('rgt', '>', $issue->rgt)
                ->decrement('rgt', $nodeWidth);
        }
    }

    /**
     * 从嵌套集合中移除节点（限制在同一个root_id内）
     */
    protected function removeNodeFromNestedSetInRootGroup(IssueModel $issue): void
    {
        if ($issue->lft && $issue->rgt && $issue->root_id) {
            $nodeWidth = $issue->rgt - $issue->lft + 1;
            
            Log::get()->info('从嵌套集合中移除节点（按root_id分组）', [
                'issue_id' => $issue->id,
                'lft' => $issue->lft,
                'rgt' => $issue->rgt,
                'root_id' => $issue->root_id,
                'node_width' => $nodeWidth
            ]);
            
            // 所有 lft > issue.rgt 的节点 -nodeWidth（限制在同一项目和同一root_id内）
            $affectedLft = $this->issueModel::where('project_id', $issue->project_id)
                ->where('root_id', $issue->root_id)
                ->where('lft', '>', $issue->rgt)
                ->decrement('lft', $nodeWidth);
                
            // 所有 rgt > issue.rgt 的节点 -nodeWidth（限制在同一项目和同一root_id内）
            $affectedRgt = $this->issueModel::where('project_id', $issue->project_id)
                ->where('root_id', $issue->root_id)
                ->where('rgt', '>', $issue->rgt)
                ->decrement('rgt', $nodeWidth);
                
            Log::get()->info('节点移除完成（按root_id分组）', [
                'issue_id' => $issue->id,
                'affected_lft_count' => $affectedLft,
                'affected_rgt_count' => $affectedRgt,
                'node_width' => $nodeWidth
            ]);
        }
    }

    /**
     * 验证并修复嵌套集合的完整性
     */
    public function validateAndFixNestedSet(int $projectId = null): array
    {
        Log::get()->info('开始验证嵌套集合完整性', ['project_id' => $projectId]);
        
        $query = $this->issueModel::query();
        if ($projectId) {
            $query->where('project_id', $projectId);
        }
        
        $issues = $query->get();
        $problems = [];
        
        foreach ($issues as $issue) {
            // 检查 lft、rgt 是否为空
            if ($issue->lft === null || $issue->rgt === null) {
                $problems[] = [
                    'issue_id' => $issue->id,
                    'problem' => 'lft 或 rgt 为空',
                    'action' => '重建嵌套集合字段'
                ];
                $this->rebuildNestedSetForNode($issue);
                continue;
            }
            
            // 检查 lft < rgt
            if ($issue->lft >= $issue->rgt) {
                $problems[] = [
                    'issue_id' => $issue->id,
                    'problem' => 'lft >= rgt',
                    'lft' => $issue->lft,
                    'rgt' => $issue->rgt,
                    'action' => '重建嵌套集合字段'
                ];
                $this->rebuildNestedSetForNode($issue);
                continue;
            }
            
            // 检查父子关系
            if ($issue->parent_id) {
                $parent = $this->issueModel::find($issue->parent_id);
                if (!$parent) {
                    $problems[] = [
                        'issue_id' => $issue->id,
                        'problem' => '父节点不存在',
                        'parent_id' => $issue->parent_id,
                        'action' => '设为根节点'
                    ];
                    $issue->update(['parent_id' => null]);
                    $this->rebuildNestedSetForNode($issue);
                } elseif ($parent->lft && $parent->rgt && 
                         ($issue->lft <= $parent->lft || $issue->rgt >= $parent->rgt)) {
                    $problems[] = [
                        'issue_id' => $issue->id,
                        'problem' => '子节点不在父节点范围内',
                        'issue_lft' => $issue->lft,
                        'issue_rgt' => $issue->rgt,
                        'parent_lft' => $parent->lft,
                        'parent_rgt' => $parent->rgt,
                        'action' => '重建嵌套集合字段'
                    ];
                    $this->rebuildNestedSetForNode($issue);
                }
            }
            
            // 检查 root_id
            if ($issue->parent_id && !$issue->root_id) {
                $problems[] = [
                    'issue_id' => $issue->id,
                    'problem' => 'root_id 为空',
                    'action' => '设置正确的 root_id'
                ];
                $this->rebuildNestedSetForNode($issue);
            } elseif (!$issue->parent_id && $issue->root_id !== $issue->id) {
                $problems[] = [
                    'issue_id' => $issue->id,
                    'problem' => '根节点的 root_id 不正确',
                    'current_root_id' => $issue->root_id,
                    'expected_root_id' => $issue->id,
                    'action' => '修正 root_id'
                ];
                $issue->update(['root_id' => $issue->id]);
            }
        }
        
        Log::get()->info('嵌套集合验证完成', [
            'project_id' => $projectId,
            'total_issues' => $issues->count(),
            'problems_found' => count($problems)
        ]);
        
        return [
            'total_issues' => $issues->count(),
            'problems_found' => count($problems),
            'problems' => $problems
        ];
    }

    /**
     * 完全重建整个项目的嵌套集合（按root_id分组）
     */
    public function rebuildNestedSetForProject(int $projectId): void
    {
        Log::get()->info("开始重建项目 {$projectId} 的嵌套集合（按root_id分组）");
        
        // 获取项目的所有事项，按层级排序
        $issues = $this->issueModel::where('project_id', $projectId)
            ->orderBy('parent_id', 'ASC')
            ->orderBy('id', 'ASC')
            ->get();
            
        Log::get()->info("项目事项统计", [
            'project_id' => $projectId,
            'total_issues' => $issues->count(),
            'root_nodes_count' => $issues->whereNull('parent_id')->count()
        ]);
            
        $rootNodes = $issues->whereNull('parent_id');
        
        // 按root_id分组重建，每个根节点树都从1开始
        foreach ($rootNodes as $root) {
            Log::get()->info("重建根节点树（按root_id分组）", [
                'project_id' => $projectId,
                'root_issue_id' => $root->id
            ]);
            
            // 每个根节点树都从1开始计数
            $counter = 1;
            $counter = $this->rebuildNodeAndChildrenInRootGroup($root, $counter, $issues);
            
            Log::get()->info("根节点树重建完成（按root_id分组）", [
                'project_id' => $projectId,
                'root_issue_id' => $root->id,
                'final_counter' => $counter
            ]);
        }
        
        Log::get()->info("项目 {$projectId} 的嵌套集合重建完成（按root_id分组）", [
            'root_nodes_count' => $rootNodes->count(),
            'note' => '每个根节点树都从1开始计数'
        ]);
    }

    /**
     * 递归重建节点及其子节点
     */
    protected function rebuildNodeAndChildren(IssueModel $node, int $counter, $allIssues): int
    {
        $node->lft = $counter++;
        
        Log::get()->info("重建节点", [
            'node_id' => $node->id,
            'parent_id' => $node->parent_id,
            'lft' => $node->lft,
            'counter_before_children' => $counter
        ]);
        
        // 处理子节点
        $children = $allIssues->where('parent_id', $node->id);
        foreach ($children as $child) {
            $child->root_id = $node->root_id ?: $node->id;
            $counter = $this->rebuildNodeAndChildren($child, $counter, $allIssues);
        }
        
        $node->rgt = $counter++;
        $node->root_id = $node->root_id ?: $node->id;
        $node->save();
        
        Log::get()->info("节点重建完成", [
            'node_id' => $node->id,
            'lft' => $node->lft,
            'rgt' => $node->rgt,
            'root_id' => $node->root_id,
            'children_count' => $children->count(),
            'counter_after' => $counter
        ]);
        
        return $counter;
    }

    /**
     * 递归重建节点及其子节点（按root_id分组）
     */
    protected function rebuildNodeAndChildrenInRootGroup(IssueModel $node, int $counter, $allIssues): int
    {
        $node->lft = $counter++;
        
        Log::get()->info("重建节点（按root_id分组）", [
            'node_id' => $node->id,
            'parent_id' => $node->parent_id,
            'lft' => $node->lft,
            'counter_before_children' => $counter
        ]);
        
        // 处理子节点
        $children = $allIssues->where('parent_id', $node->id);
        foreach ($children as $child) {
            $child->root_id = $node->root_id ?: $node->id;
            $counter = $this->rebuildNodeAndChildrenInRootGroup($child, $counter, $allIssues);
        }
        
        $node->rgt = $counter++;
        $node->root_id = $node->root_id ?: $node->id;
        $node->save();
        
        Log::get()->info("节点重建完成（按root_id分组）", [
            'node_id' => $node->id,
            'lft' => $node->lft,
            'rgt' => $node->rgt,
            'root_id' => $node->root_id,
            'children_count' => $children->count(),
            'counter_after' => $counter
        ]);
        
        return $counter;
    }

    /**
     * 准备更新事项的数据
     */
    protected function prepareIssueDataForUpdate(array $params, IssueModel $issue): array
    {
        $data = [];
        $now = $this->getAdjustedTime();
        
        // 可更新的字段
        $updateableFields = [
            'subject', 'description', 'status_id', 'priority_id', 
            'assigned_to_id', 'category_id', 'fixed_version_id', 
            'parent_id', 'start_date', 'due_date', 'done_ratio', 
            'estimated_hours', 'is_private', 'class_id', 'class_pid'
        ];
        
        foreach ($updateableFields as $field) {
            if (array_key_exists($field, $params)) {
                $data[$field] = $params[$field];
            }
        }
        
        // 更新时间
        $data['updated_on'] = $now;
        
        return $data;
    }

    /**
     * 处理乐观锁
     */
    protected function handleOptimisticLocking(IssueModel $issue, array $params): void
    {
        if (isset($params['lock_version'])) {
            if ($params['lock_version'] < $issue->lock_version) {
                throw new AppException(StatusCode::ERR_SERVER, '数据已被其他用户修改，请刷新后重试');
            }
            $issue->lock_version = $params['lock_version'] + 1;
        } else {
            $issue->lock_version = $issue->lock_version + 1;
        }
        $issue->save();
    }

    /**
     * 处理事项关联数据
     */
    protected function handleIssueRelatedData(IssueModel $issue, array $params, bool $isCreate): void
    {
        // 处理自定义字段
        if (isset($params['custom_fields'])) {
            $this->updateCustomFields($issue, $params['custom_fields']);
        }
        
        // 处理关注人
        if (isset($params['watchers'])) {
            $this->updateWatchers($issue, $params['watchers']);
        }
        
        // 处理多人指派
        if (isset($params['assigned'])) {
            $this->updateIssueAssigned($issue, $params['assigned']);
        }
        
        // 处理附件
        if (isset($params['uploads'])) {
            $this->handleAttachments($issue, $params['uploads']);
        }
    }

    /**
     * 更新自定义字段
     */
    protected function updateCustomFields(IssueModel $issue, array $customFields): void
    {
        foreach ($customFields as $customField) {
            $fieldId = $customField['id'] ?? null;
            $value   = $customField['value'] ?? '';

            if (!$fieldId) {
                continue;
            }

            // 数组情况 -> 拆成多条插入
            if (is_array($value)) {
                foreach ($value as $v) {
                    CustomValuesModel::create([
                        'customized_type' => 'Issue',
                        'customized_id'   => $issue->id,
                        'custom_field_id' => $fieldId,
                        'value'           => (string)$v, // 统一转为字符串存储
                    ]);
                }
                continue; // 跳过后面逻辑
            }

            CustomValuesModel::create([
                'customized_type' => 'Issue',
                'customized_id'   => $issue->id,
                'custom_field_id' => $fieldId,
                'value'           => (string)$value,
            ]);
        }
    }
    /**
     * 更新关注人
     */
    protected function updateWatchers(IssueModel $issue, array $watchers): void
    {
        // 删除现有关注人
        WatchersModel::where('watchable_type', 'Issue')
            ->where('watchable_id', $issue->id)
            ->delete();

        $projectMemberIds = \App\Model\Redmine\MemberModel::query()
            ->select([
                'members.user_id',
            ])
            ->join('users', 'members.user_id', '=', 'users.id')
            ->where('members.project_id', $issue->project_id)
            ->where('users.status', 1) // 只要用户是活跃的
            ->pluck('user_id')
            ->toArray();

        // 20250826 16：00 去除不在对应项目成员中的关注人
        $watchers = array_intersect($watchers, $projectMemberIds);

        // 添加新关注人
        foreach ($watchers as $userId) {
            if (is_numeric($userId)) {
                WatchersModel::create([
                    'watchable_type' => 'Issue',
                    'watchable_id' => $issue->id,
                    'user_id' => $userId,
                ]);
            }
        }
    }

    /**
     * 更新多人指派
     */
    protected function updateIssueAssigned(IssueModel $issue, array $assigned): void
    {
        // 删除现有指派
        IssueAssignedModel::where('issue_id', $issue->id)->delete();


        $projectMemberIds = \App\Model\Redmine\MemberModel::query()
            ->select([
                'members.user_id',
            ])
            ->join('users', 'members.user_id', '=', 'users.id')
            ->where('members.project_id', $issue->project_id)
            ->where('users.status', 1) // 只要用户是活跃的
            ->pluck('user_id')
            ->toArray();

        // 20250826 15：30 去除不在对应项目成员中的处理人
        $assigned = array_intersect($assigned, $projectMemberIds);
        
        // 添加新指派
        foreach ($assigned as $index => $userId) {
            if (is_numeric($userId)) {
                IssueAssignedModel::create([
                    'issue_id' => $issue->id,
                    'user_id' => $userId,
                    'leader' => $index === 0 ? 1 : 0, // 第一个为主负责人
                    'created_at' => $this->getAdjustedCarbon(),
                    'updated_at' => $this->getAdjustedCarbon(),
                ]);
            }
        }
    }

    /**
     * 处理附件
     */
    protected function handleAttachments(IssueModel $issue, array $uploads): void
    {
        foreach ($uploads as $upload) {
            if (isset($upload['token'])) {
                $tokenParts = explode('.', $upload['token']);
                $attachmentId = $tokenParts[0] ?? null;
                
                if ($attachmentId && is_numeric($attachmentId)) {
                    // 更新附件的容器ID
                    AttachmentModel::where('id', $attachmentId)
                        ->update([
                            'container_id' => $issue->id,
                            'container_type' => 'Issue',
                        ]);
                }
            }
        }
    }

    /**
     * 创建初始Journal
     */
    protected function createInitialJournal(IssueModel $issue): void
    {
        // 新建事项时创建空的Journal记录
        $this->journalsModel::create([
            'journalized_id' => $issue->id,
            'journalized_type' => 'Issue',
            'user_id' => getRedmineUserId(),
            'notes' => '',
            'created_on' => $this->getAdjustedCarbon(),
            'private_notes' => 0,
        ]);
    }

    /**
     * 为更新创建Journal记录
     */
    protected function createJournalForUpdate(IssueModel $issue, array $oldAttributes, array $params): void
    {
        $changes = $this->detectChanges($oldAttributes, $issue->toArray());
        
        if (empty($changes) && empty($params['notes'])) {
            return; // 没有变更，不创建Journal
        }
        
        $journal = $this->journalsModel::create([
            'journalized_id' => $issue->id,
            'journalized_type' => 'Issue',
            'user_id' => getRedmineUserId(),
            'notes' => $params['notes'] ?? '',
            'created_on' => $this->getAdjustedCarbon(),
            'private_notes' => $params['private_notes'] ?? 0,
        ]);
        
        // 创建变更详情
        foreach ($changes as $field => $change) {
            JournalDetailsModel::create([
                'journal_id' => $journal->id,
                'property' => 'attr',
                'prop_key' => $field,
                'old_value' => $change['old'],
                'value' => $change['new'],
            ]);
        }
        
        // 处理自定义字段变更
        if (isset($params['custom_fields'])) {
            $this->createCustomFieldJournalDetails($journal, $issue, $params['custom_fields']);
        }
    }

    /**
     * 检测变更
     */
    protected function detectChanges(array $oldAttributes, array $newAttributes): array
    {
        $changes = [];
        $watchedFields = [
            'status_id' => '状态',
            'assigned_to_id' => '指派给',
            'priority_id' => '优先级', 
            'done_ratio' => '完成度',
            'start_date' => '开始日期',
            'due_date' => '截止日期',
            'estimated_hours' => '预估工时',
            'category_id' => '分类',
            'fixed_version_id' => '目标版本',
            'subject' => '标题',
            'description' => '描述'
        ];

        foreach ($watchedFields as $field => $label) {
            $oldValue = $oldAttributes[$field] ?? null;
            $newValue = $newAttributes[$field] ?? null;
            
            if ($oldValue !== $newValue) {
                $changes[] = [
                    'field' => $field,
                    'label' => $label,
                    'old_value' => $this->formatFieldValue($field, $oldValue),
                    'new_value' => $this->formatFieldValue($field, $newValue),
                ];
            }
        }

        return $changes;
    }

    /**
     * 格式化字段值用于显示
     */
    protected function formatFieldValue(string $field, $value): ?string
    {
        if ($value === null || $value === '') {
            return null;
        }

        // 根据字段类型进行特殊格式化
        switch ($field) {
            case 'status_id':
                $status = \App\Model\Redmine\IssueStatusModel::find($value);
                return $status ? $status->name : (string)$value;
                
            case 'priority_id':
                $priority = \App\Model\Redmine\EnumerationModel::find($value);
                return $priority ? $priority->name : (string)$value;
                
            case 'assigned_to_id':
                $user = \App\Model\Redmine\UserModel::find($value);
                return $user ? $user->name : (string)$value;
                
            case 'category_id':
                $category = \App\Model\Redmine\IssueCategoriesModel::find($value);
                return $category ? $category->name : (string)$value;
                
            case 'fixed_version_id':
                $version = \App\Model\Redmine\VersionModel::find($value);
                return $version ? $version->name : (string)$value;
                
            case 'estimated_hours':
                return $value ? $value . ' 小时' : null;
                
            case 'done_ratio':
                return $value . '%';
                
            default:
                return (string)$value;
        }
    }

    /**
     * 创建自定义字段Journal详情
     */
    protected function createCustomFieldJournalDetails(JournalsModel $journal, IssueModel $issue, array $customFields): void
    {
        foreach ($customFields as $customField) {
            $fieldId = $customField['id'] ?? null;
            $newValue = $customField['value'] ?? '';
            
            if (!$fieldId) {
                continue;
            }
            
            // 获取旧值
            $oldValue = CustomValuesModel::where('customized_type', 'Issue')
                ->where('customized_id', $issue->id)
                ->where('custom_field_id', $fieldId)
                ->value('value') ?? '';
            
            if ($oldValue != $newValue) {
                JournalDetailsModel::create([
                    'journal_id' => $journal->id,
                    'property' => 'cf',
                    'prop_key' => $fieldId,
                    'old_value' => $oldValue,
                    'value' => $newValue,
                ]);
            }
        }
    }

    /**
     * 发送创建通知
     */
    protected function sendCreateNotification(IssueModel $issue, array $params): void
    {
        Log::get('debug')->info('=== IssueLocalApiService::sendCreateNotification ===', [
            'issue_id' => $issue->id,
            'issue_subject' => $issue->subject,
            'params' => $params
        ]);
        
        try {
            // 使用现有的NoticeService系统发送通知
            $this->sendNotificationViaNoticeService($issue, $params);
            
            Log::get('system')->info("事项创建通知已发送", ['issue_id' => $issue->id]);
        } catch (\Exception $e) {
            Log::get('debug')->error('创建通知发送异常', [
                'message' => $e->getMessage(),
                'exception_class' => get_class($e),
                'file' => $e->getFile() . ':' . $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            Log::get('system')->error("事项创建通知发送失败: " . $e->getMessage(), [
                'issue_id' => $issue->id,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
    
    /**
     * 通过现有NoticeService发送通知（使用队列）
     */
    protected function sendNotificationViaNoticeService(IssueModel $issue, array $params): void
    {
        Log::get('debug')->info('=== sendNotificationViaNoticeService 开始 ===', [
            'issue_id' => $issue->id,
            'project_id' => $issue->project_id
        ]);
        
        try {
            // 加载事项关联数据
            $issue->load(['issueType', 'issueStatus', 'enumeration', 'authorText', 'projectText']);
            
            // 准备通知参数
            $projectName = $issue->projectText ? $issue->projectText->name : '';
            $trackerName = $issue->issueType ? $issue->issueType->name : '';
            $statusName = $issue->issueStatus ? $issue->issueStatus->name : '';
            $authorName = $issue->authorText ? ($issue->authorText->name) : '';
            
            // 生成动态邮件标题: [项目名-事项类型 #事项ID](事项状态) 事项标题
            $emailTitle = "[{$projectName}-{$trackerName} #{$issue->id}]({$statusName}) {$issue->subject}";
            
            $noticeParams = [
                'issue_id' => $issue->id,
                'issue_subject' => $issue->subject,
                'issue_description' => $issue->description ?? '',
                'tracker_name' => $trackerName,
                'status_name' => $statusName,
                'priority_name' => $issue->enumeration ? $issue->enumeration->name : '',
                'author_name' => $authorName,
                'assigned_to_name' => $this->getMultipleAssignedUserNames($issue),
                'project_name' => $projectName,
                'project_id' => $issue->project_id,
                'host' => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
                'created_on' => $issue->created_on ?? $this->getAdjustedTime(),
                'issue_url' => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101') . '/#/project/detail?project_id=' . $issue->project_id . '&issue_id=' . $issue->id,
                'project_url' => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101') . '/#/project/issue?project_id=' . $issue->project_id,
                'settings_url' => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101') . '/settings/notifications',
                // 新增邮件通知专用变量
                'operator_name' => $authorName,
                'operation_type' => '创建',
                'email_title' => $emailTitle,
                'due_date' => $issue->due_date ?? '',
            ];
            
            Log::get('debug')->info('通知参数准备完成', $noticeParams);
            
            // 获取应该接收事项通知的BI用户ID列表（根据Redmine通知机制）
            $biUserIds = $this->getIssueNotificationUserIds($issue);
            
            if (empty($biUserIds)) {
                Log::get('debug')->warning('没有符合通知条件的用户，跳过通知发送');
                return;
            }
            
            // 获取NoticeService实例
            $noticeService = make(\App\Core\Services\Notice\NoticeService::class);
            
            // 为每个用户发送通知
            $successCount = 0;
            $failCount = 0;
            
            foreach ($biUserIds as $userId) {
                try {
                    // 设置用户名
                    $userName = \App\Model\TchipBi\UserModel::query()->where('id', $userId)->value('name') ?: '';
                    $userNoticeParams = $noticeParams;
                    $userNoticeParams['username'] = $userName;
                    
                    Log::get('debug')->info('正在为用户发送通知', [
                        'user_id' => $userId,
                        'user_name' => $userName,
                        'method' => 'issueCreated'
                    ]);
                    
                    // 调用NoticeService的issueCreated方法
                    $noticeService->issueCreated($userId, $userNoticeParams);
                    
                    $successCount++;
                    
                    Log::get('debug')->info('用户通知发送完成', [
                        'user_id' => $userId
                    ]);
                    
                } catch (\Exception $userNoticeException) {
                    $failCount++;
                    
                    Log::get('debug')->error('用户通知发送异常', [
                        'user_id' => $userId,
                        'error' => $userNoticeException->getMessage(),
                        'trace' => $userNoticeException->getTraceAsString()
                    ]);
                }
            }
            
            Log::get('debug')->info('通知发送完成', [
                'total_users' => count($biUserIds),
                'success_count' => $successCount,
                'fail_count' => $failCount
            ]);
            
        } catch (\Exception $e) {
            Log::get('system')->error("通知发送失败: " . $e->getMessage(), [
                'issue_id' => $issue->id,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 获取应该接收事项通知的用户ID列表（包含关注人、处理人等）
     */
    protected function getIssueNotificationUserIds(IssueModel $issue): array
    {
        try {
            $allNotifiedUsers = [];
            
            Log::get('debug')->info('=== 开始获取事项通知用户 ===', [
                'issue_id' => $issue->id,
                'project_id' => $issue->project_id,
                'author_id' => $issue->author_id,
                'assigned_to_id' => $issue->assigned_to_id
            ]);
            
            // 1. 获取所有项目成员及其通知设置
            $projectMembers = \App\Model\Redmine\MemberModel::query()
                ->select([
                    'members.user_id',
                    'members.mail_notification as member_mail_notification',
                    'users.mail_notification as user_mail_notification',
                    'users.status as user_status',
                    'users.firstname',
                    'users.lastname'
                ])
                ->join('users', 'members.user_id', '=', 'users.id')
                ->where('members.project_id', $issue->project_id)
                ->where('users.status', 1) // 只要用户是活跃的
                ->get();
                
            // 2. 获取关注人
            $watchers = \App\Model\Redmine\WatchersModel::query()
                ->where('watchable_type', 'Issue')
                ->where('watchable_id', $issue->id)
                ->pluck('user_id')
                ->toArray();
                
            // 3. 获取多人指派用户列表
            $assignedUsers = \App\Model\Redmine\IssueAssignedModel::query()
                ->where('issue_id', $issue->id)
                ->whereNull('deleted_at')
                ->pluck('user_id')
                ->toArray();
                
            Log::get('debug')->info('项目成员、关注人和指派人信息', [
                'project_members_count' => $projectMembers->count(),
                'watchers_count' => count($watchers),
                'watchers' => $watchers,
                'assigned_users_count' => count($assignedUsers),
                'assigned_users' => $assignedUsers
            ]);
            
            // 4. 根据用户的通知设置决定是否发送通知
            $notifiedRedmineUserIds = [];
            
            // 预先获取用户参与的项目信息（用于only_my_events判断）
            $userParticipatedProjects = $this->getUserParticipatedProjects(array_column($projectMembers->toArray(), 'user_id'));
            
            foreach ($projectMembers as $member) {
                $userId = $member->user_id;
                $userNotification = $member->user_mail_notification;
                $memberNotification = $member->member_mail_notification;
                
                $shouldNotify = false;
                $reason = '';
                
                switch ($userNotification) {
                    case 'all':
                        // 收取我的项目的所有通知 - 只要是用户参与的项目就发送通知
                        if (in_array($issue->project_id, $userParticipatedProjects[$userId] ?? [])) {
                            $shouldNotify = true;
                            $reason = 'user_setting_all_my_projects';
                        }
                        break;
                        
                    case 'selected':
                        // 收取选中项目的所有通知 - 检查成员级设置是否启用该项目通知
                        if ($memberNotification == 1) {
                            $shouldNotify = true;
                            $reason = 'user_setting_selected_and_member_enabled';
                        }
                        break;
                        
                    case 'only_my_events':
                        // 只收取我关注或参与的项目的通知 - 只要参与该项目就发送通知
                        if (in_array($issue->project_id, $userParticipatedProjects[$userId] ?? [])) {
                            $shouldNotify = true;
                            $reason = 'user_setting_only_my_events_participated_project';
                        }
                        break;
                        
                    case 'only_assigned':
                        // 只发送我关注或指派给我的相关信息（包括多人指派）
                        if ($userId == $issue->assigned_to_id || in_array($userId, $assignedUsers) || in_array($userId, $watchers)) {
                            $shouldNotify = true;
                            $reason = 'user_setting_only_assigned_or_watched';
                        }
                        break;
                        
                    case 'only_owner':
                        // 只发送我关注或我创建的相关信息
                        if ($userId == $issue->author_id || in_array($userId, $watchers)) {
                            $shouldNotify = true;
                            $reason = 'user_setting_only_owner_or_watched';
                        }
                        break;
                        
                    case 'none':
                    default:
                        // 不收取任何通知
                        $shouldNotify = false;
                        $reason = 'user_setting_none';
                        break;
                }
                
                if ($shouldNotify) {
                    $notifiedRedmineUserIds[] = $userId;
                    
                    Log::get('debug')->info('用户将接收通知', [
                        'user_id' => $userId,
                        'user_name' => $member->firstname . ' ' . $member->lastname,
                        'user_notification' => $userNotification,
                        'member_notification' => $memberNotification,
                        'reason' => $reason,
                        'is_author' => $userId == $issue->author_id,
                        'is_assigned' => $userId == $issue->assigned_to_id,
                        'is_multi_assigned' => in_array($userId, $assignedUsers),
                        'is_watcher' => in_array($userId, $watchers),
                        'participated_projects_count' => count($userParticipatedProjects[$userId] ?? [])
                    ]);
                } else {
                    Log::get('debug')->info('用户不接收通知', [
                        'user_id' => $userId,
                        'user_name' => $member->firstname . ' ' . $member->lastname,
                        'user_notification' => $userNotification,
                        'member_notification' => $memberNotification,
                        'reason' => $reason,
                        'is_author' => $userId == $issue->author_id,
                        'is_assigned' => $userId == $issue->assigned_to_id,
                        'is_multi_assigned' => in_array($userId, $assignedUsers),
                        'is_watcher' => in_array($userId, $watchers),
                        'participated_projects_count' => count($userParticipatedProjects[$userId] ?? [])
                    ]);
                }
            }
            
            // 5. 处理非项目成员的关注人和指派人（如果有的话）
            $projectMemberIds = $projectMembers->pluck('user_id')->toArray();
            $nonMemberWatchers = array_diff($watchers, $projectMemberIds);
            $nonMemberAssigned = array_diff($assignedUsers, $projectMemberIds);
            $nonMemberUsers = array_unique(array_merge($nonMemberWatchers, $nonMemberAssigned));
            
            if (!empty($nonMemberUsers)) {
                $nonMemberUsers = \App\Model\Redmine\UserModel::query()
                    ->select(['id', 'mail_notification', 'status', 'firstname', 'lastname'])
                    ->whereIn('id', $nonMemberUsers)
                    ->where('status', 1)
                    ->get();

                $nonMemberUserIds = $nonMemberUsers->pluck('id')->toArray();
                    
                // 获取非成员用户的参与项目信息
                $nonMemberParticipatedProjects = $this->getUserParticipatedProjects($nonMemberUserIds);
                    
                foreach ($nonMemberUsers as $user) {
                    $shouldNotify = false;
                    $reason = '';
                    
                    switch ($user->mail_notification) {
                        case 'all':
                            // 收取我的项目的所有通知 - 检查是否参与该项目
                            if (in_array($issue->project_id, $nonMemberParticipatedProjects[$user->id] ?? [])) {
                                $shouldNotify = true;
                                $reason = 'non_member_watcher_setting_all_my_projects';
                            }
                            break;
                            
                        case 'only_my_events':
                            // 只收取我关注或参与的项目的通知 - 只要参与该项目就发送通知
                            if (in_array($issue->project_id, $nonMemberParticipatedProjects[$user->id] ?? [])) {
                                $shouldNotify = true;
                                $reason = 'non_member_watcher_setting_only_my_events_participated_project';
                            }
                            break;
                            
                        case 'only_assigned':
                            // 只发送我关注或指派给我的相关信息 - 检查是否是关注人或指派人
                            if (in_array($user->id, $watchers) || $user->id == $issue->assigned_to_id || in_array($user->id, $assignedUsers)) {
                                $shouldNotify = true;
                                $reason = 'non_member_watcher_setting_only_assigned';
                            }
                            break;
                            
                        case 'only_owner':
                            // 只发送我关注或我创建的相关信息 - 检查是否是创建人或关注人
                            if ($user->id == $issue->author_id) {
                                $shouldNotify = true;
                                $reason = 'non_member_watcher_setting_only_owner';
                            }
                            break;
                    }
                    
                    if ($shouldNotify) {
                        $notifiedRedmineUserIds[] = $user->id;
                        
                        Log::get('debug')->info('非项目成员用户将接收通知', [
                            'user_id' => $user->id,
                            'user_name' => $user->firstname . ' ' . $user->lastname,
                            'user_notification' => $user->mail_notification,
                            'reason' => $reason,
                            'is_watcher' => in_array($user->id, $watchers),
                            'is_assigned' => $user->id == $issue->assigned_to_id,
                            'is_multi_assigned' => in_array($user->id, $assignedUsers)
                        ]);
                    }
                }
            }
            
            // 6. 映射到BI用户并检查邮箱设置
            $finalNotifiedUserIds = [];
            if (!empty($notifiedRedmineUserIds)) {
                Log::get('debug')->info('开始映射到BI用户', [
                    'notified_redmine_user_ids' => $notifiedRedmineUserIds
                ]);
                
                // 直接映射到BI用户ID并检查邮箱
                $biUsersWithEmail = \App\Model\TchipBi\UserModel::query()
                    ->select(['user.id', 'user.name', 'user.biz_mail', 'user_third.third_user_id'])
                    ->join('user_third', 'user.id', '=', 'user_third.user_id')
                    ->whereIn('user_third.third_user_id', $notifiedRedmineUserIds)
                    ->where('user_third.platform', 'redmine')
                    ->where('user.status', 1)
                    ->whereNotNull('user.biz_mail') // 必须有邮箱
                    ->where('user.biz_mail', '!=', '') // 邮箱不能为空
                    ->get();
                    
                Log::get('debug')->info('BI用户邮箱映射结果', [
                    'bi_users_with_email' => $biUsersWithEmail->toArray(),
                    'mapping_success_count' => $biUsersWithEmail->count()
                ]);
                
                $finalNotifiedUserIds = $biUsersWithEmail->pluck('third_user_id')->toArray();
            }
            
            // 7. 获取最终的BI用户ID
            $biUserIds = [];
            if (!empty($finalNotifiedUserIds)) {
                // 重新查询获取BI用户ID（因为上面已经过滤了有邮箱的用户）
                $biUsers = \App\Model\TchipBi\UserModel::query()
                    ->select(['user.id'])
                    ->join('user_third', 'user.id', '=', 'user_third.user_id')
                    ->whereIn('user_third.third_user_id', $finalNotifiedUserIds)
                    ->where('user_third.platform', 'redmine')
                    ->where('user.status', 1)
                    ->pluck('user.id')
                    ->toArray();
                    
                $biUserIds = $biUsers;
            }
            
            Log::get('debug')->info('最终通知用户统计', [
                'issue_id' => $issue->id,
                'project_members_count' => $projectMembers->count(),
                'qualified_redmine_users' => count($finalNotifiedUserIds),
                'final_bi_users' => count($biUserIds),
                'redmine_user_ids' => $finalNotifiedUserIds,
                'bi_user_ids' => $biUserIds
            ]);
            
            return $biUserIds;
            
        } catch (\Exception $e) {
            Log::get('debug')->error('获取事项通知用户列表失败', [
                'issue_id' => $issue->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * 获取用户参与的项目ID列表
     * @param array $userIds 用户ID数组
     * @return array 用户ID为键，项目ID数组为值的关联数组
     */
    protected function getUserParticipatedProjects(array $userIds): array
    {
        if (empty($userIds)) {
            return [];
        }
        
        $userProjects = \App\Model\Redmine\MemberModel::query()
            ->select(['user_id', 'project_id'])
            ->whereIn('user_id', $userIds)
            ->get()
            ->groupBy('user_id')
            ->toArray();
            
        $result = [];
        foreach ($userProjects as $userId => $projects) {
            $result[$userId] = array_column($projects, 'project_id');
        }
        
        // 添加调试日志
        Log::get('debug')->info('用户参与项目详情', [
            'user_projects_mapping' => $result,
            'total_users_checked' => count($userIds)
        ]);
        
        return $result;
    }

    /**
     * 发送更新通知
     */
    protected function sendUpdateNotification(IssueModel $issue, array $oldAttributes, array $params): void
    {
        try {
            // 检测变更
            $changes = $this->detectChanges($oldAttributes, $issue->toArray());
            
            if (!empty($changes) || !empty($params['notes'])) {
                // 使用现有的NoticeService系统发送更新通知
                $this->sendUpdateNotificationViaNoticeService($issue, $changes, $params);
                
                Log::get('system')->info("事项更新通知已发送", [
                    'issue_id' => $issue->id,
                    'changes_count' => count($changes)
                ]);
            }
        } catch (\Exception $e) {
            Log::get('system')->error("事项更新通知发送失败: " . $e->getMessage(), [
                'issue_id' => $issue->id,
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        // 如果有notes，通过原有的Journal事件发送
        if (!empty($params['notes'])) {
            $this->eventDispatcher->dispatch(
                new IssueJournalsNoticeEvent($issue->id, $params['notes'], [
                    'created_on' => Carbon::now()->format('Y-m-d H:i:s')
                ])
            );
        }
    }

    /**
     * 发送事项更新通知
     */
    protected function sendUpdateNotificationViaNoticeService(IssueModel $issue, array $changes, array $params): void
    {
        Log::get('debug')->info('=== sendUpdateNotificationViaNoticeService 开始 ===', [
            'issue_id' => $issue->id,
            'changes_count' => count($changes)
        ]);
        
        try {
            // 加载事项关联数据
            $issue->load(['issueType', 'issueStatus', 'enumeration', 'authorText', 'projectText']);
            
            // 准备变更描述文本
            $changesText = '';
            foreach ($changes as $change) {
                $oldValue = !empty($change['old_value']) ? $change['old_value'] : '空';
                $newValue = $change['new_value'];
                $changesText .= "{$change['label']}: {$oldValue} → {$newValue}\n";
            }
            
            // 准备通知参数
            $projectName = $issue->projectText ? $issue->projectText->name : '';
            $trackerName = $issue->issueType ? $issue->issueType->name : '';
            $statusName = $issue->issueStatus ? $issue->issueStatus->name : '';
            $authorName = $issue->authorText ? ($issue->authorText->name) : '';
            
            // 获取当前操作人姓名（修改者）
            $currentUserId = getRedmineUserId();
            $operatorName = '';
            if ($currentUserId) {
                $currentUser = \App\Model\Redmine\UserModel::find($currentUserId);
                $operatorName = $currentUser ? $currentUser->name : $authorName;
            } else {
                $operatorName = $authorName;
            }
            
            // 生成动态邮件标题: [项目名-事项类型 #事项ID](事项状态) 事项标题
            $emailTitle = "[{$projectName}-{$trackerName} #{$issue->id}]({$statusName}) {$issue->subject}";
            
            $noticeParams = [
                'issue_id' => $issue->id,
                'issue_subject' => $issue->subject,
                'issue_description' => $issue->description ?? '',
                'tracker_name' => $trackerName,
                'status_name' => $statusName,
                'priority_name' => $issue->enumeration ? $issue->enumeration->name : '',
                'author_name' => $authorName,
                'assigned_to_name' => $this->getMultipleAssignedUserNames($issue),
                'project_name' => $projectName,
                'project_id' => $issue->project_id,
                'host' => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101'),
                'changes_text' => $changesText,
                'notes' => $params['notes'] ?? '',
                'created_on' => $issue->updated_on ?? $this->getAdjustedTime(),
                'issue_url' => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101') . '/#/project/detail?project_id=' . $issue->project_id . '&issue_id=' . $issue->id,
                'project_url' => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101') . '/#/project/issue?project_id=' . $issue->project_id,
                'settings_url' => env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101') . '/settings/notifications',
                // 新增邮件通知专用变量
                'operator_name' => $operatorName,
                'operation_type' => '修改',
                'email_title' => $emailTitle,
                'due_date' => $issue->due_date ?? '',
            ];
            
            // 获取应该接收事项通知的BI用户ID列表（根据Redmine通知机制）
            $biUserIds = $this->getIssueNotificationUserIds($issue);
            
            if (empty($biUserIds)) {
                Log::get('debug')->warning('没有符合通知条件的用户，跳过通知发送');
                return;
            }
            
            // 获取NoticeService实例
            $noticeService = make(\App\Core\Services\Notice\NoticeService::class);
            
            // 为每个用户发送通知
            foreach ($biUserIds as $userId) {
                try {
                    // 设置用户名
                    $userName = \App\Model\TchipBi\UserModel::query()->where('id', $userId)->value('name') ?: '';
                    $userNoticeParams = $noticeParams;
                    $userNoticeParams['username'] = $userName;
                    
                    // 调用NoticeService的issueUpdated方法
                    $noticeService->issueUpdated($userId, $userNoticeParams);
                    
                } catch (\Exception $userNoticeException) {
                    Log::get('debug')->error('用户更新通知推送异常', [
                        'user_id' => $userId,
                        'error' => $userNoticeException->getMessage()
                    ]);
                }
            }
            
        } catch (\Exception $e) {
            Log::get('system')->error("更新通知推送失败: " . $e->getMessage(), [
                'issue_id' => $issue->id,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 获取指派用户姓名
     */
    protected function getAssignedUserName(int $userId): string
    {
        $user = UserModel::find($userId);
        return $user ? ($user->name) : '';
    }

    /**
     * 获取多人指派用户姓名（包括单人指派和多人指派）
     */
    protected function getMultipleAssignedUserNames(IssueModel $issue): string
    {
        $assignedNames = [];
        
        // 1. 获取单人指派用户（如果有）
        if ($issue->assigned_to_id) {
            $assignedNames[] = $this->getAssignedUserName($issue->assigned_to_id);
        }
        
        // 2. 获取多人指派用户
        $multiAssignedUsers = \App\Model\Redmine\IssueAssignedModel::query()
            ->select(['user_id'])
            ->where('issue_id', $issue->id)
            ->whereNull('deleted_at')
            ->get();
            
        if ($multiAssignedUsers->isNotEmpty()) {
            $userIds = $multiAssignedUsers->pluck('user_id')->toArray();
            
            // 去除重复的单人指派用户
            if ($issue->assigned_to_id) {
                $userIds = array_filter($userIds, function($id) use ($issue) {
                    return $id != $issue->assigned_to_id;
                });
            }
            
            // 获取用户姓名
            $users = \App\Model\Redmine\UserModel::query()
                ->whereIn('id', $userIds)
                ->select(['id', 'firstname', 'lastname'])
                ->get();
                
            foreach ($users as $user) {
                $assignedNames[] = $user->name;
            }
        }
        
        // 3. 返回组合后的姓名字符串
        if (empty($assignedNames)) {
            return '未指派';
        }
        
        return implode(', ', array_unique($assignedNames));
    }

    /**
     * 验证删除权限
     */
    protected function validateDeletePermission(IssueModel $issue): void
    {
        $userId = getRedmineUserId();
        $user = UserModel::find($userId);
        
        if ($user && $user->admin == 1) {
            return; // 管理员可以删除
        }
        
        // 检查删除权限
        if (!$this->hasProjectPermission($userId, $issue->project_id, 'delete_issues')) {
            throw new AppException(StatusCode::ERR_SERVER, '没有删除此事项的权限');
        }
    }

    /**
     * 处理级联删除
     */
    protected function handleCascadeDelete(IssueModel $issue): void
    {
        // 删除子事项
        $childIssues = IssueModel::where('parent_id', $issue->id)->get();
        foreach ($childIssues as $child) {
            $this->handleCascadeDelete($child);
        }
        
        // 删除相关数据
        JournalsModel::where('journalized_id', $issue->id)
            ->where('journalized_type', 'Issue')
            ->delete();
            
        CustomValuesModel::where('customized_type', 'Issue')
            ->where('customized_id', $issue->id)
            ->delete();
            
        WatchersModel::where('watchable_type', 'Issue')
            ->where('watchable_id', $issue->id)
            ->delete();
            
        IssueAssignedModel::where('issue_id', $issue->id)->delete();
        
        AttachmentModel::where('container_type', 'Issue')
            ->where('container_id', $issue->id)
            ->delete();
    }

    /**
     * 获取默认状态ID
     */
    protected function getDefaultStatusId(int $trackerId): int
    {
        // 返回默认状态ID，通常是"新建"状态
        return 1;
    }

    /**
     * 获取默认优先级ID
     */
    protected function getDefaultPriorityId(): int
    {
        // 返回默认优先级ID，通常是"普通"
        return 2;
    }
    
    /**
     * 获取应该接收事项通知的BI用户ID列表（根据Redmine通知机制）
     * 兼容性方法，用于其他可能调用此方法的地方
     */
    protected function getProjectMemberBiUserIds(int $projectId): array
    {
        // 为了兼容性，创建一个临时的issue对象
        // 这个方法主要用于项目级通知，不涉及具体的事项关系
        $tempIssue = new IssueModel();
        $tempIssue->project_id = $projectId;
        $tempIssue->author_id = null;
        $tempIssue->assigned_to_id = null;
        $tempIssue->id = 0;
        
        return $this->getIssueNotificationUserIds($tempIssue);
    }
} 