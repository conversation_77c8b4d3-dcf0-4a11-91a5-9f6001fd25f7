<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/8/23 下午3:20
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\Project;

use App\Constants\FlowCode;
use App\Constants\ProductCode;
use App\Constants\ProjectCode;
use App\Constants\StatusCode;
use App\Core\Services\Notice\NoticeService;
use App\Core\Services\TchipBi\ProductService;
use App\Core\Utils\FlowUtils;
use App\Core\Utils\Log;
use App\Core\Utils\TimeUtils;
use App\Exception\AppException;
use App\Model\Redmine\AttachmentModel;
use App\Model\Redmine\CategoryModel;
use App\Model\Redmine\CustomFieldsModel;
use App\Model\Redmine\FlowModel;
use App\Model\Redmine\FlowTemplateModel;
use App\Model\Redmine\IssueClassModel;
use App\Model\Redmine\ProductModel;
use App\Model\Redmine\ProjectLabelMapModel;
use App\Model\Redmine\ProjectLabelModel;
use App\Model\Redmine\ProjectsInfoModel;
use App\Model\TchipBi\UserModel;
use App\Model\TchipBi\UserThirdModel;
use DateTime;
use Hyperf\Context\Context;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use App\Model\Redmine\ProjectModel;
use App\Model\Redmine\IssueModel;
use function Symfony\Component\String\b;

class FlowService extends \App\Core\Services\BusinessService
{
    /**
     * @Inject()
     * @var FlowModel
     */
    protected $model;

    public function getOverView($id)
    {
        $overView = $this->model::query()->find($id);
        $overView = $overView ? $overView->toArray() : [];
        if (!empty($overView['nodes']) && is_array($overView['nodes']) && count($overView['nodes']) > 0) {
            $flowIssueIds = array_column(array_column($overView['nodes'], 'properties'), 'issue_id');
            $issues = IssueModel::query()->with(['issueStatus', 'customFields', 'issueAssigned', 'childIssue'])->whereIn('id', $flowIssueIds)->get()->toArray();
            // 遍历 $issues 数组，添加 assigned 字段
            foreach ($issues as &$issue) {
                if (!empty($issue['issue_assigned'])) {
                    // 提取 issue_assigned 数组中的 id 值
                    $issue['assigned'] = array_column($issue['issue_assigned'], 'user_id');
                } else {
                    // 若 issue_assigned 为空，则 assigned 也为空数组
                    $issue['assigned'] = [];
                }
            }
            $issues = array_column($issues, null, 'id');
            foreach ($overView['nodes'] as &$node) {
                if (!empty($issues[$node['properties']['issue_id']])) {
                    $node['properties']['issue'] = $issues[$node['properties']['issue_id']];
                    $node['properties']['issue']['name'] = $node['properties']['issue']['subject'];
                }
            }
        }
        return $overView;
    }

    public function doEdit(int $id, array $values)
    {

        // 20250307 检测到产品类型为 ‘思特森产品’，为对应节点生成子任务
        $isStationProductForChildTask =  false;
        if (!empty($values['isStationProduct']))  {
            $isStationProductForChildTask = $values['isStationProduct'];
            unset($values['isStationProduct']);
        }

        $isStationProduct = $isStationProductForChildTask;


        // 20250317 检测是否使用的是自定义html节点，以决定生成的事项不使用层级关系
        $isHtmlNode = false;
        if ( !empty($values['nodes']) && is_array($values['nodes']) && $values['nodes'][0]['type'] == 'ResizableHtml') {
            $isHtmlNode = true;
        }

        $issueClassList = make(IssueClassModel::class)::query()
            ->where('project_id', $values['container_id'])
            ->pluck('id', 'name')
            ->toArray();


        $productLabels = make(ProjectLabelModel::class)::query()
            ->where('project_id', $values['container_id'])
            ->get(['id', 'name', 'text_color', 'background_color']) // 先获取数据
            ->toArray();

        $productLabelIdToNameMap = array_column($productLabels, null, 'id'); // id => []
        $productLabelNameToIdMap = array_column($productLabels, null, 'name'); // name => []

        $issueLabelMap = [];
        $issueClassSum = []; // 用以自动选择产品进行的阶段

        $issueService = make(\App\Core\Services\Project\IssueService::class);
        $attachmentService = make(\App\Core\Services\Project\AttachmentService::class);
        if (!empty($values['nodes']) && is_array($values['nodes'])) {
            if ($id) {
                $this->flowNodeNotice($id, $values['nodes'], $values['container_id']);
            }
            //$id && $this->flowNodeNotice($id, $values['nodes']);
            $nodesMap = array_column($values['nodes'], null, 'id');
            foreach ($values['nodes'] as &$node) {
                // 判断是否加入node的层级
                if (empty($node['properties']['level'])) {
                    if (in_array($node['text']['value'], $this->model::$topNodesName)) {
                        $node['properties']['level'] = 1;
                    } else {
                        // 1级条件。在1级后面，且没有source的
                        if (FlowUtils::isFirstNodeNext($node['id'], $values['edges'] ?? [], $nodesMap) && FlowUtils::isSourceNode($node['id'], $values['edges'] ?? [])) {
                            $node['properties']['level'] = 1;
                        } else {
                            $node['properties']['level'] = 2;
                        }
                    }
                }
                // 根据连接确定issue的层级
                // if (!empty($values['edges'])) {
                //     foreach ($values['edges'] as $edge) {
                //         if ($edge['targetNodeId'] == $node['id'] && (!empty($node['properties']['level']) && $node['properties']['level'] == 2 || !in_array($node['text']['value'], $this->model::$topNodesName))) {
                //             $assignNode = $this->getNode($edge['sourceNodeId'], $values['nodes']);
                //             if (!empty($assignNode['properties']['issue_id'])) {
                //                 $node['properties']['issue']['parent_id'] = $assignNode['properties']['issue_id'];
                //             }
                //         }
                //     }
                // }

                if ($node['type'] == 'ResizableHtml') {
                    $isStationProduct = true;
                }

                // 编辑节点时可能存在issue数据
                if (!empty($node['properties']['issue_id'])) {
                    if (!empty($node['properties']['issue'])) {
                        // 20250329流程节点需要自动推进产品的阶段状态 如果节点属于进行中
                        if ($node['properties']['issue']['status_id'] == FlowCode::progressNodeStatusTextToIdMap['进行中']) {
                            $issueClassSum[] = $node['properties']['issue']['class_id'];
                        }

                        $issueInfo = IssueModel::query()->find($node['properties']['issue_id']);
                        if ($issueInfo) {
                            // 解决重复提交时字段不匹配导致更新失败
                            // $node['properties']['issue']['updated_on'] = $issueInfo->updated_on;
                            $node['properties']['issue']['lock_version'] = $issueInfo->lock_version;
                        }

                        // 20250315 临时解决一个问题 ‘无法将一个打开的问题关联至一个被关闭的父任务’
                        // 仅当 issue 和 parent_id 存在时才执行 unset
                        if (!empty($node['properties']['issue']) && array_key_exists('parent_id', $node['properties']['issue'])) {
                            unset($node['properties']['issue']['parent_id']);
                        }

                        $issueService->doEdit($node['properties']['issue_id'], $node['properties']['issue'], false);
                        unset($node['properties']['issue']);
                    }
                    // $node['properties']['status'] = $this->getNodeStatus($node['properties']);
                } else {
                    // 2不存在issueId时创建事项
                    $issueData = $this->initIssueData($values, $node, $issueClassList);
                    if (empty($issueData['author_id'])) {
                        return false;
                    }

                    // 20250318 思特森产品 首个节点‘产品立项’初始默认需要设置为进行中
                    if (!empty($node['properties']['issue']['subject']) && $node['properties']['issue']['subject'] == '项目立项') {
                        $issueData['status_id'] = 38; // 进行中状态
                    }
                    // 解决‘指派给 是无效的’
                    if (!isset($issueData['assigned_to_id']) && ($issueData['assigned_to_id'] == 0)) {
                        $issueData['assigned_to_id'] = null;
                    }

                    $newIssue = $issueService->doEdit(0, $issueData);
                    $newIssueId = $newIssue->id ?? ($newIssue['issue']['id'] ?? null);
                    if ($newIssueId) {
                        // 这里接口可能无法直接保存状态为进行中，或者已完成，所以要单独再保存多一次
                        // $issueService->doEditLocal($node['properties']['issue_id'], ['status_id' => $issueData['status_id']]);
                        IssueModel::query()->where('id', $newIssueId)->update(['status_id' => $issueData['status_id']]);
                        // 创建完事项后，需要把ID赋给当前节点
                        $node['properties']['issue_id'] = $newIssueId;
                        // 不再按照时间更新节点状态
                        // if (!empty($issueData['start_date'])) {
                        //     if (!empty($issueData['due_date'])) {
                        //         $effeDay = TimeUtils::getTimeNum($issueData['due_date'], date('Y-m-d'));
                        //         $node['properties']['day_date'] = TimeUtils::getTimeNum($issueData['due_date'], $issueData['start_date']);
                        //         if ($effeDay < 0) {
                        //             $node['properties']['status'] = ProjectCode::PROJECT_FLOW_DOING;
                        //         }
                        //     }
                        // }

                        // 20250318 思特森产品添加默认标签
                        if (!empty($productLabelNameToIdMap)) {
                            if (!empty($node['properties']['label']['customLabels'])) {
                                foreach ($node['properties']['label']['customLabels'] as &$customLabel) {
                                    if (!empty($customLabel['options'])) {
                                        $options = $customLabel['options'] ?? [];
                                        $labelId = $customLabel['id'] ?? 0;
                                        $labelKeywords = $options[$labelId]['keywords'] ?? '';
                                        if (!empty($labelKeywords)) {
                                            $productlabelId = $productLabelNameToIdMap[$labelKeywords]['id'];
                                            $customLabel['id'] = $productlabelId;
                                            $customLabel['project_label_id'] = $productlabelId;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                //// 处理附件
                //if (!empty($node['properties']['attachment'])) {
                //    foreach ($node['properties']['attachment'] as &$attachment) {
                //        $attachment['container_id'] = $node['properties']['issue_id'];
                //        $attachment['container_type'] = 'Issue';
                //        $attachmentService->doEdit($attachment['id'], $attachment);
                //    }
                //}
                // 处理附件
                if (!empty($node['properties']['attachment'])) {
                    foreach ($node['properties']['attachment'] as &$attachment) {
                        if (empty($attachment['id'])) {
                            continue; // 跳过无效附件
                        }
                        $attachment['container_id'] = $node['properties']['issue_id'];
                        $attachment['container_type'] = 'Issue';
                        try {
                            $attachmentService->doEdit($attachment['id'], $attachment);
                        } catch (\Exception $e) {
                            //throw new AppException(StatusCode::ERR_SERVER, '附件有问题');
                        }
                    }
                }

            } // foreach结束

            // 查看有哪些状态(class_id)是激活的，方便自动自动自动复制给产品一个状态 (思特森产品）
            if ($isStationProduct) {
                $issueClassSum = array_unique($issueClassSum);
                $issueClassNameList = make(IssueClassModel::class)::query()
                    ->whereIn('id', $issueClassSum)
                    ->pluck('name')
                    ->toArray();
                $highestPriorityName = null;
                $stationIssueClass = array_reverse(FlowCode::stationIssueClass);
                foreach ( $stationIssueClass as $className) {
                    if (in_array($className, $issueClassNameList)) {
                        $highestPriorityName = $className;
                        break;
                    }
                }
                $highestPriorityName = $highestPriorityName == '立项' ? '产品立项' : $highestPriorityName;
                $this->doEditProductStatus($values['container_id'], $highestPriorityName);
            }

            // 重新定义节点issue的屋级关系
            // 更新nodes后重新定义nodesMap
            $nodesMap = array_column($values['nodes'], null, 'id');
            if (!empty($values['edges']) && count($values['edges']) > 0) {
                foreach ($values['nodes'] as $vNode) {
                    if (!empty($node['properties']['level']) && $vNode['properties']['level'] > 1) {
                        $issue = null;
                        $issue = IssueModel::query()->find($vNode['properties']['issue_id']);

                        if ($issue && empty($issue->parent_id) && !$isHtmlNode) {
                            $parentNode = FlowUtils::getParentNode($vNode['id'], $values['edges'] ?? [], $nodesMap);
                            if (!empty($parentNode['properties']['issue_id'])) {
                                $issue->parent_id = $parentNode['properties']['issue_id'];
                                $issue->update();
                            }
                        }
                    }
                }
            }

            // 20250318 绑定标签关系填写project_label_map表
            foreach ($values['nodes'] as &$vNode) {
                if (!empty($productLabelNameToIdMap) && !empty($vNode['properties']['label']['customLabels'])) {
                    // 获取当前节点的 issueId
                    $issueId = $vNode['properties']['issue_id'];

                    // 遍历 customLabels，收集 project_label_id
                    foreach ($vNode['properties']['label']['customLabels'] as $customLabel) {
                        if (isset($customLabel['project_label_id'])) {
                            $projectLabelId = $customLabel['project_label_id'];

                            // 将 project_label_id 存入对应 issueId 的数组中
                            $issueLabelMap[$issueId][] = $projectLabelId;
                        }
                    }
                }

                // 20250320 处理多选的标签
                if (!empty($productLabelIdToNameMap) && !empty($vNode['properties']['label']['customLabelIds'])) {
                    $customLabelIds = $vNode['properties']['label']['customLabelIds'];
                    $labelInfo = [
                        'id' => null,
                        'project_label_id' => null,
                        'keywords' => '',
                        'name' => '',
                        'color' => '',
                        'options' => [],
                    ];
                    // options存在表示作为从模板中新创建,需要通过关键字映射到已创建好的默认label
                    if (!empty($vNode['properties']['label']['customLabels'][0]['options'])) {
                        $defaultLabelOptions = $vNode['properties']['label']['customLabels'][0]['options'];
                        if (!empty($defaultLabelOptions)) {
                            $vNode['properties']['label']['customLabels'] = [];
                            foreach ($customLabelIds as $customLabelId) {
                                $tempInfo = $labelInfo;
                                $tempInfo['project_label_id'] = $productLabelNameToIdMap[$defaultLabelOptions[$customLabelId]['keywords']]['id'];
                                $tempInfo['id'] = $productLabelNameToIdMap[$defaultLabelOptions[$customLabelId]['keywords']]['id'];
                                $tempInfo['keywords'] = $productLabelNameToIdMap[$defaultLabelOptions[$customLabelId]['keywords']]['name'];
                                $tempInfo['name'] = $productLabelNameToIdMap[$defaultLabelOptions[$customLabelId]['keywords']]['name'];
                                $tempInfo['color'] = $productLabelNameToIdMap[$defaultLabelOptions[$customLabelId]['keywords']]['text_color'];
                                $tempInfo['text_color'] = $productLabelNameToIdMap[$defaultLabelOptions[$customLabelId]['keywords']]['text_color'];
                                $vNode['properties']['label']['customLabels'][] = $tempInfo;
                            }
                        }
                    } else {
                        $vNode['properties']['label']['customLabels'] = [];
                        foreach ($customLabelIds as $customLabelId) {
                            $tempInfo = $labelInfo;
                            $tempInfo['project_label_id'] = $customLabelId;
                            $tempInfo['id'] = $customLabelId;
                            $tempInfo['keywords'] = $productLabelIdToNameMap[$customLabelId]['name'];
                            $tempInfo['name'] = $productLabelIdToNameMap[$customLabelId]['name'];
                            $tempInfo['color'] = $productLabelIdToNameMap[$customLabelId]['text_color'];
                            $tempInfo['text_color'] = $productLabelIdToNameMap[$customLabelId]['text_color'];
                            $vNode['properties']['label']['customLabels'][] = $tempInfo;
                        }
                    }

                }

            }

        }

        // 20250319 写入label map表记录节点事项与 label id关联关系
        foreach ($issueLabelMap as $k => $labelIds) {
            foreach ($labelIds as $labelId) {
                // 检查是否已有相同的记录
                $exists = ProjectLabelMapModel::query()
                    ->where('field_name', 'issue_id')
                    ->where('field_value', $issueId)
                    ->where('label_id', $labelId)
                    ->where('from', 'ProgressNode')
                    ->exists();

                // 如果不存在，则写入数据库
                if (!$exists) {
                    make(ProjectLabelService::class)->doMapEdit(-1, [
                        'field_name' => 'issue_id',
                        'field_value' => $issueId,
                        'label_id' => $labelId,
                        'from' => 'ProgressNode'
                    ]);
                }
            }
        }


        if ($id > 0) {
            $row = $this->model::query()->find($id);
            if (!$row) {
                throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
            }

            if (empty($values['delete_node_id'])) {
                $beforeRow = $row->toArray();
                $beforeIssues = array_column($beforeRow['nodes'], 'properties');
                $beforeIssues = array_column($beforeIssues, 'issue_id');
                $afterIssues = array_column($values['nodes'], 'properties');
                $afterIssues = array_column($afterIssues, 'issue_id');
                $values['delete_node_id'] =array_diff($beforeIssues, $afterIssues);
            }

            // 删除已删除的节点事项
            if (!empty($values['delete_node_id'])) {
                $values['delete_node_id'] = is_array($values['delete_node_id']) ? $values['delete_node_id'] : explode(',', $values['delete_node_id']);
                foreach ($values['delete_node_id'] as $value) {
                    $issueService->doDelete( (int) $value );
                }
            }
            $result = $row->update($values);
        } else {
            if (empty($values['name'])) {
                $values['flow_type'] = $values['flow_type'] ?? 'project';
                switch ($values['flow_type']) {
                    case 'project' :
                        $project = ProjectModel::query()->where('id', $values['container_id'])->first();
                        $values['name'] = $project->name . '流程';
                        break;
                    case 'issue' :
                        $project = IssueModel::query()->where('id', $values['container_id'])->first();
                        $values['name'] = $project->subject . '流程';
                        break;
                }
            }
            $result = $this->model::query()->create($values);
            if ($result->id) {
                $this->addDefaultStationTask($values['nodes'], $result->id);
            }
        }

        if (!empty($values['nodes']) && is_array($values['nodes']) && $isStationProductForChildTask && $id) {
            $this->addDefaultStationTask($values['nodes'], $id);
        }

        return $result;
    }

    public function doDelete($ids): int
    {
        DB::connection('tchip_redmine')->beginTransaction();
        try {
            $rows = $this->model::query()->whereIn('id', $ids)->get()->toArray();
            // 删除产品流程图需要删除之前创建的一些事项
            if ($rows[0]['flow_type'] == 'project') {
                foreach ($rows as $row) {
                    make(IssueModel::class)::query()->where('project_id', $row['container_id'])->delete();
                }
            }
            $this->model::query()->whereIn('id', $ids)->delete();
            DB::connection('tchip_redmine')->commit();
            return 1;
        } catch (AppException $e) {
            DB::connection('tchip_redmine')->rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
        return 1;
    }

    public function createDefaultFlow(int $containerId, $flowType = 'project', $productType = 0)
    {
        if ($this->model::query()->where('container_id', $containerId)->where('flow_type', $flowType)->first()) {
            return null;
        }
        $default = FlowTemplateModel::query()
            ->where('is_default', 1);
        if ($productType != 0) {
            // 是否存在对应产品类型的默认模板
            $isExist = FlowTemplateModel::query()->where('default_product_type', '=', $productType)->first();
            if ($isExist) {
                $default = $default->where('default_product_type', '=', $productType);
            } else {
                $default = $default->whereNull('default_product_type');
            }
        }
        $default =  $default->where('flow_type', $flowType)->first();
        if ($default) {
            $default = $default->toArray();
            if ($flowType == 'project' || $flowType == 'product') {
                $project = ProjectModel::query()->find($containerId);
                $name    = $project->name ?? $containerId;
            } else {
                $issue = IssueModel::query()->find($containerId);
                $name  = $issue->subject ?? $containerId;
            }
            // 获取立项时间
            if ($flowType == 'project') {
                $createdDate = ProjectsInfoModel::query()->where('project_id', $containerId)->value('created_date');
                if ($createdDate) {
                    foreach ($default['template']['nodes'] as &$node) {
                        if (empty($node['text']['value'])) {
                            continue;
                        }
                        switch ($node['text']['value']) {
                            case '产品立项':
                                $node['properties']['issue']['start_date'] = $createdDate;
                                $node['properties']['issue']['due_date'] = date('Y-m-d', strtotime("+1 day", strtotime($createdDate)));
                                break;
                            case '研发':
                                $node['properties']['issue']['start_date'] = date('Y-m-d', strtotime("+1 day", strtotime($createdDate)));
                                $node['properties']['issue']['due_date'] = date('Y-m-d', strtotime("+36 day", strtotime($createdDate)));
                                break;
                            case '结构设计':
                            case '硬件设计':
                            case '软件设计':
                            $node['properties']['issue']['start_date'] = date('Y-m-d', strtotime("+1 day", strtotime($createdDate)));
                                break;
                            case '小批量':
                                $node['properties']['issue']['start_date'] = date('Y-m-d', strtotime("+36 day", strtotime($createdDate)));
                                $node['properties']['issue']['due_date'] = date('Y-m-d', strtotime("+64 day", strtotime($createdDate)));
                                break;
                            case '大批量':
                                $node['properties']['issue']['start_date'] = date('Y-m-d', strtotime("+64 day", strtotime($createdDate)));
                                $node['properties']['issue']['due_date'] = date('Y-m-d', strtotime("+92 day", strtotime($createdDate)));
                                break;
                            default:
                                $node['properties']['issue']['start_date'] = $createdDate ?? date('Y-m-d');
                        }
                    }
                }
            }
            $values = [
                'tpl_id'       => $default['id'],
                'container_id' => $containerId,
                'name'         => $name,
                'flow_type'    => $flowType,
                'edges'        => $default['template']['edges'],
                'nodes'        => $default['template']['nodes'],
            ];
            return $this->doEdit(0, $values);
        }
        return null;
    }

    public function addDefaultIssue(int $id, int $containerId, $flowType = 'project')
    {
        $overview = $this->model::query()->find($id);
        if ($overview) {
            $overviewArr = $overview->toArray();
            // 过滤已经存在的节点
            $existIssueIds = array_column(array_column($overviewArr['nodes'], 'properties'), 'issue_id');
            $issues = IssueModel::query()->where('project_id', $containerId)->whereNotIn('id', $existIssueIds)->get();
            $issues = $issues ? $issues->toArray() : [];
            if (count($issues) == 0) return;
            if (empty($overviewArr['nodes'])) {
                $overviewArr['nodes'] = [];
            }
            $x = count($overviewArr['nodes']) > 0 ? $overviewArr['nodes'][count($overviewArr['nodes']) - 1]['x'] : 0;
            // x=0且没有节点时，x保存节点时，且最后一个X=0时
            if (count($overviewArr['nodes']) == 0) {
                $x = 0;
            } else if (count($overviewArr['nodes']) > 0) {
                $x = $overviewArr['nodes'][count($overviewArr['nodes']) - 1]['x'] == 0 ? 160 : $overviewArr['nodes'][count($overviewArr['nodes']) - 1]['x'];
            }

            foreach ($issues as $key => $issue) {
                $x = $x + (($key + 1) * 50);
                // nodes
                $overviewArr['nodes'][] = [
                    'x' => $x,
                    'y' => $overviewArr['nodes'][count($overviewArr['nodes']) - 1]['y'],
                    'id' => $issue['id'],
                    'text' => [
                        'x' => $x,
                        'y' => $overviewArr['nodes'][count($overviewArr['nodes']) - 1]['y'],
                        'value' => $issue['subject'],
                    ],
                    'type' => 'task',
                    'properties' => [
                        'issue' => [
                            'name' => $issue['subject'],
                        ],
                        'status' => $this->getIssueStatus($issue['status_id']),
                        'issue_id' => $issue['id'],
                    ]
                ];

                // edges todo
            }
            $overview->update($overviewArr);
        }
    }

    public function detail(array $filter = [], array $op = [])
    {
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, '', '', '');
        $row = $query->first();
        $row = $row ? $row->toArray() : [];
        if (!empty($row['nodes']) && is_array($row['nodes'])) {
            $properties = array_column($row['nodes'], 'properties');
            $issueIds = array_column($properties, 'issue_id');
            if ($issueIds) {
                $issueService = make(\App\Core\Services\Project\IssueService::class);
                $issueList = $issueService->lists(['id' => implode(',', $issueIds)], ['id' => 'IN']);
                $issueList = is_object($issueList) ? $issueList->toArray() : $issueList;
                $issueList = array_column($issueList, null, 'id');
                foreach ($row['nodes'] as &$node) {
                    if (!empty($issueList[$node['properties']['issue_id']])) {
                        $node['properties']['issue'] = $issueList[$node['properties']['issue_id']];
                    }
                }
            }
        }
        return $row;
    }

    /**
     * 初始化节点的事项数据
     * @param $values array  doEdit提交的主体数据
     * @param $node array 当前node
     * @return array
     */
    public function initIssueData($values, $node = [], $issueClassList = [])
    {
        // 思特森产品添加默认事项分类
        if (!empty($issueClassList)) {
            $options = $node['properties']['label']['phase']['options'] ?? [];
            $phaseId = $node['properties']['label']['phase']['id'] ?? 0;
            if ($options && $phaseId) {
                $node['properties']['issue']['class_id'] = $issueClassList[$options[$phaseId]['keywords']] ?? 0;
            }
        }

        if (isset($node['properties']['status'])) {
            $issueStatus = $this->getIssueStatus($node['properties']['status']);
        } else {
            $issueStatus = 0;
        }

        // 20250113 如果存在 custom_fields 【完成条件】自定义字段填充需要为其补充id
        $customFields = [];
        if (!empty($node['properties']['issue']['custom_fields'])) {
            $customFields = $node['properties']['issue']['custom_fields'];
            $fieldNames = array_column($customFields, 'name');
            $fieldIds = CustomFieldsModel::query()
                ->whereIn('name', $fieldNames)
                ->where('type', 'IssueCustomField')
                ->pluck('id', 'name');
            foreach ($customFields as &$customField) {
                if (empty($customField['id']) && isset($fieldIds[$customField['name']])) {
                    $customField['id'] = $fieldIds[$customField['name']];
                }
            }
            $node['properties']['issue']['custom_fields'] = $customFields;
        }

        if (!empty($node['properties']['issue'])) {
            $issueData = $node['properties']['issue'];
            $issueData['subject'] = $node['text']['value'] ?? $node['properties']['issue']['subject'] ?? '';
            if (empty($issueData['project_id'])) {
                $issueData['project_id'] = $values['container_id'];
            }
            if (empty($issueData['status_id'])) {
                $issueData['status_id'] = $issueStatus;
            }
            if (empty($issueData['tracker_id'])) {
                $issueData['tracker_id'] = 21;
            }
        } else {
            $issueData = [
                // 节点事项
                'tracker_id' => 21, // 21重要事项
                // 项目ID
                'project_id' => $values['container_id'],
                'subject' => $node['text']['value'] ?? $node['properties']['issue']['subject'] ?? '',
                // 状态默认新建
                'status_id' => $issueStatus,
                // 'start_date' => date('Y-m-d'),

                // 20250313
                'custom_fields' => $customFields,
            ];
        }
        if (empty($issueData['start_date']) && !empty($node['text']['value'])) {
            switch ($node['text']['value']) {
                case '产品立项':
                    $issueData['start_date'] = date('Y-m-d');
                    $issueData['due_date'] = date('Y-m-d', strtotime("+1 day"));
                    break;
                case '研发':
                    $issueData['start_date'] = date('Y-m-d', strtotime("+1 day"));
                    $issueData['due_date'] = date('Y-m-d', strtotime("+36 day"));
                    break;
                case '小批量':
                    $issueData['start_date'] = date('Y-m-d', strtotime("+36 day"));
                    $issueData['due_date'] = date('Y-m-d', strtotime("+64 day"));
                    break;
                case '大批量':
                    $issueData['start_date'] = date('Y-m-d', strtotime("+64 day"));
                    $issueData['due_date'] = date('Y-m-d', strtotime("+92 day"));
                    break;
                default:
                    $issueData['start_date'] = date('Y-m-d');
            }
        }

        $issueData['priority_id'] = 3;
        if (Context::get('taskRuntime') == 1) {
            $manager = make(MemberService::class)->getProjectFirstMembers($issueData['project_id'], ProjectCode::PROJECT_ROLE_MANAGER);
            if (!$manager) {
                Log::get()->info(__('common.Project_members_not_exist') . "#{$issueData['project_id']}");
                return;
                // throw new AppException(StatusCode::ERR_SERVER, __('common.Project_members_not_exist') . "#{$issueData['project_id']}");
            }
            $issueData['author_id'] = $manager['user_id'];
        } else {
            $issueData['author_id'] = getRedmineUserId();
        }

        $issueData['assigned_to_id'] = (empty($issueData['assigned_to_id']) || $issueData['assigned_to_id'] == 0) ? null : $issueData['assigned_to_id'];


        return $issueData;
    }

    /**
     * 同步删除事项后，更新node数据
     * @param $issueRow 被删除的issue数据
     * @return void
     */
    public function syncIssueNode(array $issueRow)
    {
        $flow = \App\Model\Redmine\FlowModel::query()->where('container_id', $issueRow['project_id'])->where('flow_type', 'project')->first();
        if ($flow) {
            $flowArray = $flow->toArray();
            $nodeId = null;
            foreach ($flowArray['nodes'] as $key => $node) {
                if (!empty($node['properties']['issue_id']) && $node['properties']['issue_id'] == $issueRow['id']) {
                    unset($flowArray['nodes'][$key]);
                    $nodeId = $node['id'];
                    break;
                }
            }
            if ($nodeId) {
                $flowArray['nodes'] = array_values($flowArray['nodes']);
                foreach ($flowArray['edges'] as $key => $edge) {
                    if ($edge['sourceNodeId'] == $nodeId || $edge['targetNodeId'] == $nodeId ) {
                        unset($flowArray['edges'][$key]);
                    }
                }
                $flowArray['edges'] = array_values($flowArray['edges']);
                $flow->update($flowArray);
            }
        }
    }

    /**
     * flow节点状态换事项状态
     * @param $flowNodeStatus
     * @return int
     */
    public function getIssueStatus($flowNodeStatus)
    {
        $list = [
            1 => 39,// 完成 - 完成
            2 => 38, // 进行中 - 进行中,
            3 => 37, // 未开始 - 未开始,
            4 => 38, // 超时 - 未开始,
            5 => 38, // 限期天 - 未开始,
        ];
        return $list[$flowNodeStatus] ?? 0;
    }


    /**
     * 根据node id 获取node
     * @param $id
     * @param array $nodes
     * @return array
     */
    public function getNode($id, array $nodes) : array
    {
        $result = [];
        foreach ($nodes as $node) {
            if ($node['id'] == $id) {
                $result = $node;
                break;
            }
        }
        return $result;
    }

    /**
     * flow节点状态换事项状态
     * @param $nodeProp $node[properties]
     * @return int
     */
    public function getNodeStatus($nodeProp)
    {
        $status = $nodeProp['status'] ?? FlowCode::STATUS_NOTSTART;
        if (!empty($node['issue']['due_date'])) {
            $dueTime = strtotime($node['issue']['due_date']) - strtotime(date('Y-m-d 00:00:00'));
            if (!empty($node['issue']['start_date'])) {
                $status = FlowCode::STATUS_PROGRESS;
            }
            // 进行中且否超时
            if ($node['issue']['status_id'] == 38 && $dueTime < 0) { // 进行中状态
                $status = FlowCode::STATUS_OVER;
            }
        } else if (!empty($node['issue']['start_date'])) {
            if ($node['issue']['status_id'] == 38) { // 进行中状态
                // 存在开始时间-已开始
                $status = FlowCode::STATUS_PROGRESS;
            }
        }
        return $status;
    }

    /**
     * 重新整理排序流程的顺序
     * @param $nodes
     * @param $edges
     * @return void
     */
    public function handleNodesSort(&$nodes, $edges)
    {
        $nodesIds = array_column($nodes, 'id');
        $position = [];
        $targetNodes = array_column($edges, 'targetNodeId');
        // 1. 先找到第一个节点,第一个节点是没有被当作target的
        $first = FlowUtils::getFirstNode($nodes, $edges);
        $this->nodesSort(1, $first, $edges, $nodesIds, $position);

        foreach ($nodes as $key => &$node) {
            if (!empty($position[$node['id']])) {
                $node['position'] = $position[$node['id']];
            } else {
                $node['position'] = $key;
            }
        }
        $nodes = collect($nodes)->sortBy('position')->values()->all();
    }

    protected function nodesSort($level = 1, $source, $edges, $nodesIds, &$position)
    {
        foreach ($edges as $edge) {
            if ($edge['sourceNodeId'] == $source && in_array($source, $nodesIds)) {
                if (empty($position[$source])) {
                    $position[$source] = $level;
                }
                if (in_array($edge['targetNodeId'], $nodesIds)) {
                    $position[$edge['targetNodeId']] = $level + 1;
                }
                $this->nodesSort($level + 1, $edge['targetNodeId'], $edges, $nodesIds, $position);
            }
        }
    }

    public function syncNodes($containerId = null, $flowType = 'project')
    {
        Context::set('taskRuntime', 1);
        $issueService = make(IssueService::class);
        $tpl = FlowTemplateModel::query()->where('flow_type', 'project')->where('is_default', 1)->first();
        if ($tpl) {
            $tplNodes = $tpl->template['nodes'] ?? [];
            $tplNodesMap = array_column($tplNodes, null, 'id');
            $tplEdges = $tpl->template['edges'] ?? [];
            if ($containerId) {
                $containerId = is_array($containerId) ? $containerId : explode(',', $containerId);
                $flows = $this->model::query()->whereIn('container_id', $containerId)->where('flow_type', $flowType)->get();
            } else {
                $flows = $this->model::get();
            }

            $flows = $flows ? $flows->toArray() : [];
            foreach ($flows as &$flow) {

                if (empty($flow['nodes'])) continue;
                // $flowNodesMap = array_column($flow['nodes'], null, 'id');
                foreach ($flow['nodes'] as $nKey => &$node) {
                    if (empty($node['properties']['level'])) {
                        $flow['nodes'][$nKey]['properties']['level'] = FlowUtils::defNodeLevel($node, $flow['edges'] ?? [], array_column($flow['nodes'], null, 'id'));
                    }
                }
                $flowItemNames = array_column(array_column($flow['nodes'], 'text'), 'value');
                foreach ($tplNodes as $tplNode) {
                    // 如果当前的模板节点的名称不在产品里的，直接添加
                    if (!in_array($tplNode['text']['value'], $flowItemNames)) {
                        $flow['nodes'][] = $tplNode;
                        // 模板节点里的上级节点
                        $tplNodeSource = FlowUtils::getParentNode($tplNode['id'], $tplEdges, $tplNodesMap);
                        $tplEdgeItem = null;
                        if ($tplNodeSource) {
                            foreach ($tplEdges as $tplEdge) {
                                if ($tplEdge['targetNodeId'] == $tplNode['id'] && $tplEdge['sourceNodeId'] == $tplNodeSource['id']) {
                                    $tplEdgeItem = $tplEdge;
                                    break;
                                }
                            }

                            // 模板里存在连线
                            if ($tplEdgeItem) {
                                // 如果模板的上级节点在当前的流程里。需要加入连接
                                foreach ($flow['nodes'] as $flowNodeItem) {
                                    if ($tplNodeSource['text']['value'] == $flowNodeItem['text']['value']) {
                                        // 修改模板中的源ID
                                        $tplEdgeItem['sourceNodeId'] = $flowNodeItem['id'];
                                        $flow['edges'][] = $tplEdgeItem;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                $this->doEdit($flow['id'], $flow);
            }
        }
    }

    /**
     * 检测到为Station类型产品，添加默认的子任务
     * @param array $nodes
     */
    public function addDefaultStationTask(array $nodes = [], int $flowId = 0)
    {
        $issueList = [];
        $nodes = [];
        if ($flowId) {
            $flow = make(FlowModel::class)::query()->find($flowId);
            $nodes = $flow->nodes ?? []; // 依赖模型的 $casts 转换

            // 补充 issues 内容
            $issueIds = array_column(array_column($nodes, 'properties'), 'issue_id');
            $issueList = make(IssueModel::class)::query()
                ->select('id', 'parent_id', 'subject', 'project_id', 'assigned_to_id', 'tracker_id', 'class_id', 'class_pid','priority_id')
                ->whereIn('id', $issueIds)
                ->get()
                ->keyBy('id')
                ->toArray();
        }

        if (empty($issueList)) {
            return [];
        }

        DB::connection('tchip_redmine')->beginTransaction();
        try {
            $issueSubjects = FlowCode::CHILD_ISSUE_SUBJECT_MAP;

            foreach ($nodes as $nKey => $node) {
                $nodeIssue = $issueList[$node['properties']['issue_id']] ?? null;
                if (!$nodeIssue) continue;

                $subjects = $issueSubjects[$nodeIssue['subject']] ?? [];
                if (!empty($subjects)) {
                    $issueDefaultValues = [
                        'project_id' => $nodeIssue['project_id'],
                        'assigned_to_id' => $nodeIssue['assigned_to_id'],
                        'priority_id' => 3,
                        'tracker_id' => $nodeIssue['tracker_id'],
                        'parent_id' => $nodeIssue['id'],
                        'class_id' => $nodeIssue['class_id'],
                        'class_pid' => $nodeIssue['class_pid'],
                    ];
                    $issueDefaultValues['assigned'] = $issueDefaultValues['assigned_to_id'] ? [$issueDefaultValues['assigned_to_id']] : [];

                    foreach ($subjects as $subject) {
                        $issueDefaultValues['subject'] = $subject;
                         make(IssueService::class)->doEdit(-1, $issueDefaultValues);
                    }

                    // 修改子节点数量
                    $this->changeNodeChildrenCount($nodes, $node['id'], count($subjects));
                }
            }

            // 更新数据库
            $row = $this->model::query()->findOrFail($flowId);
            $row->nodes = $nodes; // 依赖模型的 $casts 自动转 JSON
            $row->save();

            DB::connection('tchip_redmine')->commit();
            return $row->nodes;
        } catch (\Exception $e) {
            DB::connection('tchip_redmine')->rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }
    private function changeNodeChildrenCount(array &$nodes, string $nodeId, int $count): void
    {
        foreach ($nodes as &$node) {
            if ($node['id'] === $nodeId) {
                $node['properties']['children_count'] = $count;
                break;
            }
        }
    }

    /**
     * 获取流程节点通知前置处理数据
     *
     * @param mixed $flowId 流程 ID
     * @param array $newNodesValue 新节点数据（数组）
     * @param int $productId 产品 ID
     * @return array 包含 newIssueList 与 oldIssueList，如果数据为空则返回空数组
     */
    public function getFlowNodePreData($flowId, $newNodesValue, $productId)
    {
        if (empty($newNodesValue[0]['id'])) {
            return [];
        }

        // 获取旧节点数据
        $oldNodesValue = $this->model::query()->find($flowId)->nodes;
        // 提取新节点数据中的 issue 字段列表
        $newIssueList = array_column(array_column($newNodesValue, 'properties'), 'issue');
        // 将旧节点数据通过 ProductService 做预处理，获取包含 issue 的数据
        $oldNodesValue = make(\App\Core\Services\Product\ProductService::class)->getNodesIssues($oldNodesValue);
        // 提取旧节点数据中的 issue 字段列表
        $oldIssueList = array_column(array_column($oldNodesValue, 'properties'), 'issue');

        return [
            'newIssueList' => $newIssueList,
            'oldIssueList' => $oldIssueList,
        ];
    }


    /**
     * ○ 节点进行中通知，产品负责人、节点处理人
     * ○ 节点完成通知，产品负责人、所有子任务处理人
     * @param $flowId
     * @return array
     */
    public function flowNodeNotice($flowId, $newNodesValue, $productId, $IssueNewAndOldData = [])
    {
        $preData = $this->getFlowNodePreData($flowId, $newNodesValue, $productId) ;
        $preData = empty($preData) ?  $IssueNewAndOldData : $preData;
        if (empty($preData)) {
            return [];
        }

        $newIssueList = $preData['newIssueList'];
        $oldIssueList = $preData['oldIssueList'];

        $statusMapping = [
            37 => '未开始',
            38 => '进行中',
            39 => '已完成',
        ];

        // 产品信息
        $productInfo = make(ProjectModel::class)::query()
            ->where('id', $productId)
            ->with('projectsInfo')
            ->first()->toArray();

        // 获取产品负责人
        $productManagerUid = $productInfo['projects_info']['product_manager_uid'] ?? null;

        $notifications = [];

        foreach ($newIssueList as $newIssue) {
            $issueId = $newIssue['id'];
            $subject = $newIssue['subject'];
            $newStatus = $newIssue['status_id'];
            //$newDueDate = $newIssue['due_date'] ?? null;
            $assignedTo = $newIssue['assigned'] ?? ($newIssue['assigned_to_id'] ?? null);

            // 在旧数据中查找相同的任务
            $oldIssue = array_filter($oldIssueList, function ($item) use ($issueId) {
                return $item['id'] === $issueId;
            });

            if (!empty($oldIssue)) {
                $oldIssue = reset($oldIssue);
                $oldStatus = $oldIssue['status_id'];

                // 状态变更通知
                if ($oldStatus !== $newStatus) {
                    $oldStatusText = $statusMapping[$oldStatus] ?? '未知状态';
                    $newStatusText = $statusMapping[$newStatus] ?? '未知状态';

                    //$receivers = [$assignedTo, $productManagerUid]; // 加入处理人和产品负责人
                    $receivers = array_merge(
                        is_array($assignedTo) ? $assignedTo : [$assignedTo],
                        is_array($productManagerUid) ? $productManagerUid : [$productManagerUid],
                    );
                    // 去除 null、0，并去重
                    $receivers = array_values(array_filter($receivers, function ($uid) {
                        return is_scalar($uid) && $uid != 0;
                    }));


                    if ($newStatus == 12) {  // 进入进行中
                        // issue-id=19565 节点进行中通知，产品负责人、节点处理人
                        $receivers = make(UserThirdModel::class)::query()
                            ->where('platform', 'redmine')
                            ->whereIn('third_user_id', $receivers)
                            ->pluck('user_id')->toArray();

                        $notifications[] = [
                            'message' => "节点【{$subject}】状态变更：{$oldStatusText} → 进行中",
                            'receivers' => $receivers, // 通知节点处理人
                            'product_id' => $productId,
                            'product_name' => $productInfo['name'],
                        ];
                    } elseif ($newStatus == 13) { // 进入已完成
                        // issue-id=19565 节点完成通知，产品负责人、所有子任务处理人
                        $childIssues = make(IssueModel::class)::query()
                            ->select(['id', 'subject', 'assigned_to_id', 'parent_id', 'project_id'])
                            ->with(['issueAssigned'])
                            ->where('parent_id', $issueId)
                            ->where('project_id', $productId)
                            ->get();

                        $allChildTaskAssigner = $childIssues->flatMap(function ($issue) {
                            return $issue->issueAssigned->pluck('user_id');
                        })->toArray();
                        $allChildTaskAssigner = array_unique($allChildTaskAssigner); // 去重

                        // 汇总通知接收人（节点处理人 + 产品负责人 + 子任务处理人）
                        //$receivers = array_merge([$assignedTo, $productManagerUid], $allChildTaskAssigner);
                        $receivers = array_merge(
                            is_array($assignedTo) ? $assignedTo : [$assignedTo],
                            is_array($productManagerUid) ? $productManagerUid : [$productManagerUid],
                            is_array($allChildTaskAssigner) ? $allChildTaskAssigner : [$allChildTaskAssigner]
                        );


                        // 去除 null、0，并去重
                        $receivers = array_values(array_filter($receivers, function ($uid) {
                            return is_scalar($uid) && $uid != 0;
                        }));
                        $receivers = array_unique($receivers);


                        $receivers = make(UserThirdModel::class)::query()
                            ->where('platform', 'redmine')
                            ->whereIn('third_user_id', $receivers)
                            ->pluck('user_id')->toArray();

                        $notifications[] = [
                            'message' => "节点【{$subject}】状态变更：{$oldStatusText} → 已完成",
                            'receivers' => $receivers, // 通知节点处理人
                            'product_id' => $productId,
                            'product_name' => $productInfo['name'],
                        ];
                    }
                }

            }
        }


        foreach ($notifications as $notification) {
            foreach ($notification['receivers'] as $receiver) {
                make(NoticeService::class)->productFlowNodeStatusNotice($receiver,  $notification);
            }
        }

        return $notifications;
    }

    /**
     * 通知发送-逾期 & 临期(倒数2天) 节点通知：产品负责人、节点处理人、所有子任务处理人
     * @return
     */
    public function productFlowNodeDeadlineNotice()
    {

        // 1.获取所有符合条件的产品
        $allProduct = make(ProjectModel::class)::query()
            ->whereIn('id', function ($query) {
                $query->selectRaw('DISTINCT project_id')
                    ->from('projects_ext')
                    ->where('project_type', 'product_type');
            })
            ->with('projectsInfo')
            ->get()
            ->toArray();

        // 2.产品ID 对应 产品负责人ID（product_manager_uid）
        $allProductIdToManagerMap = [];
        foreach ($allProduct as $product) {
            if (
                !empty($product['projects_info']) &&
                !empty($product['projects_info']['product_manager_uid']) &&
                $product['projects_info']['product_manager_uid'] !== 0
            ) {
                $allProductIdToManagerMap[$product['id']] = $product['projects_info']['product_manager_uid'];
            }
        }


        $productIds = array_column($allProduct, 'id'); // 获取所有 product_id
        $productIdToNameMap = array_column($allProduct, 'name', 'id'); // product_id 对应 name

        // 3.获取所有流程节点数据
        $allFlowNode = make(FlowModel::class)::query()
            ->whereIn('container_id',  $productIds)
            ->pluck('nodes', 'container_id') // 获取 container_id 作为键，nodes 作为值
            ->toArray();
        // 产品下对应的所有节点事项ids
        $productIdToIssueIds = [];
        $allIssueIds = []; // 存放所有 issue_id
        foreach ($allFlowNode as $containerId => $nodes) {
            foreach ($nodes as $node) {
                if (isset($node['properties']['issue_id'])) {
                    $productIdToIssueIds[$containerId][] = $node['properties']['issue_id'];
                    $allIssueIds[] = $node['properties']['issue_id']; // 追加到总 issue_id 集合
                }
            }
        }
        // 所有事项信息，其中包含due_date
        $allIssueInfo = make(IssueModel::class)::query()
            ->select(['id', 'subject', 'due_date', 'assigned_to_id', 'status_id', 'project_id'])
            ->whereIn('id', $allIssueIds)
            ->whereNotNull('assigned_to_id')
            ->get()
            ->keyBy('id') // 以 'id' 作为键
            ->toArray();


        // 4.筛选出需要通知的节点,并添加要提示的信息
        $filteredIssues = []; // 筛选出需要通知的节点
        $today = new DateTime(); // 当前日期
        $statusTextMap = FlowCode::progressNodeStatusIdToTextMap;
        foreach ($allIssueInfo as $issueId => $issue) {
            if (!empty($issue['due_date']) && $statusTextMap[$issue['status_id']] !== '已完成') {
                try {
                    $dueDate = new DateTime($issue['due_date']);
                } catch (\Exception $e) {
                    throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
                } // 事项的截止日期
                $interval = $today->diff($dueDate)->days; // 计算日期间隔
                $isOverdue = $dueDate < $today; // 是否逾期
                $isNearDeadline = !$isOverdue && $interval <= 2; // 是否临期（倒数2天）

                if ($isOverdue || $isNearDeadline) {
                    // 构造提示信息
                    if ($dueDate->format('Y-m-d') === $today->format('Y-m-d')) {
                        $notificationMessage = " • 节点【{$issue['subject']}】已到期";
                    } elseif ($isOverdue) {
                        $daysOverdue = $interval; // 逾期天数
                        $notificationMessage = " • 节点【{$issue['subject']}】已逾期{$daysOverdue}天";
                    } elseif ($isNearDeadline) {
                        if ($interval == 0) {
                            $notificationMessage = " • 节点【{$issue['subject']}】还有1天到期";
                        } else {
                            $notificationMessage = " • 节点【{$issue['subject']}】还有{$interval}天到期";
                        }
                    }

                    // 存储到 filteredIssues 中
                    $filteredIssues[$issueId] = $issue;
                    $filteredIssues[$issueId]['is_overdue'] = $isOverdue; // 标记是否逾期
                    $filteredIssues[$issueId]['is_near_deadline'] = $isNearDeadline; // 标记是否临期
                    $filteredIssues[$issueId]['notification_message'] = $notificationMessage; // 存储通知消息
                }
            }
        }


        // 5.筛选存储子任务的处理人
        // 批量获取所有子事项的 id 和 所有处理人id
        $childIssues = make(IssueModel::class)::query()
            ->select(['id', 'assigned_to_id', 'parent_id', 'subject'])
            ->with(['projectText', 'issueAssigned'])
            ->whereIn('parent_id', array_keys($filteredIssues))
            ->whereNotNull('assigned_to_id')
            ->get()
            ->toArray();
        // 直接将子任务的处理人存储到父任务中
        foreach ($childIssues as $childIssue) {
            // 将子任务的处理人存储到父任务的 child_assignees 字段中
            if (isset($filteredIssues[$childIssue['parent_id']])) {
                // 确保父任务存在，并将子任务的所有处理人加到父任务的 'child_assignees' 字段中
                $filteredIssues[$childIssue['parent_id']]['child_assignees'] = array_column( $childIssue['issue_assigned'], 'user_id');
            }
        }
        // 为没有子任务的父任务初始化 'child_assignees' 字段为空数组
        foreach ($filteredIssues as $issueId => $issue) {
            if (!isset($issue['child_assignees'])) {
                $filteredIssues[$issueId]['child_assignees'] = [];
            }
        }

        // 6.初始化分组 *
        $groupedByProject = []; // 目前不用
        $groupedByAssignee = []; // 以处理人id为键的map
        foreach ($filteredIssues as $issueId => $issue) {
            // 分组 by project_id
            $projectId = $issue['project_id'];
            if (!isset($groupedByProject[$projectId])) {
                $groupedByProject[$projectId] = [];
            }

            // 合并 assigned_to_id 和 child_assignees 去重
            $noticers = [$issue['assigned_to_id']];
            if (!empty($issue['child_assignees'])) {
                $noticers = array_merge($noticers, $issue['child_assignees']);
            }
            // 如果该项目存在产品负责人，则添加进 noticers
            if (isset($allProductIdToManagerMap[$projectId]) && !empty($allProductIdToManagerMap[$projectId])) {
                $noticers[] = $allProductIdToManagerMap[$projectId];
            }
            $noticers = array_values(array_unique($noticers));  // 去重
            $noticers = array_filter($noticers, function ($value) {
                return !is_null($value) && $value !== '' && is_numeric($value);
            });

            // 以项目分组，并将合并后的处理人列表添加到每个项目组
            $groupedByProject[$projectId][] = [
                'issue_id' => $issue['id'],
                'subject' => $issue['subject'],
                'project_id' => $issue['project_id'],
                'due_date' => $issue['due_date'],
                'assigned_to_id' => $issue['assigned_to_id'], // 存储原始的 assigned_to_id
                'noticers' => $noticers, // 合并后的提醒人列表
                'status_id' => $issue['status_id'],
                'is_overdue' => $issue['is_overdue'],
                'is_near_deadline' => $issue['is_near_deadline'],
                'child_assignees' => $issue['child_assignees'],
                'notification_message' => $issue['notification_message'],
            ];

            // 分组 by assignees (用implode将assignees数组转为字符串做为分组键)
            foreach ($noticers as $assigneeId) {
                $noticersKey = $assigneeId;  // 直接使用assignee_id作为分组键
                if (!isset($groupedByAssignee[$noticersKey])) {
                    $groupedByAssignee[$noticersKey] = [];
                }

                // 同样处理子任务的处理人合并去重
                $groupedByAssignee[$noticersKey][] = [
                    'issue_id' => $issue['id'],
                    'subject' => $issue['subject'],
                    'project_id' => $issue['project_id'],
                    'due_date' => $issue['due_date'],
                    'assigned_to_id' => $issue['assigned_to_id'],
                    'noticers' => $noticers,  // 存储合并后的提醒人列表
                    'status_id' => $issue['status_id'],
                    'is_overdue' => $issue['is_overdue'],
                    'is_near_deadline' => $issue['is_near_deadline'],
                    'child_assignees' => $issue['child_assignees'],
                    'notification_message' => $issue['notification_message'],
                ];
            }
        }

        // 7.构造通知内容
        $notificationsByAssignee = [];
        foreach ($groupedByAssignee as $assigneeId => $issues) {
            $projects = [];

            foreach ($issues as $issue) {
                $projectId = $issue['project_id'];
                $projectName = $productIdToNameMap[$projectId] ?? "未知产品";

                if (!isset($projects[$projectId])) {
                    $projects[$projectId] = [
                        'project_name' => $projectName,
                        'messages' => []
                    ];
                }

                $projects[$projectId]['messages'][] = $issue['notification_message'];
            }

            $projectCount = count($projects);
            $currentIndex = 0;
            // 构造最终的通知文本
            $notificationText = "";

            foreach ($projects as $project) {
                $currentIndex++;

                // 产品标题加上装饰
                $notificationText .= " 产品【{$project['project_name']}】 \n\n";

                foreach ($project['messages'] as $message) {
                    $notificationText .= "{$message}\n"; // 在每条消息前加标记
                }

                $notificationText .= "\n"; // 每个项目之间加空行

                // 仅在不是最后一个项目时添加分隔符
                if ($currentIndex < $projectCount) {
                    $notificationText .= "——————————————\n\n";
                }
            }

            // 去掉末尾多余的换行
            $notificationsByAssignee[$assigneeId] = trim($notificationText);

        }

        // 8.获取redmine用户id到bi用户id的映射
        $assigneeIds = array_keys($notificationsByAssignee);
        $redmineUserIdToBiUserIdMap = make(UserThirdModel::class)::query()
            ->where('platform', 'redmine')
            ->whereIn('third_user_id', $assigneeIds)
            ->pluck('user_id', 'third_user_id')->toArray();


        // 9.发送通知
        foreach ($notificationsByAssignee as $noticer => $notificationText) {
            //make(NoticeService::class)->productFlowNodeDeadlineNotice(232, ['text' => $notificationText]);
            make(NoticeService::class)->productFlowNodeDeadlineNotice($redmineUserIdToBiUserIdMap[$noticer], ['text' => $notificationText]);
        }

        return $notificationsByAssignee;
    }


    /**
     * 通知发送-逾期 & 临期(倒数2天) 节点通知：产品负责人、节点处理人、所有子任务处理人 邮件发送
     * @return
     */
    public function productFlowNodeDeadlineNoticeEmail()
    {

        // 1.获取所有符合条件的产品
        $allProduct = make(ProjectModel::class)::query()
            ->whereIn('id', function ($query) {
                $query->selectRaw('DISTINCT project_id')
                    ->from('projects_ext')
                    ->where('project_type', 'product_type');
            })
            ->with('projectsInfo')
            ->get()
            ->toArray();

        // 2.产品ID 对应 产品负责人ID（product_manager_uid） // 20250403 需要过滤掉暂停的
        $allProductIdToManagerMap = [];
        $filteredProduct = []; // 存储过滤后的产品数据
        foreach ($allProduct as $product) {
            if (
                !empty($product['projects_info']) &&
                !empty($product['projects_info']['product_manager_uid']) &&
                $product['projects_info']['product_manager_uid'] !== 0
            ) {
                $allProductIdToManagerMap[$product['id']] = $product['projects_info']['product_manager_uid'];
            }
            if ( !empty($product['projects_info']) &&
                !empty($product['projects_info']['product_status']) &&
                $product['projects_info']['product_status'] != ProductCode::PRODUCT_STATUS_PAUSE) {
                $filteredProduct[] = $product;
            }
        }


        // 3.获取所有流程节点数据
        $productIds = array_column($filteredProduct, 'id'); // 获取所有 product_id
        $allFlowNode = make(FlowModel::class)::query()
            ->whereIn('container_id',  $productIds)
            ->pluck('nodes', 'container_id') // 获取 container_id 作为键，nodes 作为值
            ->toArray();
        // 产品下对应的所有节点事项ids
        $productIdToIssueIds = [];
        $allIssueIds = []; // 存放所有 issue_id
        foreach ($allFlowNode as $containerId => $nodes) {
            foreach ($nodes as $node) {
                if (isset($node['properties']['issue_id'])) {
                    $productIdToIssueIds[$containerId][] = $node['properties']['issue_id'];
                    $allIssueIds[] = $node['properties']['issue_id']; // 追加到总 issue_id 集合
                }
            }
        }
        // 所有事项信息，其中包含due_date
        $allIssueInfo = make(IssueModel::class)::query()
            ->select(['id', 'subject', 'due_date', 'assigned_to_id', 'status_id', 'project_id', 'due_date', 'closed_on', 'start_date'])
            ->with(['projectText', 'issueStatus', 'issueAssigned'])
            ->whereIn('id', $allIssueIds)
            ->whereNotNull('assigned_to_id')
            ->get()
            ->keyBy('id') // 以 'id' 作为键
            ->toArray();


        // 4.筛选出需要通知的节点,并添加要提示的信息
        $filteredIssues = []; // 筛选出需要通知的节点
        $today = new DateTime(); // 当前日期
        $statusTextMap = FlowCode::progressNodeStatusIdToTextMap;
        foreach ($allIssueInfo as $issueId => $issue) {
            if (!empty($issue['due_date']) && $statusTextMap[$issue['status_id']] !== '已完成') {
                try {
                    $dueDate = new DateTime($issue['due_date']);
                    $startDate = new DateTime($issue['start_date']); // 获取开始日期
                } catch (\Exception $e) {
                    throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
                } // 事项的截止日期
                $interval = $today->diff($dueDate)->days + 1; // 计算日期间隔 （+1包含 dueDate 当天）
                $isOverdue = $dueDate < $today; // 是否逾期
                $isNearDeadline = !$isOverdue && $interval <= 2; // 是否临期（倒数2天）

                // 20250331 判断是否在最后20%的时间段内
                // 计算事项的总持续时间
                if ($interval > 5) {
                    // 计算事项的总持续时间（包含 dueDate 当天）
                    $totalDays = $startDate->diff($dueDate)->days + 1;
                    // 计算最后20%的时间段（向上取整）
                    $twentyPercentDays = ceil($totalDays * 0.2);
                    // 计算"最后20%"的开始时间点
                    $twentyPercentStartDate = (clone $dueDate)->modify("-{$twentyPercentDays} days");
                    // 当前日期是否在最后20%的时间段内
                    $isInLast20Percent = ($today >= $twentyPercentStartDate && $today <= $dueDate);
                }

                $overdueMessage = '';
                if ($isOverdue || $isNearDeadline || $isInLast20Percent) {
                    // 构造提示信息
                    if ($dueDate->format('Y-m-d') === $today->format('Y-m-d')) {
                        $overdueMessage = "已到期";
                    } elseif ($isOverdue) {
                        $daysOverdue = $interval; // 逾期天数
                        $overdueMessage = "已逾期{$daysOverdue}天";
                    } elseif ($isNearDeadline) {
                        if ($interval == 0) {
                            $overdueMessage = "节点还有1天到期";
                        } else {
                            $overdueMessage = "节点还有{$interval}天到期";
                        }
                    } elseif ($isInLast20Percent) {
                        // 显示剩余天数

                        $remainingDays = $dueDate->diff($today)->days;
                        $overdueMessage = "节点还有{$remainingDays}天到期";
                    }

                    $filteredIssues[$issueId]['info'] = $issue; // 事项信息
                    $filteredIssues[$issueId]['subject'] = $issue['subject']; // 标题
                    $filteredIssues[$issueId]['project_name'] = $issue['project_text']['name']; // 项目名称
                    $filteredIssues[$issueId]['overdue_message'] = $overdueMessage; //// 逾期信息
                    $filteredIssues[$issueId]['status_text'] = $statusTextMap[$issue['status_id']]; //// 状态文本

                }
            }
        }


        // 5.筛选存储子任务的处理人
        // 批量获取所有子事项的 id 和 assigned_to_id
        $childIssues = make(IssueModel::class)::query()
            ->select(['id', 'assigned_to_id', 'parent_id', 'subject'])
            ->with(['projectText', 'issueAssigned'])
            ->whereIn('parent_id', array_keys($filteredIssues))
            ->whereNotNull('assigned_to_id')
            ->get()
            ->toArray();
        // 直接将子任务的处理人存储到父任务中
        foreach ($childIssues as $childIssue) {
            // 将子任务的处理人存储到父任务的 child_assignees 字段中
            if (isset($filteredIssues[$childIssue['parent_id']])) {
                // 确保父任务存在，并将子任务的处理人加到父任务的 'child_assignees' 字段中
                $filteredIssues[$childIssue['parent_id']]['child_assignees'] = array_column( $childIssue['issue_assigned'], 'user_id');
            }
        }
        // 为没有子任务的父任务初始化 'child_assignees' 字段为空数组
        foreach ($filteredIssues as $issueId => $issue) {
            if (!isset($issue['child_assignees'])) {
                $filteredIssues[$issueId]['child_assignees'] = [];
            }
        }


        // 6.初始化分组 *
        $groupedByProject = [];
        $groupedByAssignee = [];
        foreach ($filteredIssues as $issueId => $issue) {
            // 分组 by project_id
            $projectId = $issue['info']['project_id'];
            $groupedByProject[$projectId] = $groupedByProject[$projectId] ?? [];

            // 合并 assigned_to_id 和 child_assignees，并去重
            $noticers = array_unique(array_filter([
                $issue['info']['assigned_to_id'] ?? null,
                ...($issue['child_assignees'] ?? [])
            ], fn($value) => $value !== '' && is_numeric($value)));
            if (!empty($allProductIdToManagerMap[$projectId])) {
                $noticers[] = $allProductIdToManagerMap[$projectId]; // 如果该项目存在产品负责人，则固定添加进 noticers
            }
            $noticers = array_values(array_unique($noticers));

            // 分组 by assignees (用implode将assignees数组转为字符串做为分组键)
            foreach ($noticers as $assigneeId) {
                $noticersKey = $assigneeId;  // 直接使用assignee_id作为分组键
                if (!isset($groupedByAssignee[$noticersKey])) {
                    $groupedByAssignee[$noticersKey] = [];
                }

                // 同样处理子任务的处理人合并去重
                $groupedByAssignee[$noticersKey][] = [
                    'id' => $issue['info']['id'],
                    'issue_id' => $issue['info']['id'],
                    'subject' => $issue['info']['subject'],
                    'project_id' => $issue['info']['project_id'],
                    'project_name' => $issue['info']['project_text']['name'],
                    'due_date' => $issue['info']['due_date'],
                    'assigned_to_id' => $issue['info']['assigned_to_id'],
                    'status_id' => $issue['info']['status_id'],
                    'status_text' => $issue['status_text'],
                    'noticers' => $noticers,  // 存储合并后的提醒人列表
                    'child_assignees' => $issue['child_assignees'],
                    'overdue_message' => $issue['overdue_message'],
                ];
            }
        }



        // 7.构造邮件通知内容
        $notificationsByAssignee = [];
        foreach ($groupedByAssignee as $assigneeId => $issues) {

            $notificationText = $this->generateHtmlContent($assigneeId, $issues);
            // 去掉末尾多余的换行
            $notificationsByAssignee[$assigneeId] = $notificationText;

        }


        // 8.获取redmine用户id到bi用户id的映射
        $assigneeIds = array_keys($notificationsByAssignee);
        $redmineUserIdToBiUserIdMap = make(UserThirdModel::class)::query()
            ->where('platform', 'redmine')
            ->whereIn('third_user_id', $assigneeIds)
            ->pluck('user_id', 'third_user_id')->toArray();



        // 9.发送通知
        foreach ($notificationsByAssignee as $noticer => $notificationText) {
            //make(NoticeService::class)->productFlowNodeDeadlineNotice(232, ['text' => $notificationText]);
            make(NoticeService::class)->productFlowNodeDeadlineNotice($redmineUserIdToBiUserIdMap[$noticer], ['text' => $notificationText]);
        }

        return $notificationsByAssignee;
    }

    /**
     * 通知发送-邮件内容
     * @return string
     */
    public function getHtmlEmailContent()
    {
        $tpl = '';
        $htmlTableHeader ='<td class="block" style="padding-bottom: 24px;">
  <table>
      <thead>
      <tr class="table-header" style="padding: 12px 0;
          font-size: 12px;">
          <th style="padding: 12px 0;
              border-bottom: 1px solid #ebebeb;
              color: #818994;
              text-align: left;
              font-weight: normal;">事项标题
          </th>
          <th style="padding: 12px 0;
              border-bottom: 1px solid #ebebeb;
              color: #818994;
              text-align: left;
              font-weight: normal;">优先级
          </th>
          <th style="padding: 12px 0;
              border-bottom: 1px solid #ebebeb;
              color: #818994;
              text-align: left;
              font-weight: normal;">状态
          </th>
          <th style="padding: 12px 0;
              border-bottom: 1px solid #ebebeb;
              color: #818994;
              text-align: left;
              font-weight: normal;">所属项目
          </th>
      </tr>
      </thead>
      <tbody>';;

      $tpl .= $htmlTableHeader;
      return $tpl;
    }

    /**
     * 通知发送-邮件内容
     * @return string
     */
    protected function generateHtmlContent($redmineUserId, $issueList)
    {
        $issueService = make(IssueService::class);
        Context::set('getIssueList', 'task');
        $host = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');
        $tpl = '<tr><td valign="middle" style="line-height:24px;padding: 15px 20px;">
            <table><tbody>';

        $tpl .= '<td class="block" style="padding-bottom: 24px;">
  <table>
      <thead>
      <tr class="table-header" style="padding: 12px 0;
          font-size: 12px;">
          <th style="padding: 12px 0;
              border-bottom: 1px solid #ebebeb;
              color: #818994;
              text-align: left;
              font-weight: normal;">产品
          </th>
          <th style="padding: 12px 0;
              border-bottom: 1px solid #ebebeb;
              color: #818994;
              text-align: left;
              font-weight: normal;">节点
          </th>
          <th style="padding: 12px 0;
              border-bottom: 1px solid #ebebeb;
              color: #818994;
              text-align: left;
              font-weight: normal;">临期&逾期
          </th>
          <th style="padding: 12px 0;
              border-bottom: 1px solid #ebebeb;
              color: #818994;
              text-align: left;
              font-weight: normal;">状态
          </th>

      </tr>
      </thead>
      <tbody>
            </td></tr>';


        foreach ($issueList as $item) {
            $statusText = $this->getStatusHtml($item);
            $overdueText = $this->getOverdueHtml($item['overdue_message']);
            //$projectName = $item['project_name'] ?? ' - ';
            $issueUrl = "{$host}/#/project/detail?project_id={$item['project_id']}&issue_id={$item['id']}";
            $projectUrl = "{$host}/#/project/productDetailsIndex?product_id={$item['project_id']}";
            $projectName = isset($item['project_name'])
                ? "<a href='{$projectUrl}' style='color: #909399; text-decoration: none;'>{$item['project_name']}</a>"
                : ' - ';

            $tpl .= "<tr style='border-bottom: 1px solid #ddd;'>
            <td style='padding: 10px; font-size: 14px; color: #333;'>{$projectName}</td>
            <td style='padding: 10px; font-size: 14px; color: #333;'>{$this->generateTitleHtml($item, $issueUrl)}</td>
            <td style='padding: 10px; font-size: 14px; color: #f00;'>{$overdueText}</td>
            <td style='padding: 10px; font-size: 14px; color: #008000;'>{$statusText}</td>
        </tr>";
        }

        $tpl .= '</tbody></table></td></tr>';
        return $tpl;
    }

    /**
     * 通知发送-邮件标题列内容
     * @return string
     */
    private function generateTitleHtml($item, $issueUrl)
    {
        return "<div class='table-menuItem-title' style='display: flex;
    align-items: center;cursor: pointer; min-width: 408px; max-width: 600px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;'>
        <img  src=\"data:image/svg+xml;base64,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\" />
        <a href='{$issueUrl}' title='{$item['subject']}' style='color: #007bff; text-decoration: none;padding-left: 3px'>
            {$item['subject']}
        </a>
    </div>";
    }

    /**
     * 通知发送-邮件状态列
     * @return string
     */
    private function getStatusHtml($item)
    {
        switch ((int)$item['status_id']) {
            case 37:
                $color = 'color:#909399; background: #f4f4f5';
                break;
            case 38:
                $color = 'color:#fe9900; background: #fdf6ec';
                break;
            default:
                $color = 'color:#18BE6A; background: #e5fcf4';
        }


        return "<div class=\"table-menuItem-status table-menuItem-status_todo\" 
                style=\"width: 48px; height: 20px; margin-right: 16px; text-align: center;
                       line-height: 20px; border-radius: 3px; font-size: 12px; $color\">
                {$item['status_text']}
            </div>";
    }


    /**
     * 通知发送-邮件 是否逾期 列
     * @return string
     */
    private function getOverdueHtml($overdue_message)
    {
        // 判断是否以 "已逾期" 开头
        $isOverdue = strpos($overdue_message, '已逾期') === 0;

        // 根据是否逾期设置不同的样式
        $background = $isOverdue ? '#FF5E4B' : '#ffffff';
        $color = $isOverdue ? '#ffffff' : '#666666';
        //$border = $isOverdue ? 'none' : '1px solid #666666';
        $border = 'none';

        return "<div class='overdue' style='color: $color; background: $background; 
            border-radius: 3px; display: inline-block; font-size: 12px; line-height: 1.5em;
            padding-left: 3px; padding-right: 3px; border: $border;'>
            $overdue_message
        </div>";
    }

    /**
     * 产品状态-修改流程图对应的产品状态
     * 0研发中 1小批量 2大批量 7立项
     * @return string
     */
    public function doEditProductStatus($projectId, $name)
    {
        $status = null;
        switch ($name) {
            case '立项':
                $status = 7;
                break;
            case '研发':
                $status = 0;
                break;
            case '小批量':
                $status = 1;
                break;
            case '大批量':
                $status = 2;
                break;
        }
        if ($status !== null) {
            make(\App\Core\Services\Project\ProjectsInfoService::class)->doEditProjectId($projectId, ['product_status' => $status]);
        }
    }


}