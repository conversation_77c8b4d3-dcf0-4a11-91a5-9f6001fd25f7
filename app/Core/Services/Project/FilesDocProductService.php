<?php
    /**
     * @Copyright T-chip Team.
     * @Date 2024/05/08 10:26
     * <AUTHOR>
     * @Description
     */

    namespace App\Core\Services\Project;

    use App\Constants\DataBaseCode;
    use App\Constants\StatusCode;
    use App\Core\Services\Redmine\RedmineBaseService;
    use App\Core\Utils\Pinyin;
    use App\Core\Utils\Tree;
    use App\Exception\AppException;
    use App\Model\Redmine\CategoryModel;
    use App\Model\Redmine\FilesDocProductModel;
    use App\Model\Redmine\FilesDocProductVersionModel;
    use Hyperf\Database\Exception\QueryException;
    use Hyperf\DbConnection\Db;
    use Hyperf\Di\Annotation\Inject;


    /**
     * 产品资料文件
     */
    class FilesDocProductService extends \App\Core\Services\BusinessService
    {


        /**
         * @Inject()
         * @var FilesDocProductModel
         */
        protected $model;

        /**
         * @Inject()
         * @var FilesDocProductVersionModel
         */
        protected $versionModel;

        public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 100)
        {
            if ($filter['product_id']) {
                $this->checkInitial($filter['product_id']);
            }
            if (empty($filter['open_status'])) {
                $filter['open_status'] = 1; // 默认获取 已启用 的数据
            }

            list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
            $query = $query->with(['version', 'attribute', 'user', 'attachment'])->orderBy($sort, $order)->paginate($limit);
            $result = $query->toArray();

            $minId = ($result && $result['data'] ? min(array_column($result['data'], 'category_id')) : 0) ?? 0;
            $result['data'] = make(Tree::class)->getTreeListV3($result['data'], $minId,  'sort', 'DESC');

            return $result;
        }

        public function doEdit($id, $values)
        {

            $db = Db::connection(DataBaseCode::TCHIP_REDMINE);
            $db->beginTransaction();

            try {
                if ($id === -2) {
                    // 从分类管理处修改了通用的产品资料文件类型配置,
                    //  需要批量插入新的分类
                    $productIds = make(\App\Model\Redmine\ProjectsExtModel::class)
                        ::query()
                        ->where('project_type', 'product_type')
                        ->pluck('project_id')
                        ->toArray();

//                    $existingProductVersion = $this->versionModel::query()
//                            ->whereIn('product_id', $productIds)
//                        ->get();

                    $existingProducts = make(\App\Model\Redmine\FilesDocProductModel::class)
                        ::query()
                        ->whereIn('product_id', $productIds)
                        ->get();


//                    $existingProductVersionIds = $existingProductVersion->pluck('product_id')->unique()->toArray();
                    // 提取已存在的产品ID数组并去重
                    $existingProductIds = $existingProducts->pluck('product_id')->unique()->toArray();


                    // // 20240516 产品资料文件 分类新建时不对未初始化的产品‘全部创建’
//                    // 检查是否有不存在的记录,这部分令它文件版本初始化
//                    $missingVersionIds = array_diff($productIds, $existingProductVersionIds);
//                    if (!empty($missingVersionIds)) {
//                        foreach ($missingVersionIds as $missingVersionId) {
//                            $this->initProductFileVersion($missingVersionId);
//                        }
//                    }
//                    // 检查是否有不存在的记录,这部分令它初始化
//                    $missingIds = array_diff($productIds, $existingProductIds);
//                    if (!empty($missingIds)) {
//                        foreach ($missingIds as $missingId) {
//                            $this->initProductFile($missingId);
//                        }
//                    }
//
//                    // 获取需要插入的新产品ID数组，不需要初始化的（原来有的），更新插入数据
//                    $newProductIds = array_diff($productIds,$missingIds);

                    // 获取这些产品的版本ids
                    $versionIds = $this->versionModel::query()->select(['id', 'product_id'])->get()->toArray();
                    $resultVersionIds = [];
                    foreach ($versionIds as $version) {
                        $product_id = $version['product_id'];
                        $id = $version['id'];
                        if (!isset($resultVersionIds[$product_id])) {
                            // 如果该 product_id 不存在于结果数组中，则创建一个新的数组
                            $resultVersionIds[$product_id] = [];
                        }
                        // 将 id 添加到对应 product_id 的数组中
                        $resultVersionIds[$product_id][] = $id;
                    }

                    // 准备要插入的数据数组
                    $dataToInsert = [];

                    foreach ($existingProductIds as $productId) {
                        // 使用 $values 中的数据，并设置 product_id 字段的值为当前的 $productId
                        foreach ($resultVersionIds[$productId] as $item) {
                            // 新建文件类型分类后，对应产品每一个版本都创建该文件类型分类
                            $data = array_merge($values, ['product_id' => $productId, 'file_version' => $item]);
                            $dataToInsert[] = $data;
                        }
                    }

                    // 执行批量插入
                    $db->table('files_doc_product')->insert($dataToInsert);
                } elseif ($id === -3) {
                    // 分类配置里新增了或修改一个分类，需要批量更新

                    //// 更新文件属性（修改为内部资料等等）
                    //$attr = $this->checkFileAttr($values);
                    //if ($attr) {
                    //    $values['file_attribute'] = $attr;
                    //}

                    $this->model->where('category_id', $values['category_id'])->update($values);
                } elseif ($id > 0) {
                    $query = $this->model::query()->find($id);
                    $query->fill($values);
                    $query->save();
                } else {
                    $this->model::query()->create($values);
                }

                $db->commit();
                return true;
            } catch (QueryException $e) {
                $db->rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        /**
         * 初始化文件属性
         * @param $item
         * @return mixed
         */
        public function checkFileAttr($item)
        {
            switch ($item['file_type']) {
                case '样机测试报告':
                case '整机测试报告':
                case '电气性能测试报告':
                case '底板参考设计':
                case '主板原理图':
                case '3D结构图':
                    // 内部资料
                $fileCategory = make(\App\Model\Redmine\CategoryModel::class)::query()
                    ->select('id', 'name', 'keywords')
                    ->where('pid', '>', 0)
                    ->where('type', '=', 'product_doc_attr')->get()->toArray();

                    $fileCategory = array_column($fileCategory, null, 'keywords');
                    return $fileCategory['neibuziliao']['id'];
                default:
                    return null;
            }
        }

        public function doDelete($ids): int
        {
            try {
                return Db::connection(DataBaseCode::TCHIP_REDMINE)->transaction(function() use ($ids) {
                    $query = $this->model::query()->whereIn('id', $ids);
                    // 返回受影响的行数
                    return $query->delete();
                });
            } catch (\Throwable $e) {
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }


        public function checkInitial($productId)
        {
            $db = Db::connection(DataBaseCode::TCHIP_REDMINE);
            $db->beginTransaction();

            try {
                $product = make(\App\Model\Redmine\ProjectsExtModel::class)->where('project_id', $productId)
                    ->where('project_type', 'product_type')->first();
                $checkExit = $this->model->where('product_id', $productId)->first();

                $checkExitVersion = $this->versionModel->where('product_id', $productId)->first();

                // 新建产品下还未有版本，需要初始化
                if ($product && !$checkExitVersion) {
                    $this->initProductFileVersion($productId);
                }

                // 新建的产品下不会有文件列表，需要初始化
                if ($product && !$checkExit) {
                    $this->initProductFile($productId);
                }

                $db->commit();
            } catch (\Exception $e) {
                $db->rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }

        }

        public function initProductFile($productId, $versionId = null, $previousVersionId = null)
        {
            $db = Db::connection(DataBaseCode::TCHIP_REDMINE);
            $db->beginTransaction();

            try {
                $query = $previousVersionId ?
                    $this->model::query()->select(['file_type as name', 'category_id as id', 'category_pid as pid', 'sort', 'file_attribute' ])
                    ->where('product_id', '=', $productId)
                    ->where('file_version', '=', $previousVersionId)
                    :
                    make(\App\Model\Redmine\CategoryModel::class)::query(true)
                    ->where('type', '=', 'product_doc_type')
                    ->where('pid','!=', 0)
                    ->where('status', '=', 1);


                $fileVersion = $this->versionModel::query()->where('product_id', '=', $productId)->first();
                $fileVersion =  $versionId ?? (!empty($fileVersion) ? $fileVersion->id : null);

                $fileCategory = make(\App\Model\Redmine\CategoryModel::class)::query()
                    ->select('id', 'name', 'keywords')
                    ->where('pid', '>', 0)
                    ->where('type', '=', 'product_doc_attr')->get()->toArray();

                $fileCategory = array_column($fileCategory, null, 'keywords');
                $fileCategoryGroupById = array_column($fileCategory, null, 'id');

                $filteredRecords = $query->get()->toArray();

                // 准备要插入的数据数组
                $dataToInsert = [];
                foreach ($filteredRecords as $item) {
                    //$filtAttr = $item['name'] == '主板原理图' ? $fileCategory['neibuziliao']['id'] : $fileCategory['waifaziliao']['id'];

                    // 根据不同的测试报告和技术开源的名字来判断分类
                    switch ($item['name']) {
                        case '样机测试报告':
                        case '整机测试报告':
                        case '电气性能测试报告':
                        case '底板参考设计':
                        case '主板原理图':
                        case '3D结构图':
                            // 内部资料
                            $filtAttr = $fileCategory['neibuziliao']['id'];
                            break;
                        default:
                            // 外部资料或其他
                            $filtAttr = $fileCategory['waifaziliao']['id'];
                            break;
                    }

                    $values = [
                        'file_type' => $item['name'],
                        'category_id' => $item['id'],
                        'category_pid' => $item['pid'],
                        'sort' => $item['sort'],
                        'file_version' => $fileVersion,
                        'file_attribute' => $item['file_attribute'] ?? ($fileCategoryGroupById[(int)$item['nickname']]['id']?? $filtAttr),
                    ];
                    // 使用 $values 中的数据，并设置 product_id 字段的值为当前的 $productId
                    $data = array_merge($values, ['product_id' => $productId]);
                    $dataToInsert[] = $data;
                }
                // 执行批量插入
                $db->table('files_doc_product')->insert($dataToInsert);
                $db->commit();
            } catch (\Exception $e) {
                $db->rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        public function initProductFileVersion($productId)
        {
            $db = Db::connection(DataBaseCode::TCHIP_REDMINE);
            $db->beginTransaction();

            try {
                $values = [
                    'product_id' => $productId,
                    'version_name' => 'v1.0',
                    'status' => '0',
                ];

                $this->versionModel::query()->create($values);

                $db->commit();
            } catch (\Exception $e) {
                $db->rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        /**
         * 初始化产品文件资料(包含版本创建)
         * @param $productId
         * @return boolean
         */
        public function initProductFilev2($productId)
        {
            $db = Db::connection(DataBaseCode::TCHIP_REDMINE);
            $db->beginTransaction();
            try {
                $values = [
                    'product_id'   => $productId,
                    'version_name' => 'v1.0',
                    'status'       => '0',
                ];

                $resultVersion = $this->versionModel::query()->create($values);
                $this->initProductFile($productId, $resultVersion->id);
                $db->commit();
            } catch (\Exception $e) {
                $db->rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
            return true;
        }

        public function getProductFileVersion($productId)
        {
            return $this->versionModel::query()->select('*')->where('product_id', $productId)
                ->orderBy('sort', 'desc')
                ->orderBy('id', 'desc')
                ->get()->all();
        }

        public function doEditProductFileVersion($id, $values)
        {
            $productId = $values['product_id'];
            $versionExist = $this->versionModel::query()->find($id);
            if ($versionExist) {

                if (!empty($values['sort']) && $values['sort'] == 1000) {
                    $this->versionModel::query()
                        ->where('product_id', $productId)
                        ->update(['sort' => 0]);
                }

                $versionExist->fill($values);
                $versionExist->save();
            } else {
                $db = Db::connection(DataBaseCode::TCHIP_REDMINE);
                $db->beginTransaction();

                try {
                    // 获取上一个版本的id x  获取默认版本的id ✔ （20240528 添加默认版本）
                    $previousVersion = $this->versionModel::query()
                        ->where('product_id', $productId)
                        ->whereIn('sort', [1000, 0])
                        ->orderByRaw("FIELD(sort, 1000, 0)") // 1000 优先于 0
                        ->orderBy('id', 'desc')->first();

                    if (!empty($values['sort']) && $values['sort'] == 1000) {
                        $this->versionModel::query()
                            ->where('product_id', $productId)
                            ->update(['sort' => 0]);
                    }

                    $newVersion = $this->versionModel::query()->create($values);

                    // 以上一个版本所存在的文件作为基准来复制
                    $this->initProductFile($productId, $newVersion->id, $previousVersion->id);

                    $db->commit();
                } catch (\Exception $e) {
                    $db->rollBack();
                    throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
                }
            }

            return 1;
        }

        public function doDeleteProductFileVersion($id)
        {
                try {
                    Db::connection(DataBaseCode::TCHIP_REDMINE)->transaction(function () use ($id) {
                        // 获取要删除的行
                        $version = $this->versionModel::query()->find($id);
                        if (!$version) {
                            throw new AppException(StatusCode::ERR_SERVER, __('common.Not_found'));
                        }

                        // 删除指定行
                        $this->versionModel::query()->where('id', $id)->delete();

                        // 如果删除的行的 sort 字段值为 1000
                        if ($version->sort == 1000) {
                            // 找到与其 product_id 相同的行数据中 id 最大者
                            $newDefaultVersion = $this->versionModel::query()
                                ->where('product_id', $version->product_id)
                                ->orderBy('id', 'desc')
                                ->first();

                            // 设置新默认版本的 sort 字段为 1000
                            if ($newDefaultVersion) {
                                $newDefaultVersion->sort = 1000;
                                $newDefaultVersion->save();
                            }
                        }
                    });
                } catch (\Throwable $e) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.Delete_fail'));
                }
                return true;
            }

    }