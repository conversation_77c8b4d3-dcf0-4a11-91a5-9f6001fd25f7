<?php

namespace App\Core\Services\SnCode;

use App\Constants\ProductionCode;
use App\Constants\StatusCode;
use App\Core\Services\ExcelAnalyze\Writer\ExcelWriter;
use App\Core\Services\Production\Code\CodeService;
use App\Core\Services\TchipSale\SaleService;
use App\Core\Utils\TimeUtils;
use App\Exception\AppException;
use App\Model\TchipBi\OrderMacSnBatchModel;
use App\Model\TchipBi\SnCodeModel;
use App\Model\TchipBi\UserModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Throwable;

class SnCodeService extends CodeService
{
    /**
     * @Inject()
     * @var SnCodeModel
     */
    protected $model;

    protected $codeField = 'sn_code';
    protected $sortField = 'sort';

    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        $keywords = null;
        $saleIdArr = [];
        if (!empty($filter['keywords'])) {
            $keywords = $filter['keywords'];
            unset($filter['keywords']);
            //到销售系统查物流号对应的销售单id
            $idArr = make(SaleService::class)->getKIdByLogisticsNo($keywords);
            $idArr && $saleIdArr = $idArr;
        }
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        if ($keywords) {
            $query = $query->where(function ($query) use ($keywords, $saleIdArr) {
                $query->where('sn_code.sn_code', 'like', "%{$keywords}%")
                    // ->orWhere('used_no', 'like', "%{$keywords}%")
                    ->orWhere('used_product', 'like', "%{$keywords}%")
                    ->orWhere('used_product_code', 'like', "%{$keywords}%")
                    ->orWhere('s.client_name', 'like', "%{$keywords}%")
                    ->orWhere('s.out_stock_no','like',"%{$keywords}%");
                $saleIdArr && $query->orWhereIn('ss.sale_id', $saleIdArr);
//                    ->orWhere('ss.logistics_no', 'like', "%{$keywords}%");
                return $query;
            });
        }
        $field = [
            'sn_code.*',
            's.client_name',
            'ssn.created_at as scan_at',
            'ss.sale_id',
            's.out_stock_no'
        ];
        $query->select($field);
        $query->leftJoin('shipment_sale_sn as ssn', 'ssn.sn_code', '=', 'sn_code.sn_code')
            ->leftJoin('shipment_sale as ss', 'ss.id', '=', 'ssn.shipment_sale_id')
            ->leftJoin('shipment as s', 's.id', '=', 'ss.shipment_id');
        $query = $query->groupBy('sn_code.id');
        $paginate = $query->orderBy($sort, $order)->paginate($limit);
        $paginate = $paginate ? $paginate->toArray() : [];

        if (!empty($paginate['data'])) {
            //获取用户信息
            $userIdArr = array_unique(array_filter(array_column($paginate['data'], 'used_user_id')));
            $userData = $userIdArr ? UserModel::query()->whereIn('id', $userIdArr)->pluck('name', 'id')->toArray() : [];

            //查出对应的sn关联
            $snIds = array_column($paginate['data'], 'id');
            $snRelation = OrderMacSnBatchModel::query()->whereIn('sn_id', $snIds)
                ->leftJoin('mac_address', 'mac_address.id', '=', 'order_mac_sn_batch.mac_id')
                ->select([
                    'order_mac_sn_batch.batch_key',
                    'order_mac_sn_batch.sn_id',
                    'mac_address.mac_address'
                ])->get();

            $relation = [];
            foreach ($snRelation as $value) {
                $relation[$value['sn_id']] = $value;
            }
            //获取销售系统的物流单号
            $logisticsNoArr = make(SaleService::class)->getLogisticsNoByKId(unique_filter_column($paginate['data'], 'sale_id'));
            foreach ($paginate['data'] as &$item) {
                $item['used_type_text'] = ProductionCode::SN_USED_TYPE[$item['used_type']] ?? '';
                $item['used_date_text'] = !empty($item['used_date']) ? date('Y-m-d', strtotime($item['used_date'])) : '';
                $item['used_user_name'] = $userData[$item['used_user_id']] ?? '';
                $item['origin_type_text'] = ProductionCode::ORIGIN_TYPE[$item['origin_type']] ?? '';
                $item['mac_address'] = $relation[$item['id']]['mac_address'] ?? '';
                $item['batch_key'] = $relation[$item['id']]['batch_key'] ?? '';
                $item['scan_at'] = TimeUtils::formatDate($item['scan_at'],'Y-m-d H:i');
                $item['logistics_no'] = $logisticsNoArr[$item['sale_id']] ?? '';
            }
        }
        return $paginate;
    }

    public function doEdit(int $id, array $values)
    {
        if ($id > 0) {
            $row = $this->model::query()->find($id);
            if (!$row) {
                throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
            }

            // 判断mac是否已存在
            if (!empty($values['sn_code']) && $row->code != $values['sn_code']) {
                if ($this->model::query()->where('sn_code', $values['sn_code'])->first()) {
                    throw new AppException(StatusCode::ERR_SERVER, __('production.Sn_code_exist'));
                }
            }
            if (isset($values['is_used']) && $values['is_used'] === 0) {
                $this->setNotUsedData($values);
            }
            $result = $row->update($values);
        } else {
            // 判断mac是否已存在
            if ($this->model::query()->where('sn_code', $values['sn_code'])->exists()) {
                throw new AppException(StatusCode::ERR_SERVER, __('production.Sn_code_exist'));
            }
            $result = $this->model::query()->create($values);
        }
        return $result;
    }

    /**
     * 保存数据设置为未使用
     * @param $data
     * @return void
     */
    public function setNotUsedData(&$data)
    {
        $data['is_used'] = 0;
        $data['used_no'] = null;
        $data['used_date'] = null;
        $data['used_client'] = null;
        $data['used_product'] = null;
        $data['used_product_code'] = '';
        $data['used_type'] = 0;
        $data['used_user_id'] = 0;
        $data['used_relate_id'] = 0;
    }

    public function getCodeField()
    {
        return 'sn_code';
    }

    public function buildEditData($data)
    {
        $data['sort'] = $this->getLastNumber($data['sn_code']);
        return $data;
    }

    public function getLastNumber($string)
    {
        // 使用正则表达式匹配字符串中的最后一个数字
        if (preg_match('/\d+$/', $string, $matches)) {
            // 返回匹配到的最后一个数字
            return $matches[0];
        } else {
            // 如果没有匹配到数字，返回空字符串
            return 0;
        }
    }

    /**
     * 获取订单的sn
     * @param $orderType
     * @param $orderId
     * @return array|mixed[]
     */
    public function getOrderCode($orderType, $orderId)
    {
        $data = $this->model::query()->where([
            'sn_code.used_type' => $orderType,
            'sn_code.used_relate_id' => $orderId,
        ])->leftJoin('order_mac_sn_batch', function ($join){
            $join->on('order_mac_sn_batch.sn_id', '=', 'sn_code.id');
            $join->whereNull('order_mac_sn_batch.deleted_at');
        })
            ->leftJoin('mac_address', 'mac_address.id', '=', 'order_mac_sn_batch.mac_id')
            ->select([
                'mac_address.mac_address',
                'order_mac_sn_batch.batch_key',
                'order_mac_sn_batch.batch_num',
                'sn_code.sn_code'
            ])->orderBy('order_mac_sn_batch.batch_num')
            ->orderBy('sn_code.sort')->get();
        return $data ? $data->toArray() : [];
    }

    public function doDelete($ids): int
    {
        Db::beginTransaction();
        try {
            $ids = explode(',', $ids);
            $this->model::destroy($ids);
            OrderMacSnBatchModel::query()->whereIn('sn_id', $ids)->delete();
            Db::commit();
            return 1;
        } catch (Throwable $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

    /**
     * 过滤和检查已经存在的sn码
     * @param $codeArr
     * @param $productCode
     * @return array
     */
    public function filterAndCheckExistCode($codeArr,$productCode)
    {
        $snList = $this->model::query()->where(['origin_type' => ProductionCode::ORIGIN_TYPE_OF_SELF])
            ->whereIn('sn_code', $codeArr)->select(['sn_code','used_product','used_product_code'])->get();
        $snList = $snList?$snList->toArray():[];
        $existSnCode = array_object_column($snList,'sn_code');
        $notExistSnCode = array_diff($codeArr, $existSnCode);

        //扫码录入需要对应系统录入的sn码且料号一致
        if ($notExistSnCode) {
            throw new AppException(StatusCode::ERR_SERVER, __('production.Sn_code_not_exist').':'.implode(',',$notExistSnCode));
        }

        //校验料号是否一致
        $existProductCode = array_object_column($snList,'used_product_code');
        $otherProductCode = array_diff($existProductCode,[$productCode]);
        $err = [];
        if($otherProductCode){
            foreach ($snList as $item){
                if(in_array($item['used_product_code'],$otherProductCode)){
                    $err[] = $item['sn_code'];
                }
            }
        }
        if($err){
            throw new AppException(StatusCode::ERR_SERVER, __('production.Sn_code_exist').implode(',',$err));
        }
    }

    /**
     * 插入指定sn
     * @param $codeArr
     * @return void
     */
    public function insertCodes($codeArr)
    {
        if (!empty($codeArr)) {
            $insertData = [];
            //过滤重复
            $codeArr = array_unique($codeArr);
            //已有的不新增
            $existSnCode = $this->model::query()->whereIn('sn_code', $codeArr)->pluck('sn_code')->toArray();
            $codeArr = array_diff($codeArr, $existSnCode);
            foreach ($codeArr as $item) {
                $insertData[] = [
                    'sn_code'     => $item,
                    'sort'        => $this->getLastNumber($item),
                    'origin_type' => ProductionCode::ORIGIN_TYPE_OF_SELF,
                    'created_at'  => date('Y-m-d H:i:s'),
                    'updated_at'  => date('Y-m-d H:i:s'),
                ];
            }
            $this->model::query()->insert($insertData);
        }
    }

    /**
     * 校验订单填写的mac地址区间是否合理
     * @param $originType
     * @param $rangeArr
     * @param $relateOrder
     * @return void
     */
    public function checkOrderRange($originType, $rangeArr, $relateOrder)
    {
        $err = '';

        foreach ($rangeArr as $range) {
            list($startSn, $endSn) = $range;
            list($firstPrefix,$firstNumStr,$firstNum) = split_text_and_number($startSn);
            list($endPrefix,$endNumStr,$endNum) = split_text_and_number($endSn);
            if(strlen($startSn) != strlen($endSn) || $firstPrefix != $endPrefix || empty($firstNumStr) || empty($endNumStr)){
                $err .= $startSn . '-' . $endSn . 'SN格式不正确；';
                continue;
            }
            if($endNum-$firstNum>10000){
                $err .= $startSn . '-' . $endSn . 'SN区间过大；';
                continue;
            }
            //是否有被使用
            $usedMacCount = $this->model::query()
                ->whereBetween('sort', [
                    $firstNum,
                    $endNum
                ])->where('origin_type', $originType)
                ->where('sn_code','like',"$firstPrefix%")
                ->where('used_type', '<>', $relateOrder['used_type'])->where('used_relate_id', '<>', $relateOrder['used_relate_id'])
                ->where('is_used', 1)->count();
            if ($usedMacCount) {
                $err .= $startSn . '-' . $endSn . '已被使用.' . $usedMacCount . '个；';
            }
        }
        if ($err) {
            throw new AppException(StatusCode::ERR_SERVER, $err);
        }
    }

    /**
     * 根据区间值获取全部sn号
     * @param $rangeArr
     * @return array
     */
    public function getRangeData($rangeArr)
    {
        $snArr = [];
        foreach ($rangeArr as $range){

            list($startSn, $endSn) = $range;
            list($firstPrefix,$firstNumStr,$firstNum) = split_text_and_number($startSn);
            list($endPrefix,$endNumStr,$endNum) = split_text_and_number($endSn);
            
            // 获取数字部分的长度，用于保持前导零
            $numLength = strlen($firstNumStr);
            
            for ($i = $firstNum; $i <= $endNum; $i++) {
                $snArr[] = $firstPrefix . str_pad($i, $numLength, '0', STR_PAD_LEFT);
            }
        }
        return array_unique($snArr);
    }

    /**
     * 导出SN码
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function export(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
    {
        $data = $this->getList($filter, $op, $sort, $order, 9999);
        $data = !empty($data['data']) ? $data['data'] : [];
        $filename = "SN码.xls";
        $sheetName = 'sheet1';
        $excelWriter = new ExcelWriter($filename, $sheetName);
        $option = [
            '序号'         => 'index',
            'SN码'         => 'sn_code',
            '使用类型'     => 'used_type_text',
            '生产/组装单号' => 'used_no',
            '使用人'       => 'used_user_name',
            '使用时间'     => 'used_date_text',
            '使用产品'     => 'used_product',
            '产品料号'     => 'used_product_code',
            '出库单号'     => 'out_stock_no',
            '销售单号'     => 'sale_id',
            '使用客户'     => 'client_name',
            '录入时间'     => 'scan_at',
            'MAC地址'      => 'mac_address',
            '物流单号'     => 'logistics_no',
            '订单备注'     => 'batch_key',
        ];
        $titleData = [
            array_keys($option)
        ];

        $width = [
            0,
            5,
            15,
            10,
            18,
            6,
            12,
            35,
            18,
            10,
            15,
            15,
            20,
        ];
        $excelWriter->setColumnWidthFromList($width);

        $title_style = [];
        for($i=0;$i<count($titleData[0]);$i++){
            $title_style[$i] = ['backgroundColor' => 'b4c6e7', 'bold' => true];
        }

        // 设置首行高度
        $excelWriter->setRowHeight(1, 30);

        $excelWriter->addData($titleData,[],[],[],$title_style);
        $row = [];
        foreach ($data as $k => $v) {
            if($k==0){
                var_dump($v);
            }
            //获取目录
            $index = $k + 1;
            $tempRow = [];
            foreach ($option as $key => $value) {
                if($value ==='index'){
                    $tempRow[] = $index;
                }else{
                    $tempRow[] = $v[$value];
                }
            }
            $row = [
                $tempRow
            ];
            $excelWriter->addData($row);
        }
        $result = $excelWriter->download();
        $excelWriter->close();
        return $result;
    }

    /**
     * 修复基于sn_no_range错误生成的SN号数据
     * @param bool $dryRun 是否只是试运行，不实际修改数据
     * @return array 返回修复结果
     */
    public function fixIncorrectSnData($dryRun = true)
    {
        $result = [
            'total_orders' => 0,
            'fixed_orders' => [],
            'fixed_orders_count' => 0,
            'fixed_sn_count' => 0,
            'errors' => [],
            'details' => []
        ];

        try {
            // 查询所有包含 sn_no_range 的订单信息
            $orders = \App\Model\TchipBi\AssembleOrderInfoModel::query()
                ->whereNotNull('sn_no_range')
                ->where('sn_no_range', '!=', '[]')
                ->where('sn_no_range', '!=', '')
                ->get();

            $result['total_orders'] = $orders->count();

            foreach ($orders as $order) {
                try {
                    $snNoRange = $order->sn_no_range;
                    if (empty($snNoRange) || !is_array($snNoRange)) {
                        continue;
                    }

                    // 生成正确的SN号列表
                    $correctSnList = $this->getRangeData($snNoRange);
                    
                    if (empty($correctSnList)) {
                        continue;
                    }

                    // 查找该订单关联的现有SN号
                    $relateOrder = [
                        'used_type' => \App\Constants\ProductionCode::CODE_USED_TYPE_ASSEMBLE,
                        'used_relate_id' => $order->assemble_order_id
                    ];

                    $existingSnData = $this->model::query()
                        ->where('used_type', $relateOrder['used_type'])
                        ->where('used_relate_id', $relateOrder['used_relate_id'])
                        ->where('is_used', 1)
                        ->get();

                    $existingSnList = $existingSnData->pluck('sn_code')->toArray();
                    
                    // 找出错误的SN号（现有的但不在正确列表中的）
                    $incorrectSnList = array_diff($existingSnList, $correctSnList);
                    
                    // 找出缺失的SN号（正确的但不在现有列表中的）
                    $missingSnList = array_diff($correctSnList, $existingSnList);

                    if (empty($incorrectSnList) && empty($missingSnList)) {
                        // 数据已经正确，无需修复
                        continue;
                    }

                    $orderDetail = [
                        'order_id' => $order->assemble_order_id,
                        'sn_no_range' => $snNoRange,
                        'correct_sn_count' => count($correctSnList),
                        'existing_sn_count' => count($existingSnList),
                        'incorrect_sn_count' => count($incorrectSnList),
                        'missing_sn_count' => count($missingSnList),
                        'incorrect_sn_list' => $incorrectSnList,
                        'missing_sn_list' => $missingSnList
                    ];
                    //记录下修复的order_id
                    $result['fixed_orders'][] = $order->assemble_order_id;

                    if (!$dryRun) {
                        // 实际修复数据
                        \Hyperf\DbConnection\Db::beginTransaction();
                        
                        try {
                            // 通过前缀和排序号匹配来修复SN号
                            $fixedCount = 0;
                            
                            // 为错误的SN号创建前缀+排序号的映射
                            $incorrectSnMap = [];
                            foreach ($incorrectSnList as $incorrectSn) {
                                list($prefix, $numStr, $num) = split_text_and_number($incorrectSn);
                                $key = $prefix . '_' . $num; // 使用前缀+数字作为key
                                $incorrectSnMap[$key] = $incorrectSn;
                            }
                            
                            // 为正确的SN号创建前缀+排序号的映射
                            $correctSnMap = [];
                            foreach ($correctSnList as $correctSn) {
                                list($prefix, $numStr, $num) = split_text_and_number($correctSn);
                                $key = $prefix . '_' . $num; // 使用前缀+数字作为key
                                $correctSnMap[$key] = $correctSn;
                            }
                            
                            // 通过key匹配，更新错误的SN号为正确格式
                            foreach ($incorrectSnMap as $key => $incorrectSn) {
                                if (isset($correctSnMap[$key])) {
                                    $correctSn = $correctSnMap[$key];
                                    
                                    // 更新SN号为正确格式
                                    $this->model::query()
                                        ->where('sn_code', $incorrectSn)
                                        ->where('used_type', $relateOrder['used_type'])
                                        ->where('used_relate_id', $relateOrder['used_relate_id'])
                                        ->update([
                                            'sn_code' => $correctSn,
                                            'sort' => $this->getLastNumber($correctSn),
                                            'updated_at' => date('Y-m-d H:i:s')
                                        ]);
                                    
                                    $fixedCount++;
                                    
                                    // 从缺失列表中移除已修复的SN号
                                    $missingSnList = array_diff($missingSnList, [$correctSn]);
                                }
                            }
                            
                            // 插入剩余缺失的SN号（这些是新增的，不是修复的）
                            if (!empty($missingSnList)) {
                                // 获取现有SN号记录的信息作为模板
                                $templateSnData = $existingSnData->first();
                                
                                $insertData = [];
                                foreach ($missingSnList as $missingSn) {
                                    $insertData[] = [
                                        'sn_code' => $missingSn,
                                        'sort' => $this->getLastNumber($missingSn),
                                        'origin_type' => $order->sn_range_origin_type ?? \App\Constants\ProductionCode::ORIGIN_TYPE_OF_SELF,
                                        'used_type' => $relateOrder['used_type'],
                                        'used_relate_id' => $relateOrder['used_relate_id'],
                                        'used_no' => $templateSnData->used_no ?? '',
                                        'used_user_id' => $templateSnData->used_user_id ?? 0,
                                        'used_product' => $templateSnData->used_product ?? '',
                                        'used_product_code' => $templateSnData->used_product_code ?? '',
                                        'used_client' => $templateSnData->used_client ?? '',
                                        'is_used' => 1,
                                        'used_date' => $templateSnData->used_date ?? date('Y-m-d H:i:s'),
                                        'created_at' => date('Y-m-d H:i:s'),
                                        'updated_at' => date('Y-m-d H:i:s')
                                    ];
                                }
                                $this->model::query()->insert($insertData);
                            }
                            
                            \Hyperf\DbConnection\Db::commit();
                            
                            $result['fixed_sn_count'] += $fixedCount + count($missingSnList);
                        } catch (\Exception $e) {
                            \Hyperf\DbConnection\Db::rollBack();
                            throw $e;
                        }
                    }

                    $result['details'][] = $orderDetail;
                    $result['fixed_orders_count']++;

                } catch (\Exception $e) {
                    $result['errors'][] = [
                        'order_id' => $order->assemble_order_id ?? 'unknown',
                        'error' => $e->getMessage()
                    ];
                }
            }

        } catch (\Exception $e) {
            $result['errors'][] = [
                'general_error' => $e->getMessage()
            ];
        }

        return $result;
    }

//    /**
//     * 获取订单的mac地址区间
//     * @param $relateOrder
//     * @return array
//     */
//    public function getOrderRange($relateOrder)
//    {
//        $data = $this->model::query()
//            ->where([
//                'used_type'    => $relateOrder['used_type'],
//                'used_relate_id' => $relateOrder['used_relate_id']
//            ])
//            ->where('is_used', 1)
//            ->pluck('sort','sn_code')->toArray();
//        if (empty($data)) {
//            return [];
//        }
//
//        // 按照 mac_decimal 进行排序
//        asort($data);
//
//        $segments = [];
//        $startSn = null;
//        $startSort = null;
//        $prevSn = null;
//        $prevSort = null;
//
//        foreach ($data as $mac => $dec) {
//            if ($startSn === null) {
//                // 初始化第一个段
//                $startSn = $mac;
//                $startSort = $dec;
//            } elseif ($prevSort !== null && $dec !== $prevSort + 1) {
//                // 如果 mac_decimal 不是连续的，保存当前分段
//                $segments[] = [
//                    'start' => $startSn,
//                    'end'   => $prevSn,
//                ];
//                // 重新开始新的分段
//                $startSn = $mac;
//                $startSort = $dec;
//            }
//            $prevSn = $mac;
//            $prevSort = $dec;
//        }
//
//        // 处理最后一个分段
//        if ($startSn !== null) {
//            $segments[] = [
//                'start' => $startSn,
//                'end'   => $prevSn,
//            ];
//        }
//
//        return $segments;
//    }

}