<?php

declare(strict_types=1);

namespace App\Core\Services\TchipWiki;

use App\Constants\StatusCode;
use App\Event\Wiki\WikiDocumentStatusChangedEvent;
use App\Exception\AppException;
use App\Model\Points\PointRecordModel;
use App\Model\TchipBi\WikiDocumentStatusModel;
use App\Model\TchipBi\WikiDocumentStatusTypeModel;
use App\Model\TchipBi\WikiDocumentModel;
use Carbon\Carbon;
use Exception;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Logger\LoggerFactory;
use Psr\Container\ContainerInterface;
use Psr\EventDispatcher\EventDispatcherInterface;
use Psr\Log\LoggerInterface;

/**
 * 文档状态管理服务
 */
class DocumentStatusService
{
    /**
     * @Inject
     */
    private EventDispatcherInterface $eventDispatcher;

    private LoggerInterface $logger;

    public function __construct(ContainerInterface $container)
    {
        $this->logger = $container->get(LoggerFactory::class)->get('document_status');
    }

    /**
     * 设置文档状态
     *
     * @param int $docId 文档ID
     * @param string $statusType 状态类型
     * @param int $statusValue 状态值
     * @param int $operatorId 操作人ID
     * @param string|null $reason 操作原因
     * @param bool $addPoint
     * @return WikiDocumentStatusModel
     */
    public function setDocumentStatus(
        int $docId,
        string $statusType,
        int $statusValue,
        int $operatorId,
        ?string $reason = null,
        bool $addPoint = false
    ): WikiDocumentStatusModel {
        try {
            // 验证文档是否存在
            $document = WikiDocumentModel::where('doc_id', $docId)->first();
            if (!$document) {
                throw new AppException(StatusCode::VALIDATION_ERROR, "文档不存在: {$docId}");
            }

            // 验证状态类型是否有效
            if (!WikiDocumentStatusTypeModel::isActiveType($statusType)) {
                throw new AppException(StatusCode::VALIDATION_ERROR, "无效的状态类型: {$statusType}");
            }

            // 验证状态值
            if (!in_array($statusValue, [
                WikiDocumentStatusModel::STATUS_PENDING,
                WikiDocumentStatusModel::STATUS_APPROVED,
                WikiDocumentStatusModel::STATUS_REJECTED
            ])) {
                throw new AppException(StatusCode::VALIDATION_ERROR, "无效的状态值: {$statusValue}");
            }

            // 获取当前状态
            $currentStatus = WikiDocumentStatusModel::getCurrentStatus($docId, $statusType);
            $oldStatusValue = $currentStatus ? $currentStatus->status_value : null;
            
            // 使用upsert逻辑：一个状态类型+一个doc_id只绑定一行数据
            $newStatus = WikiDocumentStatusModel::updateOrCreate(
                [
                    'doc_id' => $docId,
                    'status_type' => $statusType,
                ],
                [
                    'status_value' => $statusValue,
                    'created_by' => $operatorId,
                    'added_points' =>  $addPoint && $statusValue == WikiDocumentStatusModel::STATUS_APPROVED ? 1 : 0,
                    'reason' => $reason,
                ]
            );
            // 触发状态变更事件
            $this->eventDispatcher->dispatch(new WikiDocumentStatusChangedEvent(
                $docId,
                $document->title,
                $document->created_by,
                $statusType,
                $oldStatusValue,
                $statusValue,
                $operatorId,
                $reason,
                $addPoint,
                $newStatus->added_points
            ));

            $this->logger->info('文档状态设置成功', [
                'doc_id' => $docId,
                'status_type' => $statusType,
                'old_status' => $currentStatus ? $currentStatus->status_value : null,
                'new_status' => $statusValue,
                'operator_id' => $operatorId,
                'reason' => $reason
            ]);

            return $newStatus;
        } catch (\Throwable $e) {
            $this->logger->info('设置文档状态失败', [
                'doc_id' => $docId,
                'status_type' => $statusType,
                'status_value' => $statusValue,
                'operator_id' => $operatorId,
                'reason' => $reason,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new AppException(StatusCode::ERR_SERVER,  $e->getMessage());
        }
    }

    /**
     * 设置精华认证状态
     *
     * @param int $docId
     * @param bool $isPremium
     * @param int $operatorId
     * @param string|null $reason
     * @return WikiDocumentStatusModel
     * @throws Exception
     */
    public function setPremiumStatus(int $docId, bool $isPremium, int $operatorId, ?string $reason = null, bool $addPoint = false): WikiDocumentStatusModel
    {
        $statusValue = $isPremium ? WikiDocumentStatusModel::STATUS_APPROVED : WikiDocumentStatusModel::STATUS_REJECTED;
        return $this->setDocumentStatus($docId, WikiDocumentStatusTypeModel::TYPE_PREMIUM, $statusValue, $operatorId, $reason, $addPoint);
    }

    /**
     * 设置培训认证状态
     *
     * @param int $docId
     * @param bool $isTraining
     * @param int $operatorId
     * @param string|null $reason
     * @param bool $addPoint
     * @return WikiDocumentStatusModel
     */
    public function setTrainingStatus(
        int $docId,
        bool $isTraining,
        int $operatorId,
        ?string $reason = null,
        bool $addPoint = false
    ): WikiDocumentStatusModel {
        $statusValue = $isTraining ? WikiDocumentStatusModel::STATUS_APPROVED : WikiDocumentStatusModel::STATUS_REJECTED;
        return $this->setDocumentStatus($docId, WikiDocumentStatusTypeModel::TYPE_TRAINING, $statusValue, $operatorId, $reason, $addPoint);
    }

    /**
     * 设置审核状态
     *
     * @param int $docId
     * @param int $auditStatus 0:待审核 1:通过 2:拒绝
     * @param int $operatorId
     * @param string|null $reason
     * @param bool $addPoint
     * @return WikiDocumentStatusModel
     * @throws Exception
     */
    public function setAuditStatus(int $docId, int $auditStatus, int $operatorId, ?string $reason = null, bool $addPoint = false): WikiDocumentStatusModel
    {
        return $this->setDocumentStatus($docId, WikiDocumentStatusTypeModel::TYPE_AUDIT, $auditStatus, $operatorId, $reason,  $addPoint);
    }

    /**
     * 批量审核文档
     *
     * @param array $docIds
     * @param int $auditStatus
     * @param int $operatorId
     * @param string|null $reason
     * @return array
     */
    public function batchAuditDocuments(array $docIds, int $auditStatus, int $operatorId, ?string $reason = null): array
    {
        $results = [];
        foreach ($docIds as $docId) {
            try {
                $results[$docId] = $this->setAuditStatus($docId, $auditStatus, $operatorId, $reason);
            } catch (Exception $e) {
                $this->logger->info('批量审核失败', [
                    'doc_id' => $docId,
                    'audit_status' => $auditStatus,
                    'operator_id' => $operatorId,
                    'error' => $e->getMessage()
                ]);
                $results[$docId] = ['error' => $e->getMessage()];
            }
        }
        return $results;
    }

    /**
     * 获取文档的状态历史
     *
     * @param int $docId
     * @return array
     */
    public function getDocumentStatusHistory(int $docId): array
    {
        return WikiDocumentStatusModel::getDocumentAllStatus($docId)->toArray();
    }

    /**
     * 查询文档状态信息
     *
     * @param int $docId 文档ID
     * @return array
     */
    public function getDocumentStatus(int $docId): array
    {
        // 验证文档是否存在
        $document = WikiDocumentModel::where('doc_id', $docId)->first();
        if (!$document) {
            throw new AppException(StatusCode::VALIDATION_ERROR, "文档不存在: {$docId}");
        }

        // 获取所有状态类型
        $statusTypes = WikiDocumentStatusTypeModel::getActiveTypes();

        // 获取文档的所有状态
        $documentStatuses = WikiDocumentStatusModel::where('doc_id', $docId)
            ->with(['statusType', 'creator'])
            ->get()
            ->keyBy('status_type');

        // 获取文档的加积分信息
        $pointRecords = PointRecordModel::where('source_type', 'wiki_document_audit')
            ->where('source_id', $docId)
            ->where('point_type', 'publish_article') // 增减皆标志为publish_article，去首行判断是否大于0
            ->orderBy('id', 'desc')
            ->first();
        $pointRecords = !empty($pointRecords) ? $pointRecords->toArray() : [];
        $pointWasAdded = !empty($pointRecords) && $pointRecords['point_change'] > 0;


        $result = [
            'doc_id' => $docId,
            'document_title' => $document->title,
            'document_author' => $document->created_by,
            'point_was_added' => $pointWasAdded,
            'statuses' => [],
            'summary' => [
                'total_status_types' => $statusTypes->count(),
                'configured_statuses' => $documentStatuses->count(),
                'pending_count' => 0,
                'approved_count' => 0,
                'rejected_count' => 0,
            ]
        ];

        // 遍历所有状态类型，构建完整的状态信息
        foreach ($statusTypes as $statusType) {
            $currentStatus = $documentStatuses->get($statusType->status_type);

            $statusInfo = [
                'status_type' => $statusType->status_type,
                'status_type_description' => $statusType->description,
                'is_configured' => $currentStatus !== null,
            ];

            if ($currentStatus) {
                $statusInfo = array_merge($statusInfo, [
                    'status_value' => $currentStatus->status_value,
                    'status_value_description' => $currentStatus->status_value_description,
                    'created_by' => $currentStatus->created_by,
                    'creator_name' => $currentStatus->creator ? $currentStatus->creator->name : null,
                    'created_at' => $currentStatus->created_at,
                    // 'expired_at' => $currentStatus->expired_at,
                    'reason' => $currentStatus->reason,
                ]);

                // 统计各状态数量
                switch ($currentStatus->status_value) {
                    case WikiDocumentStatusModel::STATUS_PENDING:
                        $result['summary']['pending_count']++;
                        break;
                    case WikiDocumentStatusModel::STATUS_APPROVED:
                        $result['summary']['approved_count']++;
                        break;
                    case WikiDocumentStatusModel::STATUS_REJECTED:
                        $result['summary']['rejected_count']++;
                        break;
                }
            } else {
                $statusInfo = array_merge($statusInfo, [
                    'status_value' => null,
                    'status_value_description' => '未设置',
                    'created_by' => null,
                    'creator_name' => null,
                    'created_at' => null,
                    // 'expired_at' => null,
                    'reason' => null,
                    'is_expired' => false,
                ]);
            }

            $result['statuses'][] = $statusInfo;
        }

        return $result;
    }

}