<?php
/**
 *
 * @Copyright T-chip Team.
 * @Date 2022/5/14 下午8:04
 * <AUTHOR>
 * @Description
 *
 */

namespace App\Core\Services\WorkWx;

use App\Constants\CommonCode;
use App\Constants\StatusCode;
use App\Core\Services\TchipOa\OaFilesService;
use App\Core\Utils\Log;
use App\Core\Utils\Random;
use App\Exception\AppException;
use App\Model\TchipBi\AuthGroupAccessModel;
use App\Model\TchipBi\OaUserFilesModel;
use App\Model\TchipBi\UserDepartmentBindModel;
use GuzzleHttp\Exception\GuzzleException;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

/**
 * 成员管理
 */
class WorkWxUserService extends WorkWxBaseService
{
    /**
     * @Inject()
     * @var AuthGroupAccessModel
     */
    protected $groupAccessModel;

    /**
     * @Inject()
     * @var UserDepartmentBindModel
     */
    protected $userDepartmentBindModel;

    /**
     * @Inject()
     * @var OaFilesService
     */
    protected $oaFilesService;


    /**
     * @param $code
     * @return "data": {"userid": "XiaoJiaJie","errcode": 0,"errmsg": "ok"}
     */
    public function getUserIdByCode($code)
    {
        $result = $this->sendRequest('auth/getuserinfo', [
            'query' => [
                'access_token'  => getCache(CommonCode::REDISKEY_WORKWX_AGENT_TCHIP_BI_TOKEN),
                'code' => $code,
            ]
        ]);
        return $result['userid'] ?? null;
    }

    /**
     * 获取成员列表
     * @return mixed
     */
    public function getUserList()
    {
        return $this->sendRequest('user/list', [
            'query' => [
                'access_token'  => getCache(CommonCode::REDISKEY_WORKWX_CONTACT_TOKEN),
                'department_id' => 1,
                'fetch_child'   => 1
            ]
        ]);
    }

    /**
     * 获取单个用户详情
     * @param string $userId 用户ID
     * @return mixed
     */
    public function getUserDetail($userId)
    {
        return $this->sendRequest('user/get', [
            'query' => [
                'access_token' => getCache(CommonCode::REDISKEY_WORKWX_CONTACT_TOKEN),
                'userid' => $userId
            ]
        ]);
    }

    /**
     * 同步成员列表
     * @return array
     */
    public function syncUserList()
    {
        Log::get('system', 'system')->info('访问微信接口获取企业微信用户信息...');
        try {
            $userFilesModel = make(OaUserFilesModel::class);
            $items = $this->sendRequest('user/list', [
                'query' => [
                    'access_token'  => getCache(CommonCode::REDISKEY_WORKWX_CONTACT_TOKEN),
                    'department_id' => 1,
                    'fetch_child'   => 1
                ]
            ]);
        } catch (AppException $e) {
            Log::get('system', 'system')->error('访问企业微信接口失败:'.$e->getMessage());
            return false;
        } catch (GuzzleException $e) {
            Log::get('system', 'system')->error('访问企业微信接口失败:'.$e->getMessage());
            return false;
        }

        $count = count($items['userlist']);
        $existUserList = [];
        foreach ($items['userlist'] as $k => $item) {
            $key = $k + 1;
            Log::get('system', 'system')->info("开始同步用户到系统({$key}/{$count}).".$item['name']);

            $syncUser = $this->userModel::query()->where('workwx_userid', $item['userid'])->first();

            if ($syncUser) {
                $existUserList[] = $item['userid'];
                $existUserListName[] = $item['name'];
            }

            if ($syncUser && $syncUser->sync_work_wechat != 1) {
                Log::get('system', 'system')->info("用户 ".$item['name']." 为不需要同步企业微信信息 ({$key}/{$count})");
                continue;
            }

            if ( isset($item['status']) && $item['status'] != 1 ) {
                Log::get('system', 'system')->info("用户 ".$item['name']." 状态为:".$item['status']);
            }
            $userInfo = $this->userModel::query()->updateOrCreate(['workwx_userid' => $item['userid']], $item);
            if (!$userInfo) {
                Log::get('system', 'system')->error("创建或查找用户失败 {$item['name']} ({$key}/{$count}).");
                continue;
            }

            // 是否需要更新邮箱
            $email = $userInfo->biz_mail ?? '';
            if ($email && empty($userInfo->email)) {
                $userInfo->email = $email;
                $userInfo->save();
            }

            // 密码处于不正常状态，初始化密码
            if (empty($userInfo->password) || empty($userInfo->salt) && $userInfo->must_change_password == 1) {
                $userInfo->salt = Random::alnum(10);
                // 所有用户为随机密码
                $defaultPassword = Random::alnum(10);
                $userInfo->password = makePassword($defaultPassword, $userInfo->salt);
                $userInfo->save();
            }

            // 更新角色
            $groupAccess = [CommonCode::BASE_GROUP_ACCESS];
            if (empty($userInfo->group_access_ids)) {
                $userInfo->group_access_ids = json_encode($groupAccess);
                $userInfo->save();
            } else {
                $groupAccess = !is_array($userInfo->group_access_ids) ? explode(',', $userInfo->group_access_ids) : $userInfo->group_access_ids;
            }
            foreach ($groupAccess as $access) {
                $accessResult = $this->groupAccessModel::query()->updateOrCreate(['user_id' => $userInfo->id, 'group_id' => $access]);
                if (!$accessResult) {
                    // 记录错误，继续操作下面的同步
                    Log::get('system', 'system')->error("创建或查找用户组群失败ID:{$access} {$item['name']} ({$key}/{$count}).");
                }
            }

            // 同步部门
            if ($item['department']) {
                $department = is_array($item['department']) ? $item['department'] : json_decode($item['department'], true);
                // 1. 先把不在的部门删除掉
                $this->userDepartmentBindModel::query()->whereNotIn('department_id', $department)->where('user_id', $userInfo->id)->delete();
                // 重新加入
                foreach ($department as $dep) {
                    $this->userDepartmentBindModel::query()->updateOrCreate(['user_id' => $userInfo->id, 'department_id' => $dep]);
                }
                // 同步员工档案表中的部门ID 暂不同步，档案部门功能沿用旧OA部门表
                // Log::get('system', 'system')->info("同步用户档案部门ID.".$item['name']);
                // $userFiles = $userFilesModel::query()->where('email', $userInfo->biz_mail)->where('status', 1)->where('incumbency', '在职')->first();
                // if ($userFiles && $department[0]) {
                //     $userFiles->department_id = $department[0];
                //     $userFiles->save();
                // }
            }
            // 同步员工档案
            // $oaFilesModel = make(OaUserFilesModel::class);
            // $saveFiles = [
            //     'user_name' => $userInfo->name,
            //     'sex' => $userInfo->gender == 1 ? '男' : ($userInfo->gender == 2 ? '女' : '未定'),
            //     'incumbency' => $userInfo->status == 1 ? '在职' : '离职',
            //     'status' => $userInfo->status,
            //     'email' => $userInfo->biz_mail,
            // ];
            // $files = $oaFilesModel::query()->where(['incumbency' => '在职', 'email' => $userInfo->biz_mail, 'status' => 1])->first();
            // $filesId = !empty($files->id) ? (int) $files->id : 0;
            // if ($filesId == 0) {
            //     Log::get('system', 'system')->info("用户 {$userInfo->name} 没有员工档案，开始创建");
            // } else {
            //     Log::get('system', 'system')->info("正在更新 {$userInfo->name} 员工档案");
            // }
            // $this->oaFilesService->doEdit($filesId, $saveFiles);
        }

        Log::get('system', 'system')->info("开始查找已经退出企业微信的员工");
        $notExUserList = $this->userModel::query()->whereNotIn('workwx_userid', $existUserList)
            ->whereNotNull('workwx_userid')->where('status', 1)->where('sync_work_wechat', 1)->get();
        $notExUserList = $notExUserList ? $notExUserList->toArray() : [];
        $quitCount = count($notExUserList);
        if ($quitCount > 0) {
            Log::get('system', 'system')->info("发现已经退出企业微信员工 {$quitCount} 名");
            foreach ($notExUserList as $key => $notExUser) {
                // 测试帐号，跳过修改状态
                if ($notExUser['name'] == 'test' || $notExUser['biz_mail'] == '<EMAIL>') {
                    Log::get('system', 'system')->info("特殊用户 {$notExUser['id']} - {$notExUser['name']} 跳过修改状态({$key}/{$quitCount})");
                    continue;
                }
                $result = $this->userModel::query()->where('id', $notExUser['id'])->update(['status' => 5]);
                if ($result) {
                    Log::get('system', 'system')->info("员工 {$notExUser['name']} 成功设置为退出状态({$key}/{$quitCount})");
                } else {
                    Log::get('system', 'system')->error("员工 {$notExUser['name']} 设置退出状态失败({$key}/{$quitCount})");
                }
            }
        }

        Log::get('system', 'system')->info("同步到系统完成");
        return true;
    }
}