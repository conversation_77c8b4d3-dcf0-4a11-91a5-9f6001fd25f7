<?php

namespace App\Core\Services\TchipOa;
use App\Constants\OaQcErpCode;
use App\Constants\StatusCode;
use App\Constants\TchipErpCode;
use App\Core\Services\AssembleOrder\AssembleOrderIqcService;
use App\Core\Services\AuthService;
use App\Core\Services\ExcelAnalyze\Writer\ExcelWriter;
use App\Core\Services\ProductionOrder\ProductionOrderIqcService;
use App\Exception\AppException;
use App\Model\Model;
use App\Model\TchipBi\AssembleOrderIqcModel;
use App\Model\TchipBi\AssembleOrderModel;
use App\Model\TchipBi\CategoryModel;
use App\Model\TchipBi\OaQcModel;
use App\Model\TchipBi\ProductionOrderIqcModel;
use App\Model\TchipBi\ProductionOrderModel;
use App\Model\TchipBi\UserModel;
use Carbon\Carbon;
use Hyperf\Context\Context;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Qbhy\HyperfAuth\AuthManager;
use Psr\SimpleCache\CacheInterface;
use Psr\SimpleCache\InvalidArgumentException;
use App\Core\Services\Notice\Driver\DynamicNoticeFactory;

class OaQcService extends OaBaseService
{

    /**
     * @Inject()
     * @var OaQcModel
     */
    protected $model;

    /**
     * @Inject()
     * @var AuthManager
     */
    protected $auth;

    /**
     * @Inject()
     * @var CacheInterface
     */
    protected $cache;

    public function getListv1(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        $keywords = null;
        if (isset($filter['keywords'])) {
            $keywords = $filter['keywords'];
            unset($filter['keywords']);
        }
        /* @var OaQcModel $query */
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        if ($keywords) {
            $query = $query->where(function ($query) use ($keywords) {
                $query->where('order_no', 'LIKE', "%{$keywords}%")->orWhere('enter_no', 'LIKE', "%{$keywords}%")
                    ->orWhere('prod_code', 'LIKE', "%{$keywords}%")->orWhere('prod_name', 'LIKE', "%{$keywords}%")
                    ->orWhere('prod_spec', 'LIKE', "%{$keywords}%");
                return $query;
            });
        }

        $wepdService = make(\App\Core\Services\TchipSale\Erp\OrderPohService::class);

        $paginate = $query->with('locationByProdLoc', 'erpSilk')->orderBy($sort, $order)->paginate($limit);
        $paginate = is_object($paginate) ? $paginate->toArray() : $paginate;
        if (!empty($paginate['data'])) {
            $typeIds = array_unique(array_column($paginate['data'], 'type'));
            $types = CategoryModel::query()->whereIn('id', $typeIds)->get();
            $types = $types ? array_column($types->toArray(), null, 'id') : [];
            $hanldeList = array_column(OaQcErpCode::HANLDE_LIST, null, 'value');
            $userIds = array_unique(array_column($paginate['data'], 'examine_user'));
            $users = UserModel::query()->select(['id', 'name'])->whereIn('id', $userIds)->get();
            $users = $users ? array_column($users->toArray(), null, 'id') : [];

            // 获取供应商名称
            $orderNos = array_column($paginate['data'], 'order_no');
            $tmp = $wepdService->getList(['code' => implode(',', array_unique($orderNos))], ['code' => 'IN'], 'code', '');
            $tmp = $tmp ? array_column($tmp, null, 'CODE') : [];

            foreach ($paginate['data'] as &$datum) {
                // 获取 type_text
                $datum['type_text'] = !empty($types[$datum['type']]) ? $types[$datum['type']]['name'] : '';
                $datum['unqualified_handle_text'] = empty($datum['unqualified_handle']) ? '待处理' : (!empty($hanldeList[$datum['unqualified_handle']]) ? $hanldeList[$datum['unqualified_handle']]['label'] : '');
                $datum['examine_user_name'] = !empty($users[$datum['examine_user']]) ? $users[$datum['examine_user']]['name'] : '';
                // 处理 erp_silk 中的 sample_approval_doc 数据
                if (!empty($datum['erp_silk'])) {
                    $datum['erp_silk'] = array_merge(...array_map(function ($silk) {
                        // 如果 silk 中有 sample_approval_doc，则直接返回它
                        return $silk['sample_approval_doc'] ?? []; // 如果没有 sample_approval_doc，返回空数组
                    }, $datum['erp_silk']));
                }
                $datum['supply_name'] = $tmp[$datum['order_no']]['supply_name'] ?? '';
            }

        }
        return $paginate;
    }

    /**
     * 获取质检列表
     * 20250418 添加供应商筛选条件
     */
    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        // 提取供应商名称筛选条件
        $supplierName = null;
        if (isset($filter['supplier'])) {
            $supplierName = $filter['supplier'];
            unset($filter['supplier']);
        }
        
        // 提取并处理归属地筛选条件
        $locationName = null;
        if (isset($filter['location'])) {
            $locationName = $filter['location'];
            unset($filter['location']);
            
            // 根据归属地名称查找对应的所有仓库编码
            $warehouseCodes = \App\Model\TchipBi\ErpWarehouseModel::query()
                ->where('location', $locationName)
                ->where('status', 1)
                ->pluck('code')
                ->toArray();
            
            if (!empty($warehouseCodes)) {
                $filter['prod_loc'] = implode(',', $warehouseCodes);
                $op['prod_loc'] = 'IN';
            }
        }
        
        $keywords = null;
        if (isset($filter['keywords'])) {
            $keywords = $filter['keywords'];
            unset($filter['keywords']);
        }
        
        /* @var OaQcModel $query */
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        if ($keywords) {
            $query = $query->where(function ($query) use ($keywords) {
                $query->where('order_no', 'LIKE', "%{$keywords}%")->orWhere('enter_no', 'LIKE', "%{$keywords}%")
                    ->orWhere('prod_code', 'LIKE', "%{$keywords}%")->orWhere('prod_name', 'LIKE', "%{$keywords}%")
                    ->orWhere('prod_spec', 'LIKE', "%{$keywords}%");
                return $query;
            });
        }

        $wepdService = make(\App\Core\Services\TchipSale\Erp\OrderPohService::class);

        $paginate = $query->with('locationByProdLoc', 'erpSilk', 'specialOrders')->orderBy($sort, $order)->paginate($limit);
        $paginate = is_object($paginate) ? $paginate->toArray() : $paginate;
        if (!empty($paginate['data'])) {
            $typeIds = array_unique(array_column($paginate['data'], 'type'));
            $types = CategoryModel::query()->whereIn('id', $typeIds)->get();
            $types = $types ? array_column($types->toArray(), null, 'id') : [];
            $hanldeList = array_column(OaQcErpCode::HANLDE_LIST, null, 'value');
            $userIds = array_unique(array_column($paginate['data'], 'examine_user'));
            $users = UserModel::query()->select(['id', 'name'])->whereIn('id', $userIds)->get();
            $users = $users ? array_column($users->toArray(), null, 'id') : [];

            // 获取供应商名称
            $orderNos = array_column($paginate['data'], 'order_no');
            $tmp = $wepdService->getList(['code' => implode(',', array_unique($orderNos))], ['code' => 'IN'], 'code', '');
            $tmp = $tmp ? array_column($tmp, null, 'CODE') : [];

            // 如果需要筛选供应商名称，先创建一个新数组
            $filteredData = [];
            foreach ($paginate['data'] as &$datum) {
                // 获取 type_text
                $datum['type_text'] = !empty($types[$datum['type']]) ? $types[$datum['type']]['name'] : '';
                $datum['unqualified_handle_text'] = empty($datum['unqualified_handle']) ? '待处理' : (!empty($hanldeList[$datum['unqualified_handle']]) ? $hanldeList[$datum['unqualified_handle']]['label'] : '');
                $datum['examine_user_name'] = !empty($users[$datum['examine_user']]) ? $users[$datum['examine_user']]['name'] : '';
                // 处理 erp_silk 中的 sample_approval_doc 数据

                if (!empty($datum['erp_silk'])) {
                    $datum['erp_silk'] = $datum['erp_silk']['sample_approval_doc'] ?? [];
                    // $datum['erp_silk'] = array_merge(...array_map(function ($silk) {
                    //     // 如果 silk 中有 sample_approval_doc，则直接返回它
                    //     return $silk['sample_approval_doc'] ?? []; // 如果没有 sample_approval_doc，返回空数组
                    // }, $datum['erp_silk']));
                }
                $datum['supply_name'] = $tmp[$datum['order_no']]['supply_name'] ?? '';

                // 如果设置了供应商筛选条件且不匹配，则跳过该条记录
                if ($supplierName !== null && $datum['supply_name'] !== $supplierName) {
                    continue;
                }
                
                // 添加到过滤后的数组
                $filteredData[] = $datum;
            }
            
            // 如果设置了供应商筛选条件，替换原始数据并更新计数，否则前端分页信息会有误
            if ($supplierName !== null) {
                $paginate['data'] = $filteredData;
                $paginate['total'] = count($filteredData);
                $paginate['last_page'] = ceil($paginate['total'] / $limit);
            }
        }
        return $paginate;
    }

    public function getAllList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
    {
        $keywords = null;
        if (isset($filter['keywords'])) {
            $keywords = $filter['keywords'];
            unset($filter['keywords']);
        }
        /* @var OaQcModel $query */
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, 9999);
        if ($keywords) {
            $query = $query->where(function ($query) use ($keywords) {
                $query->where('order_no', 'LIKE', "%{$keywords}%")->orWhere('enter_no', 'LIKE', "%{$keywords}%")
                    ->orWhere('prod_code', 'LIKE', "%{$keywords}%")->orWhere('prod_name', 'LIKE', "%{$keywords}%")
                    ->orWhere('prod_spec', 'LIKE', "%{$keywords}%");
                return $query;
            });
        }
        $rows = $query->orderBy($sort, $order)->get();
        $rows = $rows ? $rows->toArray() : $rows;
        if (!empty($rows)) {
            $typeIds = array_unique(array_column($rows, 'type'));
            $types = CategoryModel::query()->whereIn('id', $typeIds)->get();
            $types = $types ? array_column($types->toArray(), null, 'id') : [];
            foreach ($rows as &$datum) {
                $datum['type_text'] = !empty($types[$datum['type']]) ? $types[$datum['type']]['name'] : '';
            }
        }
        return $rows;
    }

    public function getOverView($id)
    {
        $row = $this->model::query()->with(['locationByProdLoc', 'erpSilk'])->find($id);
         if ($row) {
             $wepdService = make(\App\Core\Services\TchipSale\Erp\OrderPohService::class);
             $tmp = $wepdService->overView($row['order_no']);
             $row['supply_name'] = $tmp['supply_name'] ?? '';
             $row['production_order_id'] = ProductionOrderIqcModel::query()->where('oa_qc_id',$row['id'])->pluck('production_order_id')->toArray();
             $row['assemble_order_id'] = AssembleOrderIqcModel::query()->where('oa_qc_id',$row['id'])->pluck('assemble_order_id')->toArray();
        //     $row = $row->toArray();
        //     switch ($row['qc_type']) {
        //         case OaQcErpCode::QC_DONE_ENTER:
        //             $wepdService = make(\App\Core\Services\TchipSale\Erp\ScWepdService::class);
        //             $erp = $wepdService->overView(null, ['CODE' => $row['order_no']]);
        //             break;
        //         case OaQcErpCode::QC_PURCHASE_ENTER:
        //             $wepdService = make(\App\Core\Services\TchipSale\Erp\OrderPrcedService::class);
        //             break;
        //         case OaQcErpCode::QC_ASSEMBLE_ENTER:
        //             $wepdService = make(\App\Core\Services\TchipSale\Erp\StockPisdService::class);
        //             break;
        //         case OaQcErpCode::QC_COAS_ENTER:
        //             $wepdService = make(\App\Core\Services\TchipSale\Erp\StockZzhService::class);
        //             break;
        //     }
         }
        return $row;
    }


    public function doEdit(int $id, array $values)
    {
        Db::beginTransaction();
        try {
            // 需要推送外观检测员与功能检测员列表
            $appUser = $funUser = $exaUser = [];

            if (isset($values['handle'])) {
                $values['handle'] = (int) $values['handle'];
            }
            if ($id > 0) {
                $row = $this->model::query()->find($id);
                $oldStatus = $row->status;
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                }

                // 如果选择检测功能，并没有选择处理人时，自动识别当前账号
                if (!empty($values['examine_option']) && in_array('功能', $values['examine_option']) ){
                    if (empty($values['function_examine_user'])) {
                        $values['function_examine_user'] = $this->auth->user()->getId();
                    }
                }

                if (!empty($values['examine_option']) && in_array('外观', $values['examine_option']) ){
                    if (empty($values['appearance_examine_user'])) {
                        $values['appearance_examine_user'] = $this->auth->user()->getId();
                    }
                }

                // 外观检测通知 条件为之前没有选择检测人。或者修改了检测人的，需要通知
                if ((empty($row->appearance_examine_user) && !empty($values['appearance_examine_user'])) ||
                    (!empty($values['appearance_examine_user']) && !empty($row->appearance_examine_user) && $row->appearance_examine_user != $values['appearance_examine_user'])
                ) {
                    $appUser[] = $values['appearance_examine_user'];
                }
                // 功能检测通知 条件为之前没有选择检测人。或者修改了检测人的，需要通知
                if ((empty($row->function_examine_user) && !empty($values['function_examine_user'])) ||
                    (!empty($values['function_examine_user']) && !empty($row->function_examine_user) && $row->function_examine_user != $values['function_examine_user'])
                ) {
                    $funUser[] = $values['function_examine_user'];
                }

                // 状态改变 需要进行标注
                if ($oldStatus != $values['status']) {
                    // 获取同样料号 prod_code 最新的 status_change_at 记录
                    $pre = $this->model::query()
                        ->where('prod_code', $row->prod_code)
                        ->where('id', '!=', $row->id) // 排除当前记录
                        ->orderBy('status_change_at', 'desc') // 按 status_change_at 时间倒序排列
                        ->first(); // 获取最新的记录
                    if ($values['status'] == OaQcErpCode::STATUS_NO) {
                        $values['inspection_flunk_count'] = 5;
                    } else {
                        // 确保 inspection_flunk_count 最小值为 1
                        $values['inspection_flunk_count'] = max($pre->inspection_flunk_count - 1, 1);
                    }
                }

                // 更新 status_change_at 字段为当前时间
                if ( $oldStatus !== $values['status'] ||
                    (!empty($values['status']) && ($values['status'] == OaQcErpCode::STATUS_OK || $values['status'] == OaQcErpCode::STATUS_NO) && empty($values['status_change_at']))) {
                    if ($values['status'] !== 4 ) {
                        $values['status_change_at'] = Carbon::now();
                    }
                }

                $result = $row->update($values);
                //状态为挂起通知关注人
                $followers = $values['followers'];
                if (!empty($followers) && !empty($values['status']) && $values['status'] == OaQcErpCode::STATUS_PEND){
                    DynamicNoticeFactory::call('QcPendingNotice', $values);
                }

                // 报检结果通知 合格或者不合格需要
                if (!empty($values['status']) && ($values['status'] == OaQcErpCode::STATUS_OK || $values['status'] == OaQcErpCode::STATUS_NO)
                    && $oldStatus !== $values['status'] // 状态有变化才发送 20240625
                ) {
                    make(\App\Core\Services\Notice\NoticeService::class)->qcExamineResult($id);
                }

                //绑定生产订单
                if(!empty($values['production_order_id'])){
                    make(ProductionOrderIqcService::class)->doEdit(-1,['production_order_id'=>$values['production_order_id'],'oa_qc_id'=>$id]);
                }
                //绑定组装订单订单
                if(!empty($values['assemble_order_id'])){
                    make(AssembleOrderIqcService::class)->doEdit(-1,['assemble_order_id'=>$values['assemble_order_id'],'oa_qc_id'=>$id]);
                }
            } else {
                if (!empty($values['items']) && is_array($values['items'])) {
                    $resultId = [];
                    $dittoAppUser = $dittoFunUser = null;
                    foreach ($values['items'] as $item) {
                        $itemSave = array_merge($values, $item);
                        unset($itemSave['items']);
                        unset($itemSave['hanldeUserList']);

                        // 加入检测负责人通知
                        if (!empty($item['examine_user'])) {
                            $exaUser[] = $item['examine_user'];
                        }

                        // 处理同上属性的外观检测员
                        if (isset($itemSave['appearance_examine_user'])) {
                            if ($itemSave['appearance_examine_user'] === 'ditto') {
                                $itemSave['appearance_examine_user'] = $dittoAppUser;
                            } else {
                                $dittoAppUser = $itemSave['appearance_examine_user'];
                            }
                        } else {
                            $itemSave['appearance_examine_user'] = 0;
                        }

                        // 处理同上属性的功能检测员
                        if (isset($itemSave['function_examine_user'])) {
                            if ($itemSave['function_examine_user'] === 'ditto') {
                                $itemSave['function_examine_user'] = $dittoFunUser;
                            } else {
                                $dittoFunUser = $itemSave['function_examine_user'];
                            }
                        } else {
                            $itemSave['function_examine_user'] = 0;
                        }
                        if (!isset($itemSave['status'])) {
                            $itemSave['status'] = 3;
                        }

                        // 其它入库可能没有完成单号
                        if (!isset($itemSave['enter_no'])) {
                            $itemSave['enter_no'] = '';
                        }

                        // 生成通用属性
                        $this->handleAttributes($itemSave);
                        $result = $this->model::query()->create($itemSave);
                        $resultId[] = $result->id;

                        // 申检时自动关联订单
                        $this->autoRelateOrder($itemSave,$result->id);

                        if (!empty($itemSave['appearance_examine_user']) && !in_array($itemSave['appearance_examine_user'], $appUser)) {
                            $appUser[] =    $itemSave['appearance_examine_user'];
                        }
                        if (!empty($itemSave['function_examine_user']) && !in_array($itemSave['function_examine_user'], $funUser)) {
                            $funUser[] =    $itemSave['function_examine_user'];
                        }

                        // 获取同样料号 prod_code 最新的 status_change_at 记录
                        $pre = $this->model::query()
                            ->where('prod_code', $result->prod_code)
                            ->where('id', '!=', $result->id) // 排除当前记录
                            ->orderBy('status_change_at', 'desc') // 按 status_change_at 时间倒序排列
                            ->first(); // 获取最新的记录

                        // 确保 inspection_flunk_count 最小值为 1
                        $flunk = max($pre->inspection_flunk_count - 1, 1);

                        $result = $result->update(['inspection_flunk_count' => $flunk]);
                    }
                } else {
                    // 其它入库可能没有完成单号
                    if (!isset($values['enter_no'])) {
                        $values['enter_no'] = '';
                    }
                    // 生成通用属性
                    $this->handleAttributes($values);
                    $result = $this->model::query()->create($values);
                    $resultId = $result->id;

                    //申检时自动关联订单
                    $this->autoRelateOrder($values,$result->id);

                    // 获取同样料号 prod_code 最新的 status_change_at 记录
                    $pre = $this->model::query()
                        ->where('prod_code', $result->prod_code)
                        ->where('id', '!=', $result->id) // 排除当前记录
                        ->orderBy('status_change_at', 'desc') // 按 status_change_at 时间倒序排列
                        ->first(); // 获取最新的记录

                    // 确保 inspection_flunk_count 最小值为 1
                    $flunk = max($pre->inspection_flunk_count - 1, 1);

                    $result = $result->update(['inspection_flunk_count' => $flunk]);
                }

                make(\App\Core\Services\Notice\NoticeService::class)->qcExamine($resultId);

                // 报检通知
                // $result = null;
                // if ($result) {
                //     $values['type_text'] = CategoryModel::query()->where('id', $values['type'])->value('name');
                //     // 负责人通知
                //     $noticeUsers = array_merge($exaUser, $appUser, $funUser);
                //     foreach ($noticeUsers as $aUser) {
                //         if (!empty($aUser)) {
                //             // $aUser = 181;
                //             make(\App\Core\Services\Notice\NoticeService::class)->qcExamine($aUser, $values);
                //         }
                //     }
                // }
            }
            Db::commit();
            return $result;
        } catch (\Throwable $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

    /**
     * 批量处理结果
     * @param array $items
     * @return bool
     */
    public function handleMulti(array $items)
    {
        // 需要推送外观检测员与功能检测员列表
        $ids = [];
        $uid = make(\Qbhy\HyperfAuth\AuthManager::class)->user()->getId();

        // 提取所有需要更新的 id 列表
        $itemIds = array_column($items, 'id');

        // 一次性查询出所有相关记录
        $rows = $this->model::query()->whereIn('id', $itemIds)->get()->keyBy('id'); // 将结果以 id 作为键
        $prodCodes = $rows->pluck('prod_code')->unique(); // 提取所有唯一的 prod_code

        // 获取所有相关 prod_code 的最新 status_change_at 记录
        $preRecords = $this->model::query()
            ->whereIn('prod_code', $prodCodes)
            ->whereNotIn('id', array_column($items, 'id')) // 排除当前 items 中的 id
            ->orderBy('status_change_at', 'desc') // 按 status_change_at 时间倒序排列
            ->get()
            ->groupBy('prod_code'); // 将结果按 prod_code 分组

        foreach ($items as &$item) {
            $row = $rows->get($item['id']);
            if (!$row) continue;
            // 自动添加功能检测人
            if (in_array('功能', $item['examine_option']) && empty($item['function_examine_user'])) {
                $item['function_examine_user'] = $uid;
            }
            // 自动添加外观检测人
            if (in_array('外观', $item['examine_option']) && empty($item['appearance_examine_user'])) {
                $item['appearance_examine_user'] = $uid;
            }
            // 自动添加检测人
            if (empty($item['examine_user'])) {
                $item['examine_user'] = $uid;
            }
            // 更新 status_change_at 字段为当前时间
            if (!empty($item['status']) && ($item['status'] == OaQcErpCode::STATUS_OK || $item['status'] == OaQcErpCode::STATUS_NO)) {
                $item['status_change_at'] = Carbon::now();

                // 从预先获取的记录中找到该料号的最新记录
                // $pre = $preRecords->get($row->prod_code)->first(); // 获取该料号最新的记录
                $preRecordsGroup = $preRecords->get($row->prod_code);
                $pre = $preRecordsGroup ? $preRecordsGroup->first() : null;

                if ($pre) {
                    if ($item['status'] == OaQcErpCode::STATUS_NO) {
                        // 设置检测失败次数为 5
                        $item['inspection_flunk_count'] = 5;
                    } else {
                        // 减少检测失败次数，但确保不小于 1
                        $item['inspection_flunk_count'] = max($pre->inspection_flunk_count - 1, 1);
                    }
                }
            }
            // 更新记录
            $row->update($item);

            // 检查 status 是否符合条件，记录需要推送的 id
            if (isset($item['status']) && ($item['status'] == OaQcErpCode::STATUS_OK || $item['status'] == OaQcErpCode::STATUS_NO)) {
                $ids[] = $item['id'];
            }
        }

        // 推送 QC 检验结果
        make(\App\Core\Services\Notice\NoticeService::class)->qcExamineResult($ids);

        return true;
    }

    public function handleAttributes(&$data)
    {
        // 判断pmpc分类
        if (!empty($data['prod_pmpc'])) {
            switch ($data['prod_pmpc']) {
                case TchipErpCode::PMPC_CODE_TPIC:
                    $data['pmpc_category'] = 'tpic';
                    break;
                case TchipErpCode::PMPC_CODE_PCB:
                    $data['pmpc_category'] = 'pcb';
                    break;
                default:
                    $data['pmpc_category'] = 'common';
            }
        } else {
            $data['pmpc_category'] = 'common';
        }
    }

    public function getPmpcCategoryList()
    {
        $result = $this->model->pmpcCategoryList;
        return array_values($result);
    }

    /**
     * 申检自动关联订单
     * @param $values
     * @param $oaQcId
     * @return void
     */
    public function autoRelateOrder($values,$oaQcId)
    {
        if(!empty($values['qc_type']) && !empty($values['order_no'])){
            switch ($values['qc_type']){
                case OaQcErpCode::QC_DONE_ENTER:
                    $productOrderId = ProductionOrderModel::query()->where('code',$values['order_no'])->value('id')?:0;
                    $productOrderId && make(ProductionOrderIqcService::class)->doEdit(-1,['production_order_id'=>$productOrderId,'oa_qc_id'=>$oaQcId]);
                    break;
                case OaQcErpCode::QC_ASSEMBLE_ENTER:
                    $assembleOrderId = AssembleOrderModel::query()->where('code',$values['order_no'])->value('id')?:0;
                    $assembleOrderId && make(AssembleOrderIqcService::class)->doEdit(-1,['assemble_order_id'=>$assembleOrderId,'oa_qc_id'=>$oaQcId]);
                    break;
                default:
                    break;
            }
        }
    }

    public function doDelete($ids): int
    {
        Db::beginTransaction();
        try {
            $ids = explode(',', $ids);
            make(ProductionOrderIqcService::class)->deleteRelation([],$ids);
            make(AssembleOrderIqcService::class)->deleteRelation([],$ids);
            $result = $this->model::destroy($ids);
            Db::commit();
            return $result;
        }catch (\Exception $e){
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

    /**
     * 导出excel文件
     */
    public function exportExcel(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        $list         = $this->getList($filter, $op, $sort, $order, $limit);
        $list['data'] = $list['data'] ?? [];
        $time         = date('ymdhis');
        $filename     = "质检数据导出-{$time}.xls";
        $sheetName    = 'sheet1';
        $excelWriter  = new ExcelWriter($filename, $sheetName);
        $option       = [
            '序号'     => 'index',
            '类型'     => 'type_text',
            '产品名称'   => 'prod_name',
            '规格'     => 'prod_spec',
            '料号'     => 'prod_code',
            '供应商'    => 'supply_name',
            '回货数量'   => 'num',
            '报捡数量'   => 'examine_num',
            '实际送检'   => 'real_examine_num',
            '不合格数'   => 'defective_num',
            '处理结果'   => 'status_text',
            '处理方案'   => 'handle_text',
            '批量处理方案' => 'unqualified_handle_text',
            '检验人'    => 'examine_user_name',
            '创建时间'   => 'created_day',
            '检验时间'   => 'status_change_at',
            '订单号'    => 'order_no',
            '入库号'    => 'enter_no',
            '归属地'    => 'attribution',
            '报检备注'   => 'examine_remark',
            '检验结果备注' => 'result_remark',
        ];
        $titleData    = [
            array_keys($option)
        ];

        $excelWriter->addData($titleData);
        $row = [];
        foreach ($list['data'] as $k => $v) {
            //获取目录
            $index = $k + 1;
            $tempRow = [];
            foreach ($option as $key => $value) {
                if($value ==='index'){
                    $tempRow[] = $index;
                }else{
                    $tempRow[] = $v[$value];
                }
            }
            $row = [
                $tempRow
            ];
            $excelWriter->addData($row);
        }
        $result = $excelWriter->download();
        $excelWriter->close();
        return $result;
    }

    /**
     * 模糊搜索供应商
     * @return array
     */
    public function getAllSuppliers(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 50)
    {
        // 确保有关键词
        if (!isset($filter['supplier']) || empty($filter['supplier'])) {
            return [];
        }
        
        $keyword = $filter['supplier'];
        
        // 以关键词作为缓存键
        $searchCacheKey = "qc_supplier_search:" . md5($keyword);
        
        // 尝试从缓存获取此关键词的搜索结果
        $cachedResult = $this->cache->get($searchCacheKey);
        if ($cachedResult !== null) {
            return $cachedResult;
        }

        // 缓存未命中，调用API
        $wepdService = make(\App\Core\Services\TchipSale\Erp\OrderPohService::class);
        $queryFilter = ['NAME' => $keyword];
        $queryOp = ['NAME' => 'LIKE'];
        $tmp = $wepdService->getSupplierList($queryFilter, $queryOp);
        
        // 提取供应商名称
        $suppliers = [];
        foreach ($tmp as $item) {
            if (!empty($item['NAME'])) {
                $suppliers[] = $item['NAME'];
            }
        }
        
        // 缓存搜索结果，3天有效期
        $this->cache->set($searchCacheKey, $suppliers, 3 * 86400);
        
        return $suppliers;

    }

    /**
     * 获取不合格数量
     * @return array
     */
    public function getDefectiveNum()
    {
        $defectiveNums = $this->model::query()
            ->whereNotNull('defective_num')
            ->distinct()
            ->orderBy('defective_num', 'asc')
            ->pluck('defective_num')
            ->toArray();
        return $defectiveNums;
    }


    /**
     * 获取仓库位置列表，只返回唯一的位置名称
     * @return array
     */
    public function getAttributionList()
    {
        // 构建缓存键
        $cacheKey = "qc_all_locations4";
        
        // 使用缓存组件
        $locations = $this->cache->get($cacheKey);
        
        // 如果缓存不存在，则从数据库获取
        if ($locations === null) {
            // 找出所有在OaQc表中使用的prod_loc值
            $prodLocs = $this->model::query()
                ->whereNotNull('prod_loc')
                ->where('prod_loc', '!=', '')
                ->distinct()
                ->pluck('prod_loc')
                ->toArray();
                    
            // 获取这些prod_loc对应的仓库信息，直接提取不重复的location值
            $locations = \App\Model\TchipBi\ErpWarehouseModel::query()
                ->whereIn('code', $prodLocs)
                ->whereNotNull('location')
                ->where('location', '!=', '')
                ->where('status', 1)
                ->distinct()
                ->pluck('location')
                ->toArray();
            
            // 将数据存入缓存，设置过期时间为1小时
            try {
                $this->cache->set($cacheKey, $locations, 3600);
            } catch (InvalidArgumentException $e) {
                // 缓存错误处理
            }
        }
        
        return $locations;
    }


    /**
     * 同步ERP订单列表的PMPCCODE(产品分类)
     */
    public function syncErpProductList()
    {
        $erpService = make(\App\Core\Services\TchipSale\ErpService::class);

        // 每次处理50条
        $this->model::query()->chunk(50, function ($rows) use ($erpService) {
            foreach ($rows as $item) {
                // $item是模型对象，不是数组
                $prod_code = $item->prod_code ?? '';
                if (empty($prod_code)) {
                    continue;
                }

                // 如果已有分类且为数字，跳过
                if (is_numeric($item->pmpc_category)) {
                    continue;
                }

                $goodsInfo = $erpService->getGoodsByMaterial($prod_code);
                if (empty($goodsInfo)) {
                    continue;
                }

                // 更新分类
                if (!empty($goodsInfo['PMPCCODE'])) {
                    $item->update(['pmpc_category' => $goodsInfo['PMPCCODE']]);
                }
            }
        });

        return true;
    }
}