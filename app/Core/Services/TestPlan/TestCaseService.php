<?php
    /**
     * @Copyright T-chip Team.
     * @Date 2024/09/10 09:17
     * <AUTHOR>
     * @Description
     */

    namespace App\Core\Services\TestPlan;

    use App\Constants\StatusCode;
    use App\Core\Services\BusinessService;
    use App\Core\Services\ExcelAnalyze\Writer\ExcelWriter;
    use App\Core\Utils\Tree;
    use App\Exception\AppException;
    use App\Constants\TestPlanCode;
    use App\Model\Redmine\AttachmentModel;
    use App\Model\Redmine\CategoryModel;
    use App\Model\Redmine\IssueCategoriesModel;
    use App\Model\Redmine\IssueModel;
    use App\Model\Redmine\ProjectModel;
    use App\Model\Redmine\TestCaseChangeModel;
    use App\Model\Redmine\TestCaseIssueRelationModel;
    use App\Model\Redmine\TestCaseLibraryModel;
    use App\Model\Redmine\TestCaseModel;
    use App\Model\Redmine\TestCaseStepModel;
    use App\Model\Redmine\TestDirectoryModel;
    use App\Model\Redmine\TestPlanCaseModel;
    use App\Model\Redmine\TestPlanModel;
    use App\Model\Redmine\UserModel;
    use Carbon\Carbon;
    use Exception;
    use Hyperf\DbConnection\Db;
    use Hyperf\Di\Annotation\Inject;
    use Hyperf\Redis\Redis;
    use Qbhy\HyperfAuth\AuthManager;

    class TestCaseService extends BusinessService
    {
        /**
         * @Inject()
         * @var AuthManager
         */
        protected $auth;

        /**
         * @Inject()
         * @var TestCaseModel
         */
        protected $model;

        /**
         * @Inject()
         * @var TestDirectoryModel
         */
        protected $testDirectoryModel;

        /**
         * @Inject()
         * @var TestCaseLibraryModel
         */
        protected $testCaseLibraryModel;

        /**
         * @Inject()
         * @var TestCaseStepModel
         */
        protected $testCaseStepModel;

        /**
         * @Inject()
         * @var TestCaseIssueRelationModel
         */
        protected $testCaseIssueRelationModel;

        /**
         * @Inject()
         * @var TestCaseChangeModel
         */
        protected $testCaseChangeModel;

        /**
         * @Inject()
         * @var \App\Model\TchipBi\AttachmentModel
         */
        protected $attachmentModel;

        private $redisInstance = null; // 缓存 Redis 实例

        /**
         * 获取 Redis 实例
         */
        private function getRedisInstance(): Redis
        {
            if (!$this->redisInstance) {
                $this->redisInstance = make(Redis::class); // 创建 Redis 实例
            }
            return $this->redisInstance;
        }

        public function getTreeListV2_1(array $data, int $parentId = 0, string $parentName = 'parent_id', string $sort = 'id', $order = 'DESC', $tree_depth = 0): array
        {
            $hashMap = [];
            foreach ($data as $item) {
                $hashMap[$item['parent_id']][] = $item;
            }
            // 递归生成树形结构
            return $this->buildTree($hashMap, $parentId, $tree_depth);
        }

        private function buildTree(array $hashMap, int $parentId, int $tree_depth): array
        {
            $treeList = [];
            if (!isset($hashMap[$parentId])) {
                return $treeList; // 没有子节点
            }

            foreach ($hashMap[$parentId] as $item) {
                $item['tree_depth'] = $tree_depth;
                $children = $this->buildTree($hashMap, $item['id'], $tree_depth + 1);

                // 计算当前节点及其子节点中的负 id 数量
                $negativeIdCount = $this->calculateNegativeIds($item, $children);

                if (!empty($children)) {
                    $item['children'] = $children;
                }

                // 将负 id 总数记录在当前节点中
                $item['case_count'] = $negativeIdCount;
                $treeList[] = $item;
            }

            return $treeList;
        }

        public function calculateNegativeIds(array $item, array $children): int
        {
            // 当前节点是否为负 id
            $negativeIdCount = $item['id'] < 0 ? 1 : 0;

            // 累加子节点中的负 id 数量
            foreach ($children as $child) {
                $negativeIdCount += $child['case_count'] ?? 0;
            }

            return $negativeIdCount;
        }


        public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
        {
            /* @var TestCaseModel $query */
            list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
            $paginate = $query->orderBy('sort_order', 'desc')->orderBy($sort, $order)->paginate($limit, ['*'], 'pageNo');
            return $paginate;
        }

        public function getOverView($id, $planCaseId = null)
        {
            $overView = $this->model::query()->with(['project', 'attachment', 'step', 'category', 'relationIssue', 'authorText', 'libInfo'])->find($id);
            if (!$overView) {
                return [];
            }

            $overViewArray = $overView->toArray();

            // 当这是一个在测试计划中打开的“公共用例”时
            if ($planCaseId && $overViewArray['project_id'] === -1) {
                // 1. 查询只属于这个测试计划实例的附件
                $planCaseAttachments = make(\App\Model\Redmine\AttachmentModel::class)::query()
                    ->where('container_type', 'TestPlanCase')
                    ->where('container_id', $planCaseId)
                    ->get()->toArray();

                // 2. 在数组层面【替换】附件列表
                $overViewArray['attachment'] = $planCaseAttachments;

                return $overViewArray;
            }

            // // 这里可以对后续有扩展 可以同时兼容私有附件和公共附件
            // if ($planCaseId) {
            //     $planCaseAttachments = make(\App\Model\Redmine\AttachmentModel::class)::query()
            //         ->where('container_type', 'TestPlanCase')
            //         ->where('container_id', $planCaseId)
            //         ->get()->toArray();

            //     foreach ($planCaseAttachments as &$attachment) {
            //         $attachment['is_instance'] = true;
            //     }

            //     $overViewArray['attachment'] = array_merge($overViewArray['attachment'], $planCaseAttachments);
            // }

            return $overViewArray;
        }

        /**
         * 生成变更日志
         *
         * @param int   $id
         * @param mixed $row
         * @param array $newValues
         * @return array
         */
        private function logChanges(int $id, $row, array $newValues, string $changeType = TestPlanCode::CASE_CHANGE_CASE, int $relativeId = null, $oldJsonValues = null, $newJsonValues = null): array
        {
            $changes = [];

            $saveAsJson = false;
            if ($changeType == TestPlanCode::CASE_CHANGE_STEP) {
                $saveAsJson = true;
            }

            foreach ($newValues as $field => $newValue) {
                // 检查 row 是否为数组，采用数组访问方式
                if (is_array($row)) {
                    $oldValue = $row[$field] ?? null; // 如果不存在该字段，默认为 null
                } else {
                    $oldValue = $row->$field ?? null; // 如果 row 是对象，使用对象访问方式
                }

                if ($oldValue !== $newValue) {
                    $changes[] = [
                        'test_case_id' => $id,
                        'relative_id'  => $relativeId,
                        'changed_by'   => getRedmineUserId(),
                        'change_type'  => $changeType,
                        'change_field' => $field,
                        'old_value'    => $saveAsJson ? $oldJsonValues : $oldValue,
                        'new_value'    => $saveAsJson ? $newJsonValues : $newValue,
                        'created_at'   => date('Y-m-d H:i:s'),
                    ];
                }
            }
            return $changes;
        }

        public function doEdit($id, $values): int
        {
            if (isset($values['category_id']) && $values['category_id'] === '') {
                $values['category_id'] = null;
            }
            if (isset($values['tmpPrecondition'])) {
                unset($values['tmpPrecondition']);
            }
            if (isset($values['estimated_hours']) && empty($values['estimated_hours']) && $values['estimated_hours'] != '0') {
                unset($values['estimated_hours']);
            }
            DB::connection('tchip_redmine')->beginTransaction();
            try {
                if ($id > 0) {
                    $row = $this->model::query()->find($id);
                    if (!$row) {
                        throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                    }

                    // 比较新旧值并记录变更日志
                    $changes =  $this->logChanges($id, $row, $values);

                    $row->update($values);

                    // 用例复制
                    if (!empty($values['copyCase'])) {
                        $nodeData = $values['copyCase'];
                        $this->copyCase($nodeData, $row->project_id, $row->library_id, $row->directory_id);
                        DB::connection('tchip_redmine')->commit();
                        return 1;
                    }
                } else {
                    $row = $this->model::query()->create($values);
                }

                // 插入变更日志
                if (!empty($changes)) {
                    DB::connection('tchip_redmine')->table('test_case_change')->insert($changes);
                }

                DB::connection('tchip_redmine')->commit();
                // 返回记录的 id，无论是更新的还是新创建的
                return $row->id;
            } catch (AppException $e) {
                DB::connection('tchip_redmine')->rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        public function doBatchEdit($rows = [], $params = [])
        {
            // 分类数据
            $cases = array_filter($rows, function ($row) {
                return isset($row['type']) && $row['type'] === 'case';
            });
            $directories = array_filter($rows, function ($row) {
                return isset($row['type']) && $row['type'] === 'directory';
            });

            $dirIds = [];
            foreach ($directories as $directory) {
                $dirIds = array_merge($dirIds, $this->getAllChildDirectoryIds($directory['id']));
            }

            $casesSearchByDirIds = $this->model::query()->whereIn('directory_id', $dirIds)->get()->toArray();
            $cases = array_merge($cases, $casesSearchByDirIds);

            $projectId = $cases[0]['project_id'];

            // 存在将用例转移到其他项目的操作
            if (!empty($params['project_id']) && $params['project_id'] != $projectId) {
                $casesIds = array_column($cases, 'id');
                $casesQueryFromPlan = TestPlanCaseModel::query()->whereIn('test_case_id', $casesIds)->get()->toArray();
                $planIds  = array_column($casesQueryFromPlan, 'test_plan_id');
                $planNames = TestPlanModel::query()->whereIn('id', $planIds)->get()->toArray();
                $planNames = array_column($planNames, 'title', 'id');
                $casesGroupById = array_column($cases, null, 'id');

                // 存在于其他计划中的用例名单 需要二次确认
                foreach ($casesQueryFromPlan as &$c) {
                    $c['plan_name'] = $planNames[$c['test_plan_id']];
                    $c['title'] = $casesGroupById[$c['test_case_id']]['title'];
                }

                // 被使用用例名单非空，不修改传入字段
                if (!empty($casesQueryFromPlan)) {
                    return [
                        'data' => [
                            'data' => $casesQueryFromPlan,
                            'code' => 400,
                        ],
                    ];
                }

            }

            foreach ($cases as $case) {
                $this->doEdit($case['id'], $params);
            }

            return 1;
        }

        public function doDelete($ids): int
        {
            DB::connection('tchip_redmine')->beginTransaction();
            try {
                if (is_string($ids)) {
                    $ids = explode(',', $ids);
                } else {
                    $ids = is_array($ids) ? $ids : [$ids];
                }

                $this->model::query()->whereIn('id', $ids)->delete();

                DB::connection('tchip_redmine')->commit();
                return 1;
            } catch (AppException $e) {
                DB::connection('tchip_redmine')->rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        // 获取递归子孙目录ID的函数
        protected function getAllChildDirectoryIds($parentId)
        {
            // 初始ID列表包含自身
            $directoryIds = [$parentId];

            // 递归查找所有子孙目录
            $childDirectories = TestDirectoryModel::where('parent_id', $parentId)->get(['id']);
            foreach ($childDirectories as $child) {
                // 将当前目录的ID加入到ID列表中
                $directoryIds = array_merge($directoryIds, $this->getAllChildDirectoryIds($child->id));
            }

            return $directoryIds;
        }

        public function getDirList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10, int $page = 1)
        {
            $filterCaseUnion = false; // 是否混杂case一同筛选
            if (!empty($filter['id']) && ($filter['id'] < 0 || $filter['id'] == -3)) {
                unset($filter['id']);
                $filterCaseUnion = true;
            }

            // priority_id查询参数处理
            $tempPriority = null;
            if (!empty($filter['priority'])) {
                $tempPriority = $filter['priority'];
                unset($filter['priority']);
            }

            /* @var TestDirectoryModel $query */
            $query = $this->testDirectoryModel::query();
            list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
            //$query = $query->with(['project', 'authorText']); // unionAll之后不保留预加载关联数据  一次查询数千条数据情况下with非常耗时

            ////////////////////////////////开始 unionAll合并查询
            // 使用方式1.获取对应目录id下的用例与目录用以可分页列表展示
            if (isset($filter['parent_id']) && $filter['parent_id'] >= 0) {

                $isGetDirData = empty($filter['category_id']) && empty($filter['id']);
                // 目录部分的数据 获取
                if ($isGetDirData) {
                    $query = $query->select(['id', 'project_id', 'parent_id as directory_id', 'library_id', 'title', 'description', 'created_at','created_by',
                        DB::raw("NULL as priority"), DB::raw("NULL as estimated_hours"),  DB::raw("NULL as category_id"), 'sort_order'])
                        ->withCount('case')
                        ->addSelect(DB::raw("'directory' as type"));
                }

                // 用例部分的数据 获取
                /* @var TestCaseModel $query2 */
                $query2 = $this->model::query();
                list($query2, $limit, $sort, $order) = $this->buildparams(['directory_id' => $filter['parent_id'], 'library_id' => $filter['library_id']], [], $sort, $order, $limit, $query2);

                // 如果有 category_id，手动加入 category_id 的查询条件
                if (!empty($filter['category_id'])) {
                    $categoryIds = explode(',', $filter['category_id']);
                    $query2 = $query2->whereIn('category_id', $categoryIds);
                }
                // 如果有 title，手动加入 title 的查询条件
                if (!empty($filter['title'])) {
                    $query2 = $query2->where('title', 'like', "%{$filter['title']}%");
                }
                // 如果有 Priority，手动加入 Priority 的查询条件
                if ($tempPriority && $tempPriority != '') {
                    $prioritys = explode(',', $tempPriority);
                    $query2 = $query2->whereIn('priority', $prioritys);
                }
                // 存在id过滤条件
                if (!empty($filter['id'])) {
                    $query2 = $query2->where('id', $filter['id']);
                }
                // 存在created_by过滤条件
                if (!empty($filter['created_by'])) {
                    $query2 = $query2->where('created_by', $filter['created_by']);
                }

                $query2 = $query2->select(['id', 'project_id', 'directory_id', 'library_id', 'title', 'description', 'created_at', 'created_by',
                    'priority', 'estimated_hours', 'category_id', 'sort_order',
                    DB::raw("NULL as case_count")])
                    ->addSelect(DB::raw("'case' as type"));

                $result = $isGetDirData ? $query->unionAll($query2) : $query2;
                $paginate = $result
                    ->orderBy(DB::raw("CASE WHEN type = 'directory' THEN 0 ELSE 1 END")) // 保证目录皆在事项上方
                    ->orderBy('sort_order', 'desc')
                    ->orderBy($sort, $order)
                    ->paginate($limit, ['*'], 'pageNo', $page); // 没走中间件处理，默认的'page'未生成只有'pageNo'参数 中间件方法中未对post请求做处理

                // 将 $paginate 转换为数组
                $paginate = $paginate ? $paginate->toArray() : [];

                $categoryIds = array_column(array_filter($paginate['data'], function ($item) {
                    return $item['type'] === 'case' && isset($item['category_id']);
                }), 'category_id');
                $categoryIds = array_unique($categoryIds);
                $categories = IssueCategoriesModel::whereIn('id', $categoryIds)->get()->keyBy('id');
                $categories = $categories ? $categories->toArray() : [];


                $authorIds = array_column($paginate['data'], 'created_by');
                $authorIds = array_unique($authorIds);
                $authors = UserModel::whereIn('id', $authorIds)->get()->keyBy('id');
                $authors = $authors ? $authors->toArray() : [];


                // 提取所有目录的ID
                $directoryIds = [];
                foreach ($paginate['data'] as $item) {
                    if ($item['type'] === 'directory') {
                        $directoryIds[] = $item['id'];
                    }
                }
                // 获取所有目录的子孙目录ID映射
                $directoryIdMap = [];
                foreach ($directoryIds as $directoryId) {
                    $directoryIdMap[$directoryId] = $this->getAllChildDirectoryIds($directoryId); // 获取每个目录及其所有子目录ID
                }
                // 统计所有目录及其子孙目录的 case_count
                $caseCounts = TestCaseModel::whereIn('directory_id', array_merge(...array_values($directoryIdMap)))
                    ->select('directory_id', DB::raw('COUNT(*) as count'))
                    ->groupBy('directory_id')
                    ->get()
                    ->pluck('count', 'directory_id')
                    ->toArray();
                // 合并各目录及其子孙目录的 case_count
                $totalCaseCounts = [];
                foreach ($directoryIdMap as $parentId => $childIds) {
                    //$totalCaseCounts[$parentId] = array_sum(array_map(fn($id) => $caseCounts[$id] ?? 0, $childIds));
                    $totalCaseCounts[$parentId] = array_sum(array_map(function ($id) use ($caseCounts) {
                        return $caseCounts[$id] ?? 0;
                    }, $childIds));
                }


                foreach ($paginate['data'] as &$item) {
                    if ($item['type'] === 'case' && !empty($categories)) {
                        // 根据 category_id 从预加载的 categories 集合中找到对应的 category_text
                        $item['category_text'] = $item['category_id'] ? ($categories[$item['category_id']] ?? null) : null;
                    }
                    $item['author_text'] = $item['created_by'] ? ($authors[$item['created_by']] ?? null) : null;

                    // 统计目录下用例数量
                    if ($item['type'] === 'directory') {
                        $item['case_count'] = $totalCaseCounts[$item['id']] ?? 0;
                    }

                }

                return $paginate;

            }
            ////////////////////////////////结束

            $paginate = $query
                ->orderBy('sort_order', 'desc')
                ->orderBy($sort, $order)
                ->paginate($limit, ['*'], 'pageNo', $page);
            $paginate = $paginate ? $paginate->toArray() : [];

            // 使用方式2.未去筛选具体的目录 获取所有目录下用例拼接到对应目录下（整体展现 无分页）
            if ($filterCaseUnion) {
                $dirIds = implode(',', array_column($paginate['data'], 'id'));
                $cases = $this->getList(['directory_id' => $dirIds,  'library_id' => $filter['library_id']], ['directory_id' => 'IN'], 'id', 'desc', 99999);
                $cases = $cases->toArray() ?? [];

                // 使用 array_map 为每个元素添加 parent_id 字段
                $cases['data'] = array_map(function ($case) {
                    $case['parent_id'] = $case['directory_id']; // 将 directory_id 的值赋给 parent_id
                    $case['case_id'] = $case['id'];
                    $case['id'] = $case['id'] * -1; // 1防止参与树形结构构造
                    return $case;
                }, $cases['data']);

                // 后追加$cases['data']，保证目录皆在事项上方
                $paginate['data'] = array_merge($paginate['data'], $cases['data']);
            }
            // 过滤孤立节点并将其删除  20241202以处理遗留问题：原目录删除时未将子层级目录遍历删除
            $orphans = $this->filterOrphans($paginate['data'], 'parent_id');
            if (!empty($orphans)) {
                $this->doDeleteDir($orphans);
            }
            $paginate['flattenedData'] = $paginate['data'];
            $paginate['data'] = $this->getTreeListV2_1($paginate['data'], 0, 'parent_id', 'id');
            return $paginate;
        }

        private function filterOrphans(array $data, string $parentName = 'parent_id'): array
        {
            $parentIds = array_column($data, 'id'); // 所有的父节点ID
            $orphans = []; // 存储孤立节点

            foreach ($data as $item) {
                // 如果 parent_id 不存在于父节点ID列表中，且不为0，则为孤立节点
                if ($item['id'] > 0 && $item[$parentName] !== 0 && !in_array($item[$parentName], $parentIds)) {
                    $orphans[] = $item['id'];
                }
            }
            return $orphans;
        }

        public function updateChildPaths($parentId, $parentPath)
        {
            // 查找所有直接子目录
            $childDirs = $this->testDirectoryModel::query()->where('parent_id', $parentId)->get();

            foreach ($childDirs as $dir) {
                // 【关键逻辑】此处会在path字段后添加-{id}后缀，用于构建层级路径结构
                // 如果有父路径，则在父路径后添加当前目录ID，格式为：父路径-当前ID
                // 如果没有父路径，则直接使用当前目录ID作为路径
                $dir->path = $parentPath ? $parentPath . '-' . $dir->id : (string) $dir->id;
                $dir->save(); // 保存更新

                // 递归更新所有子目录的 path
                $this->updateChildPaths($dir->id, $dir->path);
            }
        }

        public function doEditDir($id, $values, $returnType = '')
        {
            DB::connection('tchip_redmine')->beginTransaction();
            try {
                if ($id > 0) {
                    $row = $this->testDirectoryModel::query()->find($id);
                    $parentDirId = $values['parent_id'] ?? $row->parent_id;
                    if (!isset($values['path']) && !!$parentDirId) {
                        // 获取父目录信息
                        $dirQuery = $this->testDirectoryModel::query()->find($parentDirId);

                        // 检查是否找到父目录
                        if ($dirQuery) {
                            $parentPath = $dirQuery->path != '' ? $dirQuery->path . '-' . $parentDirId : $parentDirId;
                        } else {
                            $parentPath = '';  // 如果没有父目录，则路径为空
                        }

                        $parentPath .= '-' . $row->id; // 添加自身id作为后缀

                        $values['path'] = $parentPath;
                    }

                    if (!$row) {
                        throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                    }
                    $values['updated_by'] = getRedmineUserId();
                    $row->update($values);
                    // 【触发路径更新】更新该目录及其所有子目录的 path，使子目录path字段添加-{id}后缀
                    $this->updateChildPaths($row->id, $row->path);
                } else {
                    $parentId = $values['parent_id'] ?? null;
                    if ($parentId) {
                        $dirQuery = $this->testDirectoryModel::query()->find($parentId);
                        if ($dirQuery) {
                            // 【路径构建逻辑】如果父目录已有路径，则在父路径后添加父目录ID，格式为：父路径-父ID
                            // 如果父目录没有路径，则直接使用父目录ID作为路径
                            $parentPath = $dirQuery->path != '' ? $dirQuery->path . '-' . $parentId : $parentId;
                        } else {
                            $parentPath = '';
                        }
                    } else {
                        $parentPath = '';
                    }

                    $values['path'] = $parentPath;
                    $values['created_by'] = getRedmineUserId();
                    $row = $this->testDirectoryModel::query()->create($values);
                    // 此处创建目录后，需要更新该目录的path字段以包含自身ID

                    $row->path = $parentPath ? $parentPath . '-' . $row->id : (string) $row->id;
                    $row->save(); // 保存更新
                }
                DB::connection('tchip_redmine')->commit();
                return $returnType == 'Builder' ? $row : $row->id;
            } catch (AppException $e) {
                DB::connection('tchip_redmine')->rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        public function doDeleteDir($ids)
        {
            DB::connection('tchip_redmine')->beginTransaction();
            try {
                if (is_string($ids)) {
                    $ids = explode(',', $ids);
                } else {
                    $ids = is_array($ids) ? $ids : [$ids];
                }
                if (empty($ids)) {
                    throw new AppException(StatusCode::ERR_SERVER, '请选择要删除的目录');
                }
                // 获取所有需要删除的目录 ID，包括子层级的目录
                $allIdsToDelete = $this->getAllDescendantIds($ids);

                $this->testDirectoryModel::query()->whereIn('id', $allIdsToDelete)->delete();

                // 删除 test_case 表中引用这些目录的记录
                $this->model::query()->whereIn('directory_id', $allIdsToDelete)->delete();
                DB::connection('tchip_redmine')->commit();
                return ['status' => 'success', 'msg' => '删除成功'];
            } catch (AppException $e) {
                DB::connection('tchip_redmine')->rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        /**
         * 获取所有子层级的 ID
         *
         * @param array $ids
         * @return array
         */
        private function getAllDescendantIds(array $ids): array
        {
            // 初始化结果集合
            $allIds = $ids;

            do {
                // 查找当前 IDs 的子级
                $childIds = $this->testDirectoryModel::query()
                    ->where(function ($query) use ($allIds) {
                        foreach ($allIds as $id) {
                            $query->orWhere('path', 'LIKE', "%-$id%")
                                ->orWhere('path', 'LIKE', "$id-%")
                                ->orWhere('path', '=', "$id");
                        }
                    })
                    ->pluck('id')
                    ->toArray();

                // 将新的子级加入集合
                $newIds = array_diff($childIds, $allIds);
                $allIds = array_merge($allIds, $newIds);
            } while (!empty($newIds)); // 当无新的子级时停止

            return $allIds;
        }

        public function getLibList(array $filter = [], array $op = [], string $sort = 'sort', string $order = 'DESC', int $limit = 10)
        {
            /* @var TestCaseLibraryModel $query */
            $query = $this->testCaseLibraryModel::query();
            list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, $query);
            $paginate = $query
                ->with(['authorText', 'editorText'])
                ->withCount('case')
                ->where('id', '>', 0)->orderBy($sort, $order)->paginate($limit, ['*'], 'pageNo');
            $paginate = $paginate ? $paginate->toArray() : [];

            // 需要给每个项目初始化一个版本
            $firstGet = count($filter) == 1 && isset($filter['project_id']); // 首次进入测试用例模块获取版本之时
            if (empty($paginate['data']) && $firstGet) {
                $value = [
                    'name' => '基线版本',
                    'project_id' => $filter['project_id'],
                    'description' => '默认版本',
                    'created_by' => getRedmineUserId(),
                ];
                $this->doEditLib(-1, $value);
                $paginate = $query->where('id', '>', 0)->orderBy($sort, $order)->paginate($limit, ['*'], 'pageNo');
                $paginate = $paginate ? $paginate->toArray() : [];

            }

            return $paginate;
        }

        public function doEditLib($id, $values)
        {
            DB::connection('tchip_redmine')->beginTransaction();
            try {
                if ($id > 0) {
                    $row = $this->testCaseLibraryModel::query()->find($id);
                    if (!$row) {
                        throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                    }
                    $values['updated_by'] = getRedmineUserId();
                    $row->update($values);
                } else {
                    $values['created_by'] = getRedmineUserId();
                    $row = $this->testCaseLibraryModel::query()->create($values);
                }
                // 创建时的用例与目录复制
                if (!empty($values['copyCase'])) {
                    $treeData = [];
                    foreach ($values['copyCase'] as $node) {
                        if (!empty($node['children'])) {
                            // 将子节点添加到 $treeData 中
                            $treeData = array_merge($treeData, $node['children']);
                        }
                    }
                    $this->copyCaseAndDir($treeData, $row->project_id, $row->id);
                }
                DB::connection('tchip_redmine')->commit();
                return $row->id;
            } catch (AppException $e) {
                DB::connection('tchip_redmine')->rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        public function doDeleteLib($ids)
        {
            DB::connection('tchip_redmine')->beginTransaction();
            try {
                if (is_string($ids)) {
                    $ids = explode(',', $ids);
                } else {
                    $ids = is_array($ids) ? $ids : [$ids];
                }

                $this->testCaseLibraryModel::query()->whereIn('id', $ids)->delete();

                DB::connection('tchip_redmine')->commit();
                return ['status' => 'success', 'msg' => '删除成功'];
            } catch (AppException $e) {
                DB::connection('tchip_redmine')->rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        public function getStepList($testCaseId)
        {
            $query = $this->testCaseStepModel->query()->where('test_case_id', $testCaseId)->orderBy('step_order', 'ASC')->get();
            return $query->toArray() ?? [];
        }

        public function doEditStep($id, $values, $notToLog = false)
        {
            $uid = getRedmineUserId();
            $cacheKey = 'case_step_last_edit_time_' . $uid . '_' . $values['test_case_id'];

            $redis = $this->getRedisInstance(); // 使用共享的 Redis 实例
            DB::connection('tchip_redmine')->beginTransaction();
            try {
                if ($id > 0) {
                    $row = $this->testCaseStepModel::query()->find($id);
                    if (!$row) {
                        throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                    }

                    $oldValues = $row->toArray();
                    $oldStepList = $this->getStepList($row->test_case_id);

                    $row->update($values);

                    $newStepList = $this->getStepList($row->test_case_id);

                    $oldJsonValues = json_encode($oldStepList);
                    $newJsonValues = json_encode($newStepList);

                    $changes = $this->logChanges($row->test_case_id, $oldValues, $values, TestPlanCode::CASE_CHANGE_STEP, $row->id, $oldJsonValues, $newJsonValues);

                    //// 插入变更日志
                    //if (!empty($changes)) {
                    //    DB::connection('tchip_redmine')->table('test_case_change')->insert($changes);
                    //}
                } else {
                    $oldStepList = $this->getStepList($values['test_case_id']);

                    $query = $this->testCaseStepModel::query()->create($values);

                    $newStepList = $this->getStepList($values['test_case_id']);

                    $oldJsonValues = json_encode($oldStepList);
                    $newJsonValues = json_encode($newStepList);

                    // 旧新 值记录变更日志
                    $changes = $notToLog ? [] : $this->logChanges($values['test_case_id'], ['step_id' => null], ['step_id' => $query->id], TestPlanCode::CASE_CHANGE_STEP, $query->id, $oldJsonValues, $newJsonValues);
                    //// 插入变更日志
                    //if (!empty($changes)) {
                    //    DB::connection('tchip_redmine')->table('test_case_change')->insert($changes);
                    //}
                }
                if (!empty($changes)) {
                    $this->cacheOldAndNewValues($redis, $cacheKey, json_encode($changes[0]['old_value']), json_encode($changes[0]['new_value']));
                    $this->scheduleInsert($cacheKey, $redis, $changes); // 异步插入逻辑
                }

                DB::connection('tchip_redmine')->commit();
                return 1;
            } catch (AppException $e) {
                DB::connection('tchip_redmine')->rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }
        /**
         * 缓存首次的 $oldJsonValues 和最后一次的 $newJsonValues
         */
        private function cacheOldAndNewValues(Redis $redis, string $cacheKey, $oldJsonValues, $newJsonValues)
        {
            // 尝试获取已有的缓存数据
            $cachedData = $redis->get($cacheKey);
            if ($cachedData) {
                $cachedData = json_decode($cachedData); // 默认返回 stdClass 对象

                // 如果 `oldJsonValues` 已存在，保持不变，只更新 `newJsonValues`
                // 使用对象方式访问
                $oldJsonValues = $cachedData->oldJsonValues ?? $oldJsonValues; // 使用对象属性
            }

            // 写入缓存
            $redis->set($cacheKey, json_encode([
                'oldJsonValues' => $oldJsonValues, // 保留首次的值
                'newJsonValues' => $newJsonValues, // 更新最新的值
            ]), ['EX' => 4]); // 设置 4 秒有效期
        }



        /**
         * 异步插入逻辑，使用首次的 $oldJsonValues 和最后一次的 $newJsonValues
         */
        private function scheduleInsert(string $cacheKey, Redis $redis, array $changes)
        {
            \Swoole\Timer::after(3000, function () use ($cacheKey, $redis, $changes) {
                // 从缓存中获取数据
                $cachedData = $redis->get($cacheKey);

                if ($cachedData) {
                    $cachedData = json_decode($cachedData); // 默认返回 stdClass 对象

                    // 使用对象方式访问 oldJsonValues 和 newJsonValues
                    $oldJsonValues = $cachedData->oldJsonValues ?? null;
                    $newJsonValues = $cachedData->newJsonValues ?? null;

                    // 解析 oldJsonValues 和 newJsonValues，如果它们是 JSON 字符串（带有反斜杠）
                    if ($oldJsonValues) {
                        $oldJsonValues = json_decode($oldJsonValues, true);  // 将其解析为数组
                    }
                    if ($newJsonValues) {
                        $newJsonValues = json_decode($newJsonValues, true);  // 同样解析为数组
                    }

                    if ($oldJsonValues && $newJsonValues) {
                        // 更新 $changes 中的 old_value 和 new_value
                        foreach ($changes as &$change) {
                            $change['old_value'] = $oldJsonValues;
                            $change['new_value'] = $newJsonValues;
                        }

                        // 插入到数据库
                        DB::connection('tchip_redmine')->table('test_case_change')->insert($changes);

                        // 清除缓存
                        $redis->del($cacheKey);
                    }
                }
            });
        }


        public function doDeleteStep($ids)
        {
            $uid = getRedmineUserId();
            $redis = $this->getRedisInstance(); // 使用共享的 Redis 实例
            DB::connection('tchip_redmine')->beginTransaction();
            try {
                if (is_string($ids)) {
                    $ids = explode(',', $ids);
                } else {
                    $ids = is_array($ids) ? $ids : [$ids];
                }

                $row = $this->testCaseStepModel::query()->find((int)$ids[0]);

                $cacheKey = 'case_step_last_edit_time_' . $uid . '_' . $row->test_case_id;

                $query = $this->testCaseStepModel::query()->whereIn('id', $ids);
                $relations = $query->get(); // 查询所有符合条件的记录

                $oldValues = $row->toArray();
                $newValues = json_decode(json_encode($oldValues), true);  // 创建深拷贝
                $newValues['step'] = [];

                $oldStepList = $this->getStepList($row->test_case_id);

                $query->delete();
                DB::connection('tchip_redmine')->commit();

                $newStepList = $this->getStepList($row->test_case_id);
                $oldJsonValues = json_encode($oldStepList);
                $newJsonValues = json_encode($newStepList);

                $changes = $this->logChanges($row->test_case_id, $oldValues, $newValues, TestPlanCode::CASE_CHANGE_STEP, $row->id, $oldJsonValues, $newJsonValues);

                //if (!empty($changes)) {
                //    DB::connection('tchip_redmine')->table('test_case_change')->insert($changes);
                //}
                if (!empty($changes)) {
                    $this->cacheOldAndNewValues($redis, $cacheKey, json_encode($changes[0]['old_value']), json_encode($changes[0]['new_value']));
                    $this->scheduleInsert($cacheKey, $redis, $changes); // 异步插入逻辑
                }

                return ['status' => 'success', 'msg' => '删除成功'];
            } catch (AppException $e) {
                DB::connection('tchip_redmine')->rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        public function doEditIssueRelation($id, $values, $notToLog = false)
        {
            DB::connection('tchip_redmine')->beginTransaction();
            try {
                if ($id > 0) {
                    $row = $this->testCaseIssueRelationModel::query()->find($id);
                    if (!$row) {
                        throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                    }
                    $row->update($values);
                } else {
                    // 旧新 值记录变更日志
                    $changes = $notToLog ? [] : $this->logChanges($values['test_case_id'], ['attachment_id' => null], ['attachment_id' => $values['issue_id']]);

                    $this->testCaseIssueRelationModel::query()->create($values);
                    // 插入变更日志
                    if (!empty($changes)) {
                        DB::connection('tchip_redmine')->table('test_case_change')->insert($changes);
                    }
                }
                DB::connection('tchip_redmine')->commit();
                return 1;
            } catch (AppException $e) {
                DB::connection('tchip_redmine')->rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        public function doDeleteIssueRelation($ids)
        {
            DB::connection('tchip_redmine')->beginTransaction();
            try {
                if (is_string($ids)) {
                    $ids = explode(',', $ids);
                } else {
                    $ids = is_array($ids) ? $ids : [$ids];
                }
                $query = $this->testCaseIssueRelationModel::query()->whereIn('id', $ids);
                $relations = $query->get(); // 查询所有符合条件的记录

                $allChanges = [];

                foreach ($relations as $relation) {
                    // 获取 test_case_id
                    $testCaseId = $relation->test_case_id;
                    // 旧新值记录变更日志
                    $changes = $this->logChanges($testCaseId, ['relation_issue_id' => $relation->issue_id], ['relation_issue_id' => null]);
                    // 将每个 change 记录添加到 $allChanges 数组中
                    if (!empty($changes)) {
                        $allChanges = array_merge($allChanges, $changes);
                    }
                }

                $query->delete();

                if (!empty($allChanges)) {
                    DB::connection('tchip_redmine')->table('test_case_change')->insert($allChanges);
                }

                DB::connection('tchip_redmine')->commit();
                return ['status' => 'success', 'msg' => '删除成功'];
            } catch (AppException $e) {
                DB::connection('tchip_redmine')->rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }

        public function updateAttachment($id, $values, $notToLog = false)
        {
            $param = [
                'container_id' => $values['container_id'],
                'container_type' => $values['container_type'],
            ];
            $query = make(\App\Model\Redmine\AttachmentModel::class)::query()->find($id);
            if ($query) {
                // 旧新 值记录变更日志
                $changes = $notToLog ? [] : $this->logChanges($values['container_id'], ['attachment_id' => null], ['attachment_id' => $id]);

                $query->fill($param);
                $query->save();

                // 插入变更日志
                if (!empty($changes)) {
                    DB::connection('tchip_redmine')->table('test_case_change')->insert($changes);
                }
                return true;
            }
            return false;

        }

        public function deleteAttachment($id)
        {
            $query = make(\App\Model\Redmine\AttachmentModel::class)::query()->find($id);
            if ($query) {
                try {
                    // 旧新 值记录变更日志
                    $changes = $this->logChanges($query->container_id, ['attachment_id' => $id], ['attachment_id' => null]);
                    $query->delete();
                    // 插入变更日志
                    if (!empty($changes)) {
                        DB::connection('tchip_redmine')->table('test_case_change')->insert($changes);
                    }
                } catch (\Exception $e) {
                    return false;
                }
                return true;
            }
            return false;

        }

        public function getCategoryList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 9999, $startDate = null, $endDate = null)
        {
            $filterTestPlanId = null;
            if (isset($filter['test_plan_id'])) {
                $filterTestPlanId = $filter['test_plan_id'];
                unset($filter['test_plan_id']);
            }
            $cateTable = 'issue_categories';
            $extTable = 'issue_categories_ext';
            $cateList = IssueCategoriesModel::query()->selectRaw("{$cateTable}.*, IFNULL({$extTable}.position, {$cateTable}.id) as position")
                ->where('project_id', $filter['project_id'] ?? 0)
                ->leftJoin($extTable, "{$cateTable}.id", '=', "{$extTable}.category_id")
                ->withCount(['cases' => function ($query) use ($filter, $op, $filterTestPlanId) {
                    list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, 'id', 'desc', 999, $query);
                    // 连接 `test_plan_case` 和 `test_case` 表，并添加过滤条件统计当前测试计划下的用例
                    if ($filterTestPlanId) {
                        $query->leftJoin('test_plan_case', 'test_plan_case.test_case_id', '=', 'test_case.id')
                            ->where('test_plan_case.test_plan_id', '=', $filterTestPlanId)
                            ->whereNull('test_plan_case.deleted_at');
                    }
                    return $query;
                } ])->orderBy('position', 'asc')->get();
            return $cateList;
        }

        public function getCaseChangeLogList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
        {
            $query = $this->testCaseChangeModel::query();
            list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit, $query);

            $paginate = $query->with(['userInfo'])
                ->orderBy($sort, $order)
                ->paginate($limit, ['*'], 'pageNo')
                ->toArray() ?? [];

            // 根据id获取对应text column一般为对应表中需要获取的的name或者title
            // 动态存储需要映射的字段及其对应的ID
            $fieldsMap = [
                'relation_issue_id' => ['new' => [], 'old' => [], 'model' => \App\Model\Redmine\IssueModel::class, 'column' => 'subject'],
                'attachment_id' => ['new' => [], 'old' => [], 'model' => \App\Model\Redmine\AttachmentModel::class, 'column' => 'filename'],
                'directory_id' => ['new' => [], 'old' => [], 'model' => TestDirectoryModel::class, 'column' => 'title'],
                'category_id' => ['new' => [], 'old' => [], 'model' => IssueCategoriesModel::class, 'column' => 'name'],
                'project_id' => ['new' => [], 'old' => [], 'model' => ProjectModel::class, 'column' => 'name'],
                'library_id' => ['new' => [], 'old' => [], 'model' => TestCaseLibraryModel::class, 'column' => 'name'],
            ];

            // 遍历数据，提取需要查询的ID
            foreach ($paginate['data'] as $value) {
                if ($value['change_type'] == 'test_case' && isset($fieldsMap[$value['change_field']])) {
                    $fieldsMap[$value['change_field']]['new'][] = $value['new_value'];
                    $fieldsMap[$value['change_field']]['old'][] = $value['old_value'];
                }
            }

            // 执行查询并构建映射关系
            foreach ($fieldsMap as $field => &$map) {
                $map['new_values'] = $this->fetchFieldValues($map['model'], $map['new'], $map['column']);
                $map['old_values'] = $this->fetchFieldValues($map['model'], $map['old'], $map['column']);
            }

            // 将结果映射回 paginate 数据
            foreach ($paginate['data'] as &$value) {
                if ($value['change_type'] == 'test_case' && $value['change_field'] == 'desc_type') {
                    $value['old_value'] = $value['old_value'] == 1 ? '文本描述' : '步骤描述';
                    $value['new_value'] = $value['new_value'] == 1 ? '文本描述' : '步骤描述';
                    continue;
                }
                if ($value['change_type'] == 'test_case' && isset($fieldsMap[$value['change_field']])) {
                    $fieldMap = $fieldsMap[$value['change_field']];
                    $value['old_value_text'] = $fieldMap['old_values'][$value['old_value']] ?? $value['old_value'];
                    $value['new_value_text'] = $fieldMap['new_values'][$value['new_value']] ?? $value['new_value'];
                }
            }

            return $paginate;
        }

        // 通用查询函数
        private function fetchFieldValues($model, array $ids, $column)
        {
            return empty($ids) ? [] : make($model)::query()
                ->whereIn('id', $ids)
                ->pluck($column, 'id')
                ->toArray();
        }

        public function getProjectCaseTree($projectId)
        {
            $field = [
                'id',
                'library_id',
                'directory_id',
                'title as name'
            ];
            $caseList = TestCaseModel::query()->where('project_id', $projectId ?: 0)->select($field)->get()->toArray();
            $libraries = TestCaseLibraryModel::query()->where('project_id', $projectId ?: 0)
                ->select('name', 'id')->orderBy('id', 'desc')->get()->toArray();
            $directories = TestDirectoryModel::query()->where('project_id', $projectId ?: 0)
                ->select('title as name', 'id', 'parent_id', 'library_id')->get()->toArray();
            //转换目录成树状结构
            $tree = [];
            foreach ($libraries as $library) {
                $tempDir = array_filter($directories, function ($item) use ($library) {
                    return $item['library_id'] == $library['id'];
                });
                $tempList = array_filter($caseList, function ($item) use ($library) {
                    return $item['library_id'] == $library['id'];
                });

                $library['type'] = 'library';
                $library['key'] = 'library_' . $library['id'];
                $library['children'] = make(Tree::class)->getTreeListV5($tempDir, $tempList); // getTreeListV5中定义category为目录，list为用例

                //计算目录下文件数量
                $library['count'] = array_reduce($library['children'], function ($count, $val) {
                    return $count + ($val['type'] === 'list' ? 1 : 0) + $val['count'];
                }, 0);
                if (!empty($library['children'])) {
                    $tree[] = $library;
                }
            }
            return $tree;
        }

        /**
         * 迭代复制用例和目录 (getTreeListV5中定义category为目录，list为用例)
         * @param array $treeData 目录和用例的树形结构数组
         * @param int $projectId 目标项目ID
         * @param int $libraryId 目标库ID
         * @param int $parentDirId 目标根目录ID，首次调用时传入根目录ID
         */
        public function copyCaseAndDir(array $treeData, int $projectId, int $libraryId, ?int $parentDirId = 0)
        {
            // 使用堆栈模拟递归
            $stack = [];

            // 初始化堆栈，将根目录的数据压入堆栈
            foreach ($treeData as $node) {
                $stack[] = ['node' => $node, 'parentDirId' => $parentDirId];
            }

            // 迭代处理堆栈中的每个节点
            while (!empty($stack)) {
                // 弹出堆栈的最上层节点
                $current = array_pop($stack);
                $node = $current['node'];
                $currentParentDirId = $current['parentDirId'];

                if ($node['type'] === 'category' || $node['type'] === 'directory') {
                    // 如果是目录，复制目录并生成新目录ID
                    $newDirId = $this->copyDirectory($node, $projectId, $libraryId, $currentParentDirId);

                    // 如果有子节点，将子节点压入堆栈，更新其 parentDirId
                    if (!empty($node['children'])) {
                        foreach ($node['children'] as $child) {
                            $stack[] = ['node' => $child, 'parentDirId' => $newDirId];
                        }
                    }
                } elseif ($node['type'] === 'list' || $node['type'] === 'case') {
                    // 如果是用例，复制用例
                    $this->copyCase($node, $projectId, $libraryId, $currentParentDirId);
                }
            }

            return 1;
        }

        /**
         * 复制目录
         * @param array $dirNode 单个目录的节点数据
         * @param int $libraryId 目标库ID
         * @param int|null $parentDirId 目标父目录ID
         * @return int 新目录的ID
         */
        public function copyDirectory(array $dirNode, int $projectId, int $libraryId, ?int $parentDirId)
        {
            // 获取父目录信息
            $dirQuery = $this->testDirectoryModel::query()->find($parentDirId);
            $nowDir = $this->testDirectoryModel::query()->find( $dirNode['id']);

            // 检查是否找到父目录
            if ($dirQuery) {
                // 【复制时路径构建】复制目录时构建path字段，会在父路径后添加-{父目录ID}后缀
                $parentPath = $dirQuery->path != '' ? $dirQuery->path . '-' . $parentDirId : $parentDirId;
            } else {
                $parentPath = '';  // 如果没有父目录，则路径为空
            }

            // 创建新目录
            $newDir = $this->testDirectoryModel->create([
                'library_id'   => $libraryId,
                'parent_id'    => $parentDirId, // 动态生成的父目录ID
                'project_id'   => $projectId,
                'path'         => $parentPath,
                'title'        => $dirNode['title'] ?? $dirNode['name'],
                'description'  => $nowDir->description,
                'created_by'   => getRedmineUserId(),
            ]);
            // 【缺失的逻辑】复制目录时创建后，也需要更新该目录的path字段以包含自身ID
            $newDir->path = $parentPath . '-' . $newDir->id;
            $newDir->save();

            // 返回新目录的ID，用于作为其子目录或用例的 parentDirId
            return $newDir->id;
        }

        /**
         * 复制用例
         * @param array $caseNode 单个用例的节点数据
         * @param int $libraryId 目标库ID
         * @param int|null $directoryId 目标目录ID
         * @return void
         */
        public function copyCase(array $caseNode,int  $projectId, int $libraryId, ?int $directoryId)
        {
            $caseNode['id'] = $caseNode['id'] < 0 ? $caseNode['id'] * -1 : $caseNode['id'];
            $caseQuery = $this->model::query()->with(['attachment', 'step', 'relationIssue'])->find($caseNode['id']);
            // 创建新用例
            $newCase = $this->model->create([
                'library_id'      => $libraryId,
                'directory_id'    => $directoryId,   // 动态生成的目录ID
                'project_id'      => $projectId,
                'title'           => $caseNode['title'] ?? $caseNode['name'], // 用例标题
                'priority'        => $caseQuery->priority,
                'category_id'     => $caseQuery->category_id ?? null,
                'precondition'    => $caseQuery->precondition,
                'desc_type'       => $caseQuery->desc_type,
                'description'     => $caseQuery->description,
                'expected_result' => $caseQuery->expected_result,
                'estimated_hours' => $caseQuery->estimated_hours,
                'created_by'      => getRedmineUserId(), // 当前用户ID
            ]);

            $caseOverview = $caseQuery->toArray() ?? [];

            // 复制附件
            $this->copyAttachment($newCase->id, $caseOverview['attachment']);

            // 复制步骤
            foreach ($caseOverview['step'] as $step) {
                $this->copyStep($newCase->id, $step);
            }

            // 复制关联问题
            foreach ($caseOverview['relation_issue'] as $issue) {
                $this->syncRelationsIssue($newCase->id, $issue);
            }

        }


        /**
         * 复制附件
         * @param int $newCaseId 新用例的ID
         * @param array $attachment 附件数据
         */
        public function copyAttachment(int $newCaseId, array $attachments)
        {
            // 构建需要插入的附件数据
            $data = [];

            foreach ($attachments as $attachment) {
                $data[] = [
                    'container_id'   => $newCaseId,
                    'container_type' => 'TestCase',
                    'filename'       => $attachment['filename'],
                    'disk_filename'  => $attachment['disk_filename'],
                    'filesize'       => $attachment['filesize'],
                    'content_type'   => $attachment['content_type'],
                    'digest'         => $attachment['digest'],
                    'downloads'      => 0,
                    'author_id'      => $attachment['author_id'],
                    'description'    => $attachment['description'],
                    'disk_directory' => $attachment['disk_directory'],
                ];
            }

            // 使用 insert 方法进行批量插入
            DB::connection('tchip_redmine')->table('attachments')->insert($data);
        }

        /**
         * 复制步骤
         * @param int $newCaseId 新用例的ID
         * @param array $step 步骤数据
         */
        public function copyStep(int $newCaseId, array $step)
        {
            $this->testCaseStepModel->create([
                'test_case_id'    => $newCaseId,
                'step_order'      => $step['step_order'],
                'step'            => $step['step'],
                'expected_result' => $step['expected_result'],
            ]);
        }

        /**
         * 复制关联问题
         * @param int $newCaseId 新用例的ID
         * @param array $issue 关联问题数据
         */
        public function syncRelationsIssue(int $newCaseId, array $issue)
        {
            $this->testCaseIssueRelationModel->create([
                'test_case_id' => $newCaseId,
                'issue_id'     => $issue['issue_id'],
            ]);
        }

        public function doBatchCreateCase(array $dataList)
        {
            $result = [];
            foreach ($dataList['title'] as $index => $title) {
                if (empty($title)) {
                    continue;
                }
                // 验证每个用例的数据
                $data = [
                    'library_id' => $dataList['library_id'],
                    'project_id' => $dataList['project_id'],
                    'directory_id' => $dataList['directory_id'][$index],
                    'precondition' => '',
                    'title' => $dataList['title'][$index],
                    'priority' => isset($dataList['priority'][$index]) ? $dataList['priority'][$index] : 3,
                    'created_by' => $dataList['created_by'] ?? getRedmineUserId(),
                ];

                // 数据验证
                $validationErrors = $this->validateCaseData($data);
                if ($validationErrors) {
                    return ['success' => false, 'errors' => $validationErrors];
                }

                // 创建用例
                $createdCase = $this->createCase($data);
                if ($createdCase) {
                    $result[] = $createdCase; // 保存成功的用例信息
                }
            }

            return ['success' => true, 'data' => $result];
        }

        private function validateCaseData(array $data)
        {
            $errors = [];

            // 验证标题不能为空
            if (empty($data['title'])) {
                $errors[] = '用例标题不能为空';
            }

            // 验证 library_id 不能为空
            if (empty($data['library_id'])) {
                $errors[] = '版本 ID 不能为空';
            }

            // 验证 directory_id 不能为空且为数组
            if (empty($data['directory_id'])) {
                $errors[] = '目录 ID 不能为空';
            }

            return $errors ?: null; // 如果没有错误，返回 null
        }


        private function createCase(array $data)
        {
            // 使用模型插入数据
            $case = new $this->model;
            $case->library_id = $data['library_id'];
            $case->project_id = $data['project_id'];
            $case->directory_id = $data['directory_id'];
            $case->precondition = $data['precondition'];
            $case->title = $data['title'];
            $case->priority = $data['priority'];
            $case->created_by = $data['created_by']; // 可选

            // 保存到数据库
            if ($case->save()) {
                return $case; // 返回创建成功的用例
            }

            return null; // 创建失败
        }

        public function exportExcel($filter, $op, $sort = 'id', $order = 'id', $limit = 999999)
        {
            $dIds = [];
            if (!empty($filter['directory_id'])) {
                $dirIdsTemp = $filter['directory_id'];
                foreach ($dirIdsTemp as $dirId) {
                    $dirids[] = $this->getAllChildDirectoryIds($dirId);
                }
                $dIds = array_merge(...$dirids);
            }

            $cIds = [];
            if (!empty($filter['id'])) {
                $cIds = $filter['id'];
            }

            $data = $this->model::query()
                ->whereIn('directory_id', $dIds)
                ->orWhereIn('id', $cIds)
                ->with(['category', 'directory', 'authorText', 'priorityInfo', 'libInfo'])
                ->paginate($limit, ['*'], 'pageNo')
                ->toArray() ?? [];
            $data = $data['data'] ?? [];

            $filename = "测试计划用例.xls";
            $sheetName = 'sheet1';
            $excelWriter = new ExcelWriter($filename, $sheetName);
            $titleData = [
                [
                    '模块',
                    '目录',
                    '标题',
                    '描述',
                    '步骤描述',
                    '文本描述',
                    '优先级',
                    '用例版本',
                    '所属项目',
                    '评估工时',
                    '创建人',
                    '相关事项',
                ]
            ];

            $excelWriter->addData($titleData);

            $excelWriter->setColumnWidth(2, 36); // 设置"目录"列
            $excelWriter->setColumnWidth(3, 100); // 设置"标题"列（第8列）宽度
            $excelWriter->setColumnWidth(4, 36); // 前置条件描述列
            $excelWriter->setColumnWidth(6, 50); // 步骤描述列
            $excelWriter->setColumnWidth(5, 50); // 文本描述列
            $excelWriter->setColumnWidth(12, 50); // 相关事项列

            $caseIds = array_column($data, 'id');
            $casesData= $data;
            $preconditions = array_column($casesData, 'precondition', 'id'); // 前置条件
            $descriptions = array_column($casesData, 'description', 'id'); // 文本类型-文本描述
            $expectedResults = array_column($casesData, 'expected_result', 'id'); // 文本类型-预期结果
            //$estimatedHourList = array_column($casesData, 'estimated_hours', 'id'); // 评估工时
            $projectId = $casesData[0]['project_id'];
            $projectName = ProjectModel::query()->where('id', $projectId)->value('name');

            // 获取所在目录
            $directoryIds = array_unique(array_column($casesData, 'directory_id'));
            $directories = TestDirectoryModel::query()->whereIn('id', $directoryIds)->select(['title', 'id', 'parent_id', 'library_id', 'path'])->get()->toArray();
            $directoryPaths = array_column($directories, 'path', 'id');
            // 提取 `path` 中所有的目录 ID
            $allPathIds = [];
            foreach ($directories as $directory) {
                $pathIds = explode('-', $directory['path']);
                $allPathIds = array_merge($allPathIds, $pathIds);
            }
            // 去重并计算缺失的目录 ID
            $allPathIds = array_unique($allPathIds);
            $missingDirectoryIds = array_diff($allPathIds, array_column($directories, 'id'));
            // 查询缺失的上层目录数据
            if (!empty($missingDirectoryIds)) {
                $parentDirectories = TestDirectoryModel::query()
                    ->whereIn('id', $missingDirectoryIds)
                    ->select(['title', 'id', 'parent_id', 'library_id', 'path'])
                    ->get()
                    ->toArray();
                // 合并上下层目录信息
                $directories = array_merge($directories, $parentDirectories);
            }
            // 确保 $directories 是以 'id' 为键的关联数组
            $directories = array_unique($directories, SORT_REGULAR);
            $directoryMap = array_column($directories, 'title', 'id');
            foreach ($directoryPaths as $k1 => &$v1) {
                $pathIds = explode('-', $v1);
                // 过滤掉无效的 ID 并替换为对应的 title
                $pathTitles = array_map(function ($id) use ($directoryMap) {
                    return $directoryMap[$id] ?? $id; // 找不到时保留原 ID
                }, array_filter($pathIds, function ($id) {
                    return $id != 0; // 排除 ID 为 0 的情况
                }));

                // 将替换后的 title 拼接为 title-title-title 格式
                $v1 = implode('-', $pathTitles);
                if (!empty($v1)) {
                    $v1 .= '-' . $directoryMap[$k1];
                } else {
                    $v1 = $directoryMap[$k1];
                }
            }


            // 创建人
            $createdUserIds = array_column($casesData, 'created_by');
            $authorList = UserModel::query()->whereIn('id', $createdUserIds)
                ->select('id', 'firstname', 'lastname')
                ->selectRaw("CONCAT(firstname, ' ', lastname) as name")
                ->get()
                ->toArray();
            $authorList = array_column($authorList, 'name', 'id');
            $createdByList = array_column($casesData, 'created_by', 'id');
            foreach ($createdByList as &$item) {
                $item = $authorList[$item] ?? '';
            }

            // 相关事项
            $domain = env('BI_FRONTEND_HOST', 'http://bi.t-firefly.com:2101');
            $RelationIssues = TestCaseIssueRelationModel::query()
                ->whereIn('test_case_id', $caseIds)
                ->get(['test_case_id', 'issue_id'])
                ->groupBy('test_case_id')
                ->mapWithKeys(function ($group, $caseId) use ($domain, $projectId) {
                    // 将每个 test_case_id 对应的 URL 构建成数组
                    $urls = $group->pluck('issue_id')->map(function ($issueId) use ($domain, $projectId) {
                        return $domain . '/#/project/detail?project_id=' . $projectId . '&issue_id=' . $issueId;
                    })->toArray();
                    return [$caseId => $urls];
                })
                ->toArray();

            // 步骤描述
            $steps = TestCaseStepModel::query()->whereIn('test_case_id', $caseIds)->get()->groupBy('test_case_id')->toArray();

            $row = [];
            foreach ($data as $k => $v) {
                //获取目录
                $index = ++$k;

                // 将相关事项的 URL 用换行符拼接为字符串
                $relationIssues = $RelationIssues[$v['id']] ?? []; // 获取相关事项数组
                $relationIssuesString = implode("\n", $relationIssues);

                // 步骤描述拼接
                $stepsString = '';
                if (!empty($steps[$v['id']])) {
                    $stepIndex = 1; // 步骤的序号
                    foreach ($steps[$v['id']] as $step) {
                        $stepsString .= $stepIndex . '. ' . $step['step'] . '->' . $step['expected_result'] . "\n";
                        $stepIndex++;
                    }
                    $stepsString = rtrim($stepsString); // 去除末尾多余的换行符
                }

                // 文本描述拼接
                $descriptionsString = '';
                if ( $v['desc_type'] == 1 && !empty($descriptions[$v['id']])) {
                    $descriptionsString = $descriptions[$v['id']] . '->' . $expectedResults[$v['id']];
                }

                // 前置条件 去除html标签
                $html = $preconditions[$v['id']];
                // 替换 <p> 标签为换行符
                $html = preg_replace('/<p.*?>/', "\n", $html);
                // 去掉最开始可能产生的换行符
                $html = ltrim($html, "\n");
                // 替换 <br> 标签为换行符
                $html = preg_replace('/<br\s*\/?>/', "\n", $html);
                // 去除剩余的 HTML 标签，保留换行符
                $preconditionText = strip_tags($html);

                $row = [[
                    $v['category']['name'] ?? '', // 模块
                    $directoryPaths[$v['directory_id']] ?? '', // 目录
                    $v['title'], // 标题
                    //$preconditions[$v['id']], // 描述
                    $preconditionText, // 描述（前置条件）
                    $stepsString, // 步骤描述
                    $descriptionsString, // 文本描述
                    //$v['desc_type'] == 0 ? '' : $v['priority_info']['name'] ?? '', // 优先级 文本描述或是步骤描述二选一，0-步骤描述，1-文本步骤描述
                    $v['priority_info']['name'] ?? '',
                    $v['lib_info']['name'], // 用例版本
                    $projectName, // 所属项目
                    $v['estimated_hours'], // 评估工时
                    $v['author_text']['name']?? '', // 创建人
                    $relationIssuesString, // 相关事项
                ]];
                $excelWriter->addData($row);
            }
            $result =  $excelWriter->download();
            $excelWriter->close();
            return $result;
        }

        public function excelImport($file, $params)
        {
            $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReaderForFile($file->getRealPath());
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($file->getRealPath());
            $worksheet = $spreadsheet->getActiveSheet();

            $errors = []; // 用于存储错误信息

            $rows = [];
            $step = [];
            $relationIssue = [];
            $start = 2; // 数据从第2行开始，跳过标题行
            $highestRow = $worksheet->getHighestRow();
            $highestColumn = $worksheet->getHighestColumn();
            $time = time();

            // Step 1: 建立标题与列的映射
            $columnMap = [];
            for ($col = 'A'; $col <= $highestColumn; $col++) {
                $header = $worksheet->getCell("{$col}1")->getValue(); // 获取首行标题
                if ($header) {
                    $columnMap[$header] = $col;
                }
            }

            // Step 1.1: 获取模块映射
            $categoryMap = make(\App\Model\Redmine\IssueCategoriesModel::class)::query()->where('project_id', $params['project_id'])->get()->toArray();
            $categoryMap = array_column($categoryMap, 'id', 'name');
            // Step 1.2: 获取目录映射或创建目录 20241224：一律视为创建，解决同名目录匹配错误的父目录问题($directoryMap设置为空[]) 20250109 需要归类到同名目录之下(directoryMap以转化后的path路径名为键）
            $directoryMap = [];
            $directoryMap = $this->testDirectoryModel::query()
                ->where('project_id', $params['project_id'])
                ->where('library_id', $params['library_id'])
                ->get()
                ->toArray();

            //$paths = array_column($directoryMap, 'path');
            //$allPathIds = [];
            //foreach ($paths as $path) {
            //    preg_match_all('/\d+/', $path, $matches);
            //    $allPathIds = array_merge($allPathIds, $matches[0]);
            //}
            //
            //// 将结果转换为整数数组
            //$allPathIds = array_map('intval', $allPathIds);
            //$allPathIds = array_values(array_unique($allPathIds)); // 去重

            $pathMap = $this->testDirectoryModel::query()
                //->whereIn('id', $allPathIds)
                ->where('project_id', $params['project_id'])
                ->where('library_id', $params['library_id'])
                ->pluck('title', 'id')
                ->toArray();

            // Step 1: 计算 path 中分隔符数量，并根据数量对 $directoryMap 进行排序;以及path字段值处理
            usort($directoryMap, function($a, $b) {
                // 计算分隔符数量
                $aCount = substr_count($a['path'], '-');
                $bCount = substr_count($b['path'], '-');

                return $aCount - $bCount; // 按照 '-' 数量升序排序
            });

            foreach ($directoryMap as &$item) {
                // 如果 path 是 '0'，直接置为空
                if ($item['path'] === '0') {
                    $item['path'] = null;
                    continue;
                }

                // 如果 path 包含 '0-'，去除 '0-'
                if (strpos($item['path'], '0-') === 0) {
                    $item['path'] = substr($item['path'], 2); // 去掉开头的 '0-'
                }

                // 末尾将其本身添加上方便匹配计算
                $item['path'] =  $item['path'] . '-' . $item['id'];

                // 提取 path 中的数字并替换为对应的 title
                preg_match_all('/\d+/', $item['path'], $matches);
                $ids = $matches[0];
                $titles = array_map(function ($id) use ($pathMap) {
                    return $pathMap[$id] ?? $id; // 如果未匹配到 title，保留原始数字
                }, $ids);

                $item['path'] = implode('-', $titles); // 用 '-' 重新拼接
            }


            // Step 2: 创建新的数组，并处理去重逻辑
            $processedMap = [];  // 存放去重后的数据

            foreach ($directoryMap as $item2) {
                $path = $item2['path'];
                $parentId = $item2['parent_id'];
                $sortOrder = $item2['sort_order'];

                // Step 2.1: 如果相同 path 的元素已经存在
                if (isset($processedMap[$path])) {
                    $existingItem = $processedMap[$path];

                    // Step 2.2: 如果 parent_id 相同，保留 sort_order 最大的项；sort_order 相同则保留 id 小的项
                    if ($existingItem['parent_id'] === $parentId) {
                        if ($sortOrder > $existingItem['sort_order']) {
                            $processedMap[$path] = $item2;  // 保留 sort_order 更大的项
                        } elseif ($sortOrder === $existingItem['sort_order']) {
                            // 如果 sort_order 相同，保留 id 更小的项
                            if ($item2['id'] < $existingItem['id']) {
                                $processedMap[$path] = $item2;  // 保留 id 更小的项
                            }
                        }
                    } else {
                        // Step 2.3: 如果 parent_id 不同，查看是否已经有 parent_id 等于当前 item 的 id
                        $parentIdExists = false;
                        foreach ($processedMap as $existing) {
                            if ($existing['id'] === $parentId) {
                                $parentIdExists = true;
                                break;
                            }
                        }
                        // 如果存在 parent_id 等于当前项的 id，则保留当前项
                        if ($parentIdExists) {
                            $processedMap[$path] = $item2;
                        }
                    }
                } else {
                    // Step 2.4: 如果该 path 尚未处理过，直接加入
                    $processedMap[$path] = $item2;
                }
            }

            // Step 3: 将去重后的数组转换为索引数组
            $directoryMap = array_values($processedMap);

            // Step 4: 使用 array_column 按 path 键化数组
            $directoryMap = array_column($directoryMap, null, 'path');

            $emptyMapForCreate = [];

            // Step 1.3: 获取项目映射
            $projectMap = make(\App\Model\Redmine\ProjectModel::class)::query()->where('id', $params['project_id'])->get()->toArray();
            $projectMap = array_column($projectMap, 'id', 'name');
            // Step 1.4: 获取用户映射
            $userMap = make(\App\Model\Redmine\UserModel::class)::query()->get()->toArray();
            $userMap = array_column($userMap, 'id', 'name');
            // Step 1.5: 获取版本映射
            $libraryMap = $this->testCaseLibraryModel::query()->where('id', $params['library_id'])->get()->toArray();
            $libraryMap = array_column($libraryMap, 'id', 'name');
            // Step 1.6: 获取优先级映射
            $priorityMap = make(\App\Model\Redmine\EnumerationModel::class)::query()->get()->toArray();
            $priorityMap = array_column($priorityMap, 'id', 'name');


            // Step 2: 使用映射读取数据
            // 用来存储错误信息和临时行数据
            $errors = '';
            $tempRows = []; // 用于存储校验通过的数据

            // （1）：校验所有行数据
            for ($row = $start; $row <= $highestRow; $row++) {
                $categoryName = isset($columnMap['模块']) ? $worksheet->getCell($columnMap['模块'] . $row)->getValue() : null;
                $dirName = isset($columnMap['目录']) ? $worksheet->getCell($columnMap['目录'] . $row)->getValue() : 0;
                $title = isset($columnMap['标题']) ? $worksheet->getCell($columnMap['标题'] . $row)->getValue() : '';
                $priority = isset($columnMap['优先级']) ? $worksheet->getCell($columnMap['优先级'] . $row)->getValue() : '';
                $precondition = isset($columnMap['描述']) ? $worksheet->getCell($columnMap['描述'] . $row)->getValue() : '';
                $estimatedHours = isset($columnMap['评估工时']) ? $worksheet->getCell($columnMap['评估工时'] . $row)->getValue() : 0.00;
                $description = isset($columnMap['文本描述']) ? $worksheet->getCell($columnMap['文本描述'] . $row)->getValue() : '';
                $stepDesc = isset($columnMap['步骤描述']) ? $worksheet->getCell($columnMap['步骤描述'] . $row)->getValue() : '';
                $relationLink = isset($columnMap['相关事项']) ? $worksheet->getCell($columnMap['相关事项'] . $row)->getValue() : '';

                // 校验标题是否为空
                if ( !$title || empty($title) || trim($title) == '') {
                    $errors = '第' . $row . ' 行用例标题为空! 无法进行导入';
                    break;
                }

                // 校验完成后，将原始数据存储到临时数组
                $tempRows[] = [
                    'categoryName' => $categoryName,
                    'dirName' => $dirName,
                    'title' => $title,
                    'priority' => $priority,
                    'precondition' => $precondition,
                    'estimatedHours' => $estimatedHours,
                    'description' => $description,
                    'stepDesc' => $stepDesc,
                    'relationLink' => $relationLink,
                ];
            }

            // 如果校验有错误，提前返回错误信息
            if (!empty($errors)) {
                return ['status' => 'error', 'msg' => $errors];
            }

            $tempRows = array_reverse($tempRows); // 将数组逆序 20241211 由于默认按照id降序排序，需要将底下的用例先创建，使最终获取到的数组顺序与excel打开查看一致
            // （2）：处理数据
            foreach ($tempRows as $tempRow) {
                $desc_type = $tempRow['description'] ? 1 : 0;
                list($descText, $expectedResultText) = explode('->', $tempRow['description']) + ['', ''];
                $estimatedHours = is_numeric($tempRow['estimatedHours']) ? (float)$tempRow['estimatedHours'] : 0.00;
                $formattedPrecondition = $tempRow['precondition']
                    ? "<p>" . str_replace(["\r\n", "\n", "\r"], '<br>', htmlspecialchars($tempRow['precondition'])) . "</p>"
                    : '';

                // 数据映射及函数调用
                $data = [
                    'category_id' => $this->getCategoryId(trim($tempRow['categoryName']), $categoryMap),
                    'directory_id' => $this->getDirectoryId($tempRow['dirName'], $directoryMap,
                        $emptyMapForCreate, $pathMap, [
                        'project_id' => $params['project_id'],
                        'library_id' => $params['library_id'],
                    ]),
                    'title' => $tempRow['title'] ?: '',
                    'priority' => $this->parsePriority(trim($tempRow['priority']), $priorityMap),
                    'precondition' => $formattedPrecondition,
                    'project_id' => $params['project_id'],
                    'library_id' => $params['library_id'],
                    'estimated_hours' => $estimatedHours,
                    'created_by' => getRedmineUserId(),
                    'desc_type' => $desc_type,
                    'description' => trim($descText),
                    'expected_result' => trim($expectedResultText),
                    'created_at' => date('Y-m-d H:i:s', $time),
                    'updated_at' => date('Y-m-d H:i:s', $time),
                ];

                // 处理步骤描述和关联问题
                $stepDesc = $this->getSteps($tempRow['stepDesc']);
                $relationIds = $this->getRelationId($tempRow['relationLink']);

                $rows[] = $data;
                $step[] = $stepDesc;
                $relationIssue[] = $relationIds;
            }

            // 执行批量插入
            if (!empty($rows)) {
                // 先插入第一条记录获取起始ID
                $firstId = $this->model->insertGetId($rows[0]);

                // 处理插入后的所有行
                $insertedIds = range($firstId, $firstId + count($rows) - 1);

                // 批量插入剩余行
                if (count($rows) > 1) {
                    $this->model->insert(array_slice($rows, 1));
                }

                // 遍历插入的ID进行关联
                foreach ($insertedIds as $index => $id) {
                    // $id 是当前插入行的ID，与 $step[$index] 和 $relationIssue[$index] 对应
                    // 将 expectedResult 和 relationIssue 进行相应的处理
                    $this->processStepAndRelation($id, $step[$index], $relationIssue[$index]);
                }
            }

            return ['status' => 'success', 'msg' => '导入成功'];
        }

        /**
         * 关联 `expectedResult` 和 `relationIssue` 到具体的条目上
         */
        protected function processStepAndRelation($id, $expectedResult, $relationIds)
        {
            // 处理 expectedResult
            foreach ($expectedResult as $step) {
                $step['test_case_id'] = $id;
                $this->doEditStep(-1, $step, true);
            }

            // 处理 relationIssue，遍历插入关系
            foreach ($relationIds as $issueId) {
                $value = ['issue_id' => $issueId, 'test_case_id' => $id,];

                $this->doEditIssueRelation(-1, $value, true);
            }
        }

        /**
         * 将 expectedResult 字段内容序列化为 test_case_step 表的数据格式
         *
         * @param string $expectedResult 原始的步骤和预期结果字符串
         * @return array
         */
        protected function getSteps($expectedResult)
        {
            $steps = [];
            $stepLines = explode("\n", $expectedResult); // 按行分割步骤

            foreach ($stepLines as $index => $line) {
                // 检查是否包含 '->' 分隔符
                if (strpos($line, '->') !== false) {
                    list($step, $result) = explode('->', $line, 2);

                    // 使用正则表达式去除 step 开头的序号字符（如"1."、"2."等）
                    $step = preg_replace('/^\d+\.\s*/', '', trim($step));

                    $steps[] = [
                        'step_order' => $index + 1,
                        'step' => $step,
                        'expected_result' => trim($result),
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                }
            }
            return $steps;
        }


        /**
         * 提取关联事项id
         */
        protected function getRelationId($relationLink)
        {
            // 使用正则表达式匹配所有 issue_id 的值
            preg_match_all('/issue_id=(\d+)/', $relationLink, $matches);

            // 返回所有匹配的 issue_id 值
            return $matches[1];
        }


        /**
         * 将优先级从字符串转换为数字。
         */
        protected function parsePriority($priority, $priorityMap)
        {
            if (is_string($priority) && strlen($priority) > 0) {
                // 仅将首字符转换为大写，其他部分保持不变
                $priority = strtoupper(substr($priority, 0, 1)) . substr($priority, 1);
            }
            return $priorityMap[$priority] ?? 3;
        }

        /**
         * 获取category_id，如果不存在则返回null。
         */
        protected function getCategoryId($categoryName, $categoryMap)
        {
            return $categoryMap[$categoryName] ?? null;
        }

        /**
         * 获取directory_id，如果不存在则创建。(将拆分为title作为键单个匹配或新建)
         */
        protected function getDirectoryId_old($directoryName, &$directoryMap, $params)
        {
            // 将目录路径按 `-` 或 `--` 分割成数组
            $directories = preg_split('/\s*-\s*/', $directoryName);
            $parentId = 0; // 当前层级的父级ID
            $currentPath = ''; // 用于构建完整的路径标识

            foreach ($directories as $directory) {
                // 构建当前层级的完整路径标识
                $currentPath = $currentPath ? "{$currentPath}-{$directory}" : $directory;

                // 检查当前路径是否已经存在
                if (isset($directoryMap[$currentPath])) {
                    // 路径已存在，获取当前目录的ID作为下一级的parent_id
                    $parentId = $directoryMap[$currentPath]['id'];
                } else {
                    // 不存在则创建新目录
                    $params['title'] = trim($directory) === ''
                        ? '其他_' . date('YmdHi') // 格式：202412111101
                        : $directory;
                    $params['parent_id'] = $parentId; // 设置父级ID

                    // 调用创建目录并获取新目录的ID
                    $directoryId = $this->doEditDir(-1, $params);

                    // 存储新目录的ID和路径到 `directoryMap`
                    $directoryMap[$currentPath] = [
                        'id' => $directoryId,
                    ];

                    // 更新 parentId 为当前目录ID，供下一级使用
                    $parentId = $directoryId;
                }
            }

            // 返回最终目录的ID
            return $parentId;
        }

        /**
         * 获取directory_id，如果不存在则创建。(依据如'首页-任务中心-上传备份-按钮测试'作为键匹配)
         */
        protected function getDirectoryId($directoryPathName, &$directoryMap, &$emptyMap, $pathMap, $params)
        {
            if (empty($directoryMap[$directoryPathName])) {
                // 逐级验证路径，寻找有效的前缀链条
                $validPrefix = $this->findValidPathPrefix($directoryPathName, $directoryMap);
                
                if ($validPrefix) {
                    // 基于有效前缀逐级创建剩余路径
                    $directoryMap[$directoryPathName]['id'] = $this->buildRemainingPath($directoryPathName, $validPrefix, $directoryMap, $params);
                } else {
                    // 如果没有找到有效的前缀链条，从头开始层层构建
                    $directoryMap[$directoryPathName] = [];
                    $directoryMap[$directoryPathName]['id'] = $this->getDirectoryId_old($directoryPathName, $emptyMap, $params);
                }
            }

            return $directoryMap[$directoryPathName]['id'];
        }

        /**
         * 查找有效的路径前缀，确保前缀链条完整存在
         */
        protected function findValidPathPrefix($directoryPathName, $directoryMap)
        {
            $segments = preg_split('/\s*-\s*/', $directoryPathName);
            $validPrefix = '';
            $currentPath = '';
            
            // 逐级检查路径段
            foreach ($segments as $index => $segment) {
                $currentPath = $currentPath ? "{$currentPath}-{$segment}" : $segment;
                
                // 检查当前路径是否存在于directoryMap中
                if (isset($directoryMap[$currentPath]) && !empty($directoryMap[$currentPath]['id'])) {
                    $validPrefix = $currentPath;
                } else {
                    // 当前路径不存在，停止检查
                    break;
                }
            }
            
            return $validPrefix;
        }

        /**
         * 从有效前缀开始逐级创建剩余路径
         */
        protected function buildRemainingPath($fullPath, $validPrefix, &$directoryMap, $params)
        {
            $segments = preg_split('/\s*-\s*/', $fullPath);
            $validPrefixSegments = preg_split('/\s*-\s*/', $validPrefix);
            
            // 获取需要创建的剩余段
            $remainingSegments = array_slice($segments, count($validPrefixSegments));
            
            $currentPath = $validPrefix;
            $parentId = $directoryMap[$validPrefix]['id'];
            
            // 逐级创建剩余目录
            foreach ($remainingSegments as $segment) {
                $newPath = "{$currentPath}-{$segment}";
                
                // 检查是否已存在
                if (!isset($directoryMap[$newPath]) || empty($directoryMap[$newPath]['id'])) {
                    $params['title'] = $segment;
                    $params['parent_id'] = $parentId;
                    $newDirId = $this->doEditDir(-1, $params);
                    $directoryMap[$newPath]['id'] = $newDirId;
                    $parentId = $newDirId;
                } else {
                    $parentId = $directoryMap[$newPath]['id'];
                }
                
                $currentPath = $newPath;
            }
            
            return $parentId;
        }

        public function doBatchCopyCase($rows, $params) {
            DB::connection('tchip_redmine')->beginTransaction();
            $isCopyDirectoryStructure = false;
            if (!empty($params['is_copy_directory_structure'])) {
                $isCopyDirectoryStructure = $params['is_copy_directory_structure'];
            }
            try {
                $dirIds = [];
                $directlySelectedCaseIds = []; // 直接选中的用例
                foreach ($rows as $row) {
                    if ($row['type'] == 'directory') {
                        $dirIds = array_merge($dirIds, $this->getAllChildDirectoryIds($row['id']));
                    } else if ($row['type'] == 'case') {
                        $directlySelectedCaseIds[] = $row['id'];
                    }
                }
                $directoryData = TestDirectoryModel::query()->whereIn('id', $dirIds)->get()->toArray();
                foreach ($directoryData as &$dir) {
                    $dir['type'] = 'directory';
                }
                $allCases = TestCaseModel::query()
                    ->whereIn('directory_id', $dirIds)
                    ->orWhereIn('id', $directlySelectedCaseIds)
                    ->get()
                    ->toArray();
                foreach ($allCases as &$row2) {
                    $row2['parent_id'] = $row2['directory_id'];
                    $row2['type'] = 'case';
                }

                $allCases = array_map(function ($case) {
                    $case['parent_id'] = $case['directory_id']; // 将 directory_id 的值赋给 parent_id
                    $case['case_id'] = $case['id'];
                    $case['id'] = $case['id'] * -1; // 1防止参与树形结构构造
                    return $case;
                }, $allCases);

                $mixedRows = array_merge($directoryData, $allCases);
                $parentIds = array_column($mixedRows, 'parent_id');
                $allIds = array_column($mixedRows, 'id');
                $allIds = array_filter($allIds, function ($id) {
                    return $id >= 0;
                });

                // 从 parentIds 中取出不在 allIds 中的部分 （即为当前最顶级的所有目录的parent_id，将会指定到要复制到的目录id下）
                $topParentIds = array_diff($parentIds, $allIds);
                $topParentId = array_unique($topParentIds);

                $mixedRows = array_map(function ($row) use ($topParentId, $params){
                    if (in_array($row['parent_id'], $topParentId)) {
                        $row['parent_id'] = $params['directory_id'];
                    }
                    return $row;
                }, $mixedRows);

                $mixedRows = $this->getTreeListV2_1($mixedRows, $params['directory_id'], 'parent_id', 'id');

                //return $mixedRows;
                // 1.复制用例与目录结构
                if ($isCopyDirectoryStructure) {
                    $this->copyCaseAndDir($mixedRows, $params['project_id'], $params['library_id'], $params['directory_id']);
                }
                // 2.仅复制用例
                else {
                    foreach ($allCases as $case) {
                        $this->copyCase($case, $params['project_id'], $params['library_id'], $params['directory_id']);
                    }
                }

                DB::connection('tchip_redmine')->commit();
            }  catch (AppException $e) {
                DB::connection('tchip_redmine')->rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
            return 1;
        }

        public function doBatchDeleteCase($rows) {
            DB::connection('tchip_redmine')->beginTransaction();
            try {
                $dirIds = [];
                $caseIds = [];

                // 解析 rows 数据，将目录和用例分开
                foreach ($rows as $row) {
                    if ($row['type'] == 'directory') {
                        $dirIds = array_merge($dirIds, $this->getAllChildDirectoryIds($row['id'])); // 获取所有子目录
                        $dirIds[] = $row['id']; // 包括当前目录
                    } else if ($row['type'] == 'case') {
                        $caseIds[] = $row['id']; // 直接选中的用例
                    }
                }

                // 删除目录及其子目录
                if (!empty($dirIds)) {
                    TestDirectoryModel::query()->whereIn('id', $dirIds)->delete(); // 删除目录
                    TestCaseModel::query()->whereIn('directory_id', $dirIds)->delete(); // 删除目录中的用例
                }

                // 删除单独选中的用例
                if (!empty($caseIds)) {
                    TestCaseModel::query()->whereIn('id', $caseIds)->delete();
                }

                DB::connection('tchip_redmine')->commit();
                return $caseIds; // 返回成功
            } catch (Exception $e) {
                DB::connection('tchip_redmine')->rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
        }



        /**
         * 重新排序函数
         *
         * 用于对节点进行重新排序操作，包括拖动到平级位置或成为其他节点的子级。
         *
         * @param int|string $dragNodeId   拖动的节点 ID。
         * @param array $targetNode        目标节点信息（包含节点 ID 和其他必要属性）。
         * @param bool $dropToGap          是否拖动到平级位置：
         *                                 - true：拖动到平级。
         *                                 - false：拖动成为子级。
         * @param int $dropPosition        拖动位置的相对位置标志（目前虚拟滚动下仅首行元素能确定以下标准）：
         *                                 - 1：拖动到目标节点的下方，平级。
         *                                 - 0：拖动成为目标节点的子级。
         *                                 - -1：拖动到目标节点的上方，平级。
         *
         * @throws Exception               如果拖动操作或排序操作失败，将抛出异常。
         */
        public function rearrangement($dragNodeId, $targetNode, $dropToGap, $dropPosition)
        {
            DB::connection('tchip_redmine')->beginTransaction();
            try {
                $dragNodeIsCase = $dragNodeId < 0; // 用例id传进时为负数
                $targetNodeIsCase = $targetNode['id'] < 0;
                $dragNodeId = abs($dragNodeId);
                $targetNodeId = abs($targetNode['id']);
                $targetDirectoryId = $targetNodeIsCase ? $targetNode['directory_id'] : $targetNode['id'];

                ////////////////////////////////////////////////
                // 1.用例之间的移动 || 用例移动到目录之下 总之即用例拖动
                ////////////////////////////////////////////////
                if ($dragNodeIsCase) {
                    $dragNode = $this->model::query()->where('id', $dragNodeId)->first()->toArray();
                    // 判断是否跨目录
                    $isCrossDirectory = ($dragNode['directory_id'] != $targetDirectoryId);

                    if (!$targetNodeIsCase && $isCrossDirectory && $dropPosition != 0 && $dropToGap) {
                        // 并非要成为子级而是拖动到其下
                        $targetDirectoryId = $targetNode['parent_id'];
                        $isCrossDirectory = $targetNode['parent_id'] != $dragNode['directory_id']; // 需要再次判断是否跨目录
                    }

                    $allCaseAtSameDirectory = $this->getList(['directory_id' => $targetDirectoryId,
                        'library_id' => $targetNode['library_id']],
                        ['directory_id' => 'IN'], 'id', 'desc', 99999)->toArray();
                    $allCases = $allCaseAtSameDirectory['data'];

                    // 找到目标节点的位置下标
                    $targetIndex = 0;
                    if ($targetNodeIsCase) {
                        foreach ($allCases as $index => $case) {
                            if ($case['id'] == $targetNodeId) {
                                $targetIndex = $index;
                                break;
                            }
                        }
                        if ($targetIndex === null) {
                            throw new AppException(StatusCode::ERR_SERVER, '目标节点未找到');
                        }
                    }

                    if ($isCrossDirectory) {
                        // 跨目录：在目标节点之后插入拖拽节点dragNode
                        array_splice($allCases, $targetNodeIsCase ? $targetIndex + 1 : 0, 0, [['id' => $dragNodeId, 'sort_order' => 0]]);
                        // 跨目录的处理 需要跨目录的修改记录
                        $this->doEdit($dragNodeId, ['directory_id' => $targetDirectoryId]);
                    } else {
                        // 非跨目录：先删除原位置节点，再在目标位置插入
                        $dragIndex = null;
                        foreach ($allCases as $index => $case) {
                            if ($case['id'] == $dragNodeId) {
                                $dragIndex = $index;
                                break;
                            }
                        }
                        if ($dragIndex === null) {
                            throw new AppException(StatusCode::ERR_SERVER, '拖拽节点未找到');
                        }
                        // 先删除原位置
                        $dragNode = $allCases[$dragIndex];
                        array_splice($allCases, $dragIndex, 1);
                        // 在目标位置之后插入
                        array_splice($allCases, $targetNodeIsCase ? $targetIndex + 1 : 0, 0, [$dragNode]);
                    }

                    $totalCases = count($allCases);
                    // 重新定义所有用例的 sort_order
                    foreach ($allCases as $index => $case) {
                        $newSortOrder = $totalCases - $index; // 根据访问顺序反向写入 sort_order 因为使用的是desc排序规则
                        DB::connection('tchip_redmine')->table('test_case')
                            ->where('id', $case['id'])
                            ->update([
                                'sort_order' => $newSortOrder,
                                'updated_at' => date('Y-m-d H:i:s'),
                            ]);
                    }
                }

                ////////////////////////////////////////////////
                // 2.目录拖动
                ////////////////////////////////////////////////
                if (!$dragNodeIsCase) {
                    $dragDirNode = $this->testDirectoryModel::query()->where('id', $dragNodeId)->first()->toArray();
                    // 判断是否跨目录操作
                    $isSameParentDirectory = ($dragDirNode['parent_id'] == $targetNode['parent_id']);
                    // 目录是否跨父目录，或者拖动到目标节点的子节点
                    $isCrossDirectoryForDir = !$isSameParentDirectory || !$dropToGap;

                    $allDirAtSameDirectory = $this->getDirList([
                        'parent_id' => $dropToGap ? $targetNode['parent_id'] : $targetDirectoryId, // $dropToGap true成为同级，否则成为子级
                        'library_id' => $dragDirNode['library_id'],
                        'id' => -3,
                        'project_id' => $dragDirNode['project_id']],
                        [], 'id', 'desc', 99999);
                    // 剔除 id 为负的元素
                    $allDirectory = array_filter($allDirAtSameDirectory['data'], function ($directory) {
                        return $directory['id'] >= 0;
                    });

                    // 找到目标节点的位置下标 目标节点非目录不进入判断内查询下标直接插入到首位否则查找下标
                    $targetDirIndex = -1; // 需要设置为-1， 处理跨目录（$isSameParentDirectory为true但实际上添加为对应元素（已展开）的子级目录，实际上是同级不跨）的情况
                    if (!$targetNodeIsCase) {
                        foreach ($allDirectory as $index => $case) {
                            if ($case['id'] == $targetNodeId) {
                                $targetDirIndex = $index;
                                break;
                            }
                        }
                        if ($targetDirIndex === null && $dropToGap) {
                            throw new AppException(StatusCode::ERR_SERVER, '目标节点未找到');
                        }
                    }

                    // 是否为跨目录
                    if ($isCrossDirectoryForDir) {
                        // 跨目录：在目标节点之后插入拖拽节点dragNode 首个目录元素$dropPosition为-1代表在其上，0为子级，1为平级
                        array_splice($allDirectory, !$targetNodeIsCase && $dropPosition !== -1 ?
                            $targetDirIndex + 1
                            : 0, 0, [['id' => $dragNodeId, 'sort_order' => 0]]);
                        // 跨目录的处理 需要跨目录的修改记录
                        $this->doEditDir($dragNodeId, ['parent_id' => !$dropToGap ? $targetDirectoryId : $targetNode['parent_id']]); // 平级选targetNode['parent_id']
                    } else {
                        // 非跨目录：先删除原位置节点，再在目标位置插入
                        $dragIndex = null;
                        foreach ($allDirectory as $index => $dir) {
                            if ($dir['id'] == $dragNodeId) {
                                $dragIndex = $index;
                                break;
                            }
                        }
                        if ($dragIndex === null) {
                            throw new AppException(StatusCode::ERR_SERVER, '拖拽节点未找到');
                        }
                        // 先删除原位置
                        $dragNode = $allDirectory[$dragIndex];
                        array_splice($allDirectory, $dragIndex, 1);
                        // 在目标位置之后插入
                        array_splice($allDirectory, $dropPosition !== -1 ? $targetDirIndex + 1 : 0, 0, [$dragNode]);
                    }
                    $totalDirCount = count($allDirectory);
                    // 构造批量更新的参数 重新定义所有目录的 sort_order
                    $updateData = [];
                    foreach ($allDirectory as $index => $dir) {
                        $newSortOrder = $totalDirCount - $index; // 根据访问顺序反向写入 sort_order
                        $updateData[] = [
                            'id' => $dir['id'],
                            'sort_order' => $newSortOrder,
                            //'updated_at' => date('Y-m-d H:i:s'),
                        ];
                    }
                    if (empty($updateData)) {
                        throw new AppException(StatusCode::ERR_SERVER, '没有有效的目录需要更新');
                    }
                    // 使用 CASE 表达式单次更新所有记录，避免多次循环更新
                    $sql = "UPDATE test_directory SET sort_order = CASE ";
                    foreach ($updateData as $dirItem) {
                        $sql .= "WHEN id = {$dirItem['id']} THEN {$dirItem['sort_order']} ";
                    }
                    $sql .= "END, updated_at = '" . date('Y-m-d H:i:s') . "' ";
                    // 添加 WHERE 子句
                    $ids = array_column($updateData, 'id');
                    $sql .= "WHERE id IN (" . implode(',', $ids) . ")";
                    DB::connection('tchip_redmine')->statement($sql);

                }
                DB::connection('tchip_redmine')->commit();
            } catch (AppException $e) {
                DB::connection('tchip_redmine')->rollBack();
                throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
            }
            return 1;
        }
    }