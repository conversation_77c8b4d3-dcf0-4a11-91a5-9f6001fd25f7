<?php

namespace App\Core\Services\TchipSale;

use App\Constants\ProductionCode;
use App\Constants\DataBaseCode;
use App\Core\Services\BusinessService;
use App\Model\TchipSale\OutStockQrcodeModel;
use Hyperf\Di\Annotation\Inject;
use Hyperf\DbConnection\Db;
use App\Core\Utils\Log;

class OutStockQrcodeService extends BusinessService
{
    /**
     * @Inject
     * @var OutStockQrcodeModel
     */
    protected $model;

    public function getOverView($id)
    {
        $info =  $this->model::query()->with('saleData')->find($id);
        if($info){
            $info = $info->toArray();
            $info['sale_data'] = array_filter($info['sale_data'],function($item){
                return in_array($item['platform_id'],[23,25]);
            });
            foreach ($info['sale_data'] as &$item){
                $item['sn_data'] = [];
            }

            $info['qrcode_id'] = $info['id'];
            unset($info['id']);
            return $info;
        }
        return [];
    }

    /**
     * 获取列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @param int $limit
     * @return mixed
     */
    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        $keywords = !empty($filter['keywords']) ? $filter['keywords'] : '';
        unset($filter['keywords']);
        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        if ($keywords) {
            $query = $query->where(function ($query) use ($keywords) {
                $query->where('out_stock_no', 'like', '%' . $keywords . '%')
                    ->orWhere('order_no', 'like', '%' . $keywords . '%')
                    ->orWhere('client_name', 'like', '%' . $keywords . '%')
                    ->orWhere('out_stock_qrcode_sale.product_name', 'like', '%' . $keywords . '%')
                    ->orWhere('out_stock_qrcode_sale.product_code', 'like', '%' . $keywords . '%');
            });

        }
        $query->join('out_stock_qrcode_sale',function($join){
            $join->on('out_stock_qrcode_sale.qrcode_id', '=', 'out_stock_qrcode.id')
                ->whereIn('out_stock_qrcode_sale.platform_id', ProductionCode::SHIPMENT_PLATFORM_ARR);
        });
        $query->select('out_stock_qrcode.*');
        $data =  $query->with('saleData')->groupBy('out_stock_qrcode.id')->orderBy($sort, $order)->paginate($limit)->toArray();
        foreach ($data['data'] as &$item){
            !$item['sale_data'] && $item['sale_data'] = [];
            //汇总产品，去重
            $productData =[];
            foreach ($item['sale_data'] as $sale){
                in_array($sale['platform_id'],ProductionCode::SHIPMENT_PLATFORM_ARR) && $productData[$sale['product_id']] = [
                    'name' => $sale['product_name'],
                    'code' => $sale['product_code'],
                ];
            }
            $item['product_name'] = implode(',',array_column($productData,'name'));
            $item['product_code'] = implode(',',array_column($productData,'code'));
        }
        return $data;
    }

    public function fixQrCode()
    {
        $db = Db::connection(DataBaseCode::TCHIP_SALE);
        $qrcodeSales = $db->table('out_stock_qrcode_sale')
            ->whereNotNull('sale_id')
            ->where('sale_id', 'regexp', '^(Kypt_|Pj_)')
            ->get();

        // 提前批量查询销售记录
        $kyptIds = [];
        $pjIds = [];
        foreach ($qrcodeSales as $sale) {
            [$prefix, $saleIdNum] = explode('_', $sale->sale_id, 2);
            if (strtolower($prefix) === 'kypt') {
                $kyptIds[] = $saleIdNum;
            } elseif (strtolower($prefix) === 'pj') {
                $pjIds[] = $saleIdNum;
            }
        }

        // 批量查询销售记录
        $kyptSales = !empty($kyptIds) ? $db->table('client_sale_kypt_table')
            ->select('id', 'size_id as prod_id', 'num')
            ->whereIn('id', $kyptIds)
            ->get()
            ->keyBy('id') : collect();

        $pjSales = !empty($pjIds) ? $db->table('client_sale_pj_table')
            ->select('id', 'prod_id', 'num')
            ->whereIn('id', $pjIds)
            ->get()
            ->keyBy('id') : collect();

        $fixedCount = 0;
        $errorCount = 0;
        $fixedData = [];

        return $db->transaction(function () use ($db, $qrcodeSales, $kyptSales, $pjSales, &$fixedCount, &$errorCount, &$fixedData) {
            foreach ($qrcodeSales as $sale) {
                try {
                    // 解析sale_id
                    [$prefix, $saleIdNum] = explode('_', $sale->sale_id, 2);
                    $tableType = strtolower($prefix);
                    
                    // 从预查询结果中获取销售记录（确保类型匹配）
                    $saleRecord = $tableType === 'kypt' ? $kyptSales->get((int)$saleIdNum) : $pjSales->get((int)$saleIdNum);
                    if (!$saleRecord || !$saleRecord->prod_id) continue;
                    
                    // 检查是否需要更新
                    $needUpdate = $sale->product_id != $saleRecord->prod_id 
                        || $sale->product_num != $saleRecord->num;
                    // $needUpdate = true;
                    if ($needUpdate) {
                        // 查询产品详细信息
                        $linkage = $db->table('linkage')
                            ->select('id', 'val', 'material')
                            ->find($saleRecord->prod_id);
                        if (!$linkage) continue;

                        // 更新产品信息
                        $db->table('out_stock_qrcode_sale')->where('id', $sale->id)->update([
                            'product_id' => $linkage->id,
                            'product_name' => $linkage->val,
                            'product_code' => $linkage->material,
                            'product_num' => $saleRecord->num,
                        ]);

                        // 记录修复后的数据
                        $fixedData[] = [
                            'sale_id' => $sale->sale_id,
                            'qrcode_sale_id' => $sale->id,
                            'old_data' => [
                                'product_id' => $sale->product_id,
                                'product_name' => $sale->product_name,
                                'product_code' => $sale->product_code,
                                'product_num' => $sale->product_num,
                            ],
                            'new_data' => [
                                'product_id' => $linkage->id,
                                'product_name' => $linkage->val,
                                'product_code' => $linkage->material,
                                'product_num' => $saleRecord->num,
                            ]
                        ];

                        $fixedCount++;
                    }
                } catch (\Exception $e) {
                    $errorCount++;
                    Log::get('system', 'system')->info("修复失败", ['sale_id' => $sale->sale_id, 'error' => $e->getMessage()]);
                    throw $e; // 抛出异常触发事务回滚
                }
            }
            $result = [
                'total' => $qrcodeSales->count(),
                'fixed' => $fixedCount,
                'error' => $errorCount,
                'message' => "修复完成：共{$qrcodeSales->count()}条，成功{$fixedCount}条，失败{$errorCount}条",
                'fixed_data' => $fixedData
            ];
            return $result;
        });
    }
}