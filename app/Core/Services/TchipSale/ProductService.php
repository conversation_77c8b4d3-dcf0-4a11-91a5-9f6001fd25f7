<?php

namespace App\Core\Services\TchipSale;

use App\Constants\DataBaseCode;
use App\Constants\TchipSaleCode;
use App\Core\Services\BusinessService;
use App\Core\Services\Notice\NoticeService;
use App\Core\Services\Production\Log\ProductionOperationLogService;
use App\Core\Services\TchipOa\OaQcErpService;
use App\Core\Services\TchipSale\Erp\ScBomdService;
use App\Core\Services\UserService;
use App\Core\Utils\Log;
use App\Core\Utils\TimeUtils;
use App\Model\TchipSale\LinkageModel;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;

class ProductService extends BusinessService
{

    /**
     * @Inject
     * @var LinkageModel
     */
    protected $model;

    public function updateProductOutStockDay()
    {
        $products = $this->model
            ->where('material', '<>', '')
            ->where('delete_time', 0)
            ->where('status', 1)
            ->where('_parentId', '>', 0)
            ->whereNotIn('_parentId', array_merge([12], $this->model->partCategoryid))->get();

        $count = count($products);
        $erpService = make(ErpService::class);
        $erpBomdService = make(ScBomdService::class);
        $productsArr = $products->toArray();
        $materials = array_column($productsArr, 'material');
        $erpData = $erpService->getGoodsByCodes($materials, 1);
        $erpData = $erpData ? array_column($erpData, null, 'CODE') : [];

        $finishedProd = $erpBomdService->bomdpgoods(['BOMDGOODS' => implode(',', $materials)], ['BOMDGOODS'=> 'IN']);

        Log::get('system', 'system')->info("找到{$count}个产品");

        foreach ($products as $key => $product) {
            $cacheKey = 'updateProductOutStockDay'.$product->material;
            $cacheLook = getCache($cacheKey);
            $i = 1 + $key;
            if ($cacheLook) {
                Log::get('system', 'system')->info("{$product->material}已更新跳过({$i}/{$count})");
                continue;
            }
            if (!empty($erpData[$product->material])) {
                // 1. ERP主要仓位数据
                $masterLocNum = $this->getProductErpOtherLocNum($product->material, $erpData[$product->material]['stock_macc']);
                // 2. 销售备货
                $stockNum = $this->getSaleStockNum($product->id);
                // 3. ERP成品数据
                $finishedNum = $this->getFinishedProductNum($product->material, $erpData, $finishedProd);

                // zzh_notqty = 未组装, owe_qty = 未送货,
                // 所有数据为0时，加1
                if ((int)$erpData[$product->material]['zzh_notqty'] <= 0 &&
                    (int)$erpData[$product->material]['owe_qty'] <= 0 &&
                    $masterLocNum <= 0 &&
                    $stockNum <= 0 &&
                    $finishedNum <= 0) {
                    $product->out_stock_day += 1;
                    $product->update();
                    Log::get('system', 'system')->info("执行产品:{$product->text},料号:{$product->material},缺货加1天. ({$i}/{$count})");
                } else {
                    $product->out_stock_day = 0;
                    $product->update();
                    Log::get('system', 'system')->info("执行产品:{$product->text},料号:{$product->material},清空缺货天数. ({$i}/{$count})");
                }
            }
            setCache($cacheKey, 1, TimeUtils::todaySecondsRemaining());
        }

        Log::get('system', 'system')->info("更新产品缺货天数完成");
        return true;
    }

    /**
     * 获取ERP主要仓位数量
     * @param $material
     * @param $macc 仓位数据，可空，
     * @return int
     */
    public function getProductErpOtherLocNum($material, $stockMacc = null)
    {
        $erpService = make(ErpService::class);
        if (!$stockMacc) {
            $erpData = $erpService->getGoodsByCodes($material);
            $stockMacc = $erpData[0]['stock_macc'] ?? [];
        }
        $masterLoc = $erpService->getMasterLoc();
        $masterLoc = array_column($masterLoc, 'CODE');
        $resNum = 0;
        if (!empty($stockMacc)) {
            foreach ($stockMacc as $macc) {
                if ($macc['FOPQTY'] > 0 && in_array($macc['LOC'], $masterLoc)) {
                    $resNum += (int) $macc['FOPQTY'];
                }
            }
        }
        return $resNum;
    }

    /**
     * 获取产品备货数量
     * @param $sizeId
     * @return int
     */
    public function getSaleStockNum($sizeId)
    {
        $orderModel = make(\App\Model\TchipSale\OrderlistModel::class);
        $shipModel =  make(\App\Model\TchipSale\OrdershiplistModel::class);
        $shipSql = $shipModel->selectRaw('SUM(shipmount)')->whereRaw('dwin_orderlist.id = dwin_ordershiplist.order_id')->toSql();
        $orders = $orderModel->selectRaw("count - IFNULL(({$shipSql}), 0) as mount")
            ->where('size_id', $sizeId)
            ->where('delete_time', 0)
            ->where('pre_stock', 1)
            // 未完成订单
            ->whereNotIn('status', [1479, 115])
            ->get();
        $resNum = 0;
        if ($orders) {
            foreach ($orders as $order) {
                $resNum += $order->mount;
            }
        }
        return $resNum;
    }

    /**
     * 获取产品的上级产品的库存数量
     * @param $material
     * @param $erpData ERP产品数据(多个)原因数组里存在上级产品的数据
     * @param $finishedProd 所有上级产品的集合
     * @return int|mixed
     */
    protected function getFinishedProductNum(string $material, array $erpData, array $finishedProd)
    {
        // $erpBomdService = make(ScBomdService::class);
        // $bomdGoods = $erpBomdService->bomdpgoods(['BOMDGOODS' => $material]);
        $finishedNum = 0;
        // if ($bomdGoods) {
        //     $erpService = make(ErpService::class);
        //     $materials = array_column($bomdGoods, 'BOMDPGOODS');
        //     $erpData = $erpService->getGoodsByCodes($materials);
        //     if ($erpData) {
        //         foreach ($erpData as $good) {
        //             foreach ($good['stock_macc'] as $cargoStock) {
        //                 if ($cargoStock['FOPQTY'] > 0) {
        //                     $finishedNum += $cargoStock['FOPQTY'];
        //                 }
        //             }
        //         }
        //     }
        // }
        // return $finishedNum;

        foreach ($finishedProd as $finished) {
            // 找到上级产品并存在ERP数据
            if ($finished['BOMDGOODS'] == $material && !empty($erpData[$finished['BOMDPGOODS']]['stock_macc'])) {
                foreach ($erpData[$finished['BOMDPGOODS']]['stock_macc'] as $macc) {
                    if ($macc['FOPQTY'] > 0) {
                        $finishedNum += $macc['FOPQTY'];
                    }
                }
            }
        }
        return $finishedNum;
    }

    //缺货提醒
    public function dailyOutStockProductRemind()
    {
        // 收集所有异常信息
        $exceptions = [];
        
        try {
            Log::get('system', 'system')->info('开始获取缺货产品数据');
            
            $saleApiService = make(SaleApiService::class);
            //用协程来分三类来获取数据
            //获取所有的产品分类
            $productCategory = make(OaQcErpService::class)->getErpProductList();
            //成品类
            $finishedParentId = 13;
            $finishedProductCategory = [];
            foreach ($productCategory as $item) {
                if ($item['ID'] == $finishedParentId) {
                    $finishedProductCategory = array_column($item['children'], 'ID');
                }
            }
            $finishedProductCategory = array_merge($finishedProductCategory, [13]);
            //配件类
            $accessoryParentId = 14;
            $accessoryProductCategory = [];
            foreach ($productCategory as $item) {
                if ($item['ID'] == $accessoryParentId) {
                    $accessoryProductCategory = array_column($item['children'], 'ID');
                }
            }
            $accessoryProductCategory = array_merge($accessoryProductCategory, [14]);
            //剩余分类
            $otherProductCategory = [];
            foreach ($productCategory as $item) {
                if (!in_array($item['ID'], $finishedProductCategory) && !in_array($item['ID'], $accessoryProductCategory)) {
                    $otherProductCategory[] = $item['ID'];
                    $otherProductCategory = array_merge($otherProductCategory, array_column($item['children']??[], 'ID'));
                }
            }
            //获取产品数据
            $filter = [
                'stock_status' => 2,
                'DUSE' => 1,
            ];
            
            // 使用协程并发获取三个分类的数据
            $finishedData = [];
            $accessoryData = [];
            $otherData = [];
            
            // 创建协程任务
            $tasks = [];
            
            // 成品类数据
            $tasks[] = \Swoole\Coroutine::create(function () use ($saleApiService, $filter, $finishedProductCategory, &$finishedData, &$exceptions) {
                try {
                    $finishedFilter = array_merge($filter, [
                        'PMPCCODE' => implode(',', $finishedProductCategory)
                    ]);
                    $finishedOp = ['PMPCCODE' => 'IN'];
                    $result = $saleApiService->getSecureStock($finishedFilter, $finishedOp, 1, 10000);
                    $finishedData = $result['rows'] ?? [];
                    Log::get('system', 'system')->info("成品类数据:".count($finishedData));
                } catch (\Exception $e) {
                    $exceptions[] = [
                        'type' => '获取成品类数据异常',
                        'message' => $e->getMessage(),
                        'time' => date('Y-m-d H:i:s')
                    ];
                    Log::get('system', 'system')->error('获取成品类数据异常', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            });
            
            // 配件类数据
            $tasks[] = \Swoole\Coroutine::create(function () use ($saleApiService, $filter, $accessoryProductCategory, &$accessoryData, &$exceptions) {
                try {
                    $accessoryFilter = array_merge($filter, [
                        'PMPCCODE' => implode(',', $accessoryProductCategory)
                    ]);
                    $accessoryOp = ['PMPCCODE' => 'IN'];
                    $result = $saleApiService->getSecureStock($accessoryFilter, $accessoryOp, 1, 10000);
                    $accessoryData = $result['rows'] ?? [];
                    Log::get('system', 'system')->info("配件类数据:".count($accessoryData));
                } catch (\Exception $e) {
                    $exceptions[] = [
                        'type' => '获取配件类数据异常',
                        'message' => $e->getMessage(),
                        'time' => date('Y-m-d H:i:s')
                    ];
                    Log::get('system', 'system')->error('获取配件类数据异常', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            });
            
            // 其他分类数据
            if (!empty($otherProductCategory)) {
                $tasks[] = \Swoole\Coroutine::create(function () use ($saleApiService, $filter, $otherProductCategory, &$otherData, &$exceptions) {
                    try {
                        $otherFilter = array_merge($filter, [
                            'PMPCCODE' => implode(',', $otherProductCategory)
                        ]);
                        $otherOp = ['PMPCCODE' => 'IN'];
                        $result = $saleApiService->getSecureStock($otherFilter, $otherOp, 1, 10000);
                        $otherData = $result['rows'] ?? [];
                        Log::get('system', 'system')->info("其他分类数据:".count($otherData));
                    } catch (\Exception $e) {
                        $exceptions[] = [
                            'type' => '获取其他分类数据异常',
                            'message' => $e->getMessage(),
                            'time' => date('Y-m-d H:i:s')
                        ];
                        Log::get('system', 'system')->error('获取其他分类数据异常', [
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                    }
                });
            }
            
            // 等待所有协程完成
            foreach ($tasks as $task) {
                if ($task !== false) {
                    \Swoole\Coroutine::join([$task]);
                }
            }
            
            // 合并所有数据
            $data = array_merge($finishedData, $accessoryData, $otherData);
            Log::get('system', 'system')->info('数据获取完成，总数据量:' . count($data));
            
            //发送邮件通知
            //1.获取用户
            //获取采购部门人员
            $sendUser = make(UserService::class)->assignDepartmentUserIds([19]);
            //李美瑕
            $sendUser[] = 183;
            $sendUser = unique_filter($sendUser);

            //2.构建邮件内容
            if (!empty($data)) {
                try {
                    // 构建表头
                    $tableHeader = '<tr style="background-color: #f5f5f5;">
                        <th style="padding: 12px 8px; border: 1px solid #ddd; text-align: left; font-weight: bold;">产品名称</th>
                        <th style="padding: 12px 8px; border: 1px solid #ddd; text-align: left; font-weight: bold;">规格</th>
                        <th style="padding: 12px 8px; border: 1px solid #ddd; text-align: left; font-weight: bold;">产品编码</th>
                        <th style="padding: 12px 8px; border: 1px solid #ddd; text-align: left; font-weight: bold; width: 80px;">当前库存</th>
                        <th style="padding: 12px 8px; border: 1px solid #ddd; text-align: left; font-weight: bold; width: 80px;">重点关注</th>
                    </tr>';
                    
                    $bodyContent = $tableHeader;
                    foreach ($data as $item) {
                        $bodyContent .= '<tr>';
                        $bodyContent .= '<td style="padding: 8px; border: 1px solid #ddd;">' . ($item['NAME'] ?? '') . '</td>';
                        $bodyContent .= '<td style="padding: 8px; border: 1px solid #ddd;">' . ($item['SPEC'] ?? '') . '</td>';
                        $bodyContent .= '<td style="padding: 8px; border: 1px solid #ddd;">' . ($item['CODE'] ?? '') . '</td>';
                        $bodyContent .= '<td style="padding: 8px; border: 1px solid #ddd; width: 80px; text-align: center;">' . ($item['stock_count'] ?? 0) . '</td>';
                        //重点关注
                        $isFocus = $item['is_focus'] ?? 0;
                        $focusText = '否';
                        $focusColor = '#666';
                        
                        if ($isFocus == 1) {
                            $focusText = '是';
                            $focusColor = '#ff0000'; // 红色
                        }
                        
                        $bodyContent .= '<td style="padding: 8px; border: 1px solid #ddd; width: 80px; text-align: center; color: ' . $focusColor . '; font-weight: bold;">' . $focusText . '</td>';
                        $bodyContent .= '</tr>';
                    }

                    make(NoticeService::class)->bindUserSend(257,['tableContent'=>$bodyContent,'host'=>env('TCHIP_SALE_API_URL')],'dailyOutStockProductRemind');
                    Log::get('system', 'system')->info('缺货产品提醒邮件发送成功');
                } catch (\Exception $e) {
                    $exceptions[] = [
                        'type' => '发送缺货产品提醒邮件异常',
                        'message' => $e->getMessage(),
                        'time' => date('Y-m-d H:i:s')
                    ];
                    Log::get('system', 'system')->error('发送缺货产品提醒邮件异常', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            } else {
                Log::get('system', 'system')->info('没有缺货产品数据，无需发送邮件');
            }
            
        } catch (\Exception $e) {
            $exceptions[] = [
                'type' => '每日缺货产品统计提醒系统异常',
                'message' => $e->getMessage(),
                'time' => date('Y-m-d H:i:s')
            ];
            Log::get('system', 'system')->error('每日缺货产品统计提醒异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
        
        // 统一发送异常通知
        if (!empty($exceptions)) {
            try {
                $exceptionSummary = "每日缺货产品统计提醒异常汇总：\n\n";
                foreach ($exceptions as $index => $exception) {
                    $exceptionSummary .= sprintf(
                        "%d. [%s] %s\n   错误信息：%s\n\n",
                        $index + 1,
                        $exception['time'],
                        $exception['type'],
                        $exception['message']
                    );
                }
                $exceptionSummary .= "请及时处理相关问题，确保缺货产品统计功能正常运行。";
                
                make(ProductionOperationLogService::class)->sendFailNotice($exceptionSummary);
                Log::get('system', 'system')->info('异常汇总通知发送成功，异常数量：' . count($exceptions));
            } catch (\Exception $e) {
                Log::get('system', 'system')->error('发送异常汇总通知失败', [
                    'error' => $e->getMessage(),
                    'exceptions_count' => count($exceptions)
                ]);
            }
        } else {
            Log::get('system', 'system')->info('每日缺货产品统计提醒执行完成，无异常');
        }
    }
}