<?php

namespace App\Core\Services\Shipment;

use App\Constants\ProductionCode;
use App\Constants\StatusCode;
use App\Core\Services\BusinessService;
use App\Core\Services\SnCode\SnCodeService;
use App\Core\Services\TchipSale\OutStockQrcodeService;
use App\Core\Services\TchipSale\SaleQrcodeService;
use App\Exception\AppException;
use App\Model\TchipBi\ShipmentModel;
use App\Model\TchipBi\ShipmentSaleModel;
use App\Model\TchipBi\ShipmentSaleSnModel;
use App\Core\Services\ExcelAnalyze\Writer\ExcelWriter;
use Carbon\Carbon;
use App\Model\TchipBi\SnCodeModel;
use App\Model\TchipSale\OutStockQrcodeModel;
use Exception;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use App\Core\Utils\Log;
class ShipmentService extends BusinessService
{

    /**
     * @Inject()
     * @var ShipmentModel
     */
    protected $model;

    public function doEdit(int $id, array $values)
    {
        Db::beginTransaction();
        try {
            //检查参数
            if (empty($values['qrcode_id']) || empty($values['sale_data'])) {
                throw new AppException(StatusCode::VALIDATION_ERROR, __('common.Missing_parameter'));
            }

            if ($id > 0) {
                $shipmentId = $id;
                $row = $this->model::query()->find($shipmentId);
                if (!$row) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                }
                //编辑只编辑sn号
                if (!empty($values['sale_data'])) {
                    foreach ($values['sale_data'] as $sale) {
                        $snData = $sale['sn_data'] ?? [];
                        $origin = ShipmentSaleSnModel::query()->where('shipment_sale_id', $sale['id'])->pluck('sn_code')->toArray();
                        //删除sn号
                        $snDel = array_diff($origin, $snData);
                        $snDel && ShipmentSaleSnModel::query()->where('shipment_sale_id', $sale['id'])->whereIn('sn_code', $snDel)->delete();
                        //新增sn号
                        $snNew = array_diff($snData, $origin);

                        if (!empty($snNew)) {
                            //校验
                            // make(SnCodeService::class)->filterAndCheckExistCode($snNew,$sale['product_code']);
                            //插入sn表
                            make(SnCodeService::class)->insertCodes($snNew);
                            $snInsert = [];
                            foreach ($snNew as $sn) {
                                $snInsert[] = [
                                    'shipment_id' => $shipmentId,
                                    'shipment_sale_id' => $sale['id'],
                                    'sn_code' => $sn,
                                    'created_at' => date('Y-m-d H:i:s')
                                ];
                            }
                            ShipmentSaleSnModel::query()->insert($snInsert);
                        }
                    }
                }
            } else {

                //检测是否已经添加
                if ($this->model::query()->where('qrcode_id', $values['qrcode_id'])->exists()) {
                    throw new AppException(StatusCode::VALIDATION_ERROR, __('common.Repeat_operation'));
                }
                $values['created_by'] = is_request() ? auth()->id() : 0;
                $result = $this->model::query()->create($values);
                $shipmentId = $result->id ?? 0;
                if (!empty($result->id)) {
                    //保存销售单的产品信息
                    $snInsert = [];
                    foreach ($values['sale_data'] as $sale) {
                        $sale['shipment_id'] = $shipmentId;
                        $saleResult = ShipmentSaleModel::query()->create($sale);
                        $snData = $sale['sn_data'] ?? [];
                        if (empty($snData)) continue;
                        //过滤且校验
                        // make(SnCodeService::class)->filterAndCheckExistCode($snData,$sale['product_code']);
                        //插入sn表
                        make(SnCodeService::class)->insertCodes($snData);
                        foreach ($snData as $sn) {
                            $snInsert[] = [
                                'shipment_id' => $shipmentId,
                                'shipment_sale_id' => $saleResult->id,
                                'sn_code' => $sn,
                                'created_at' => date('Y-m-d H:i:s')
                            ];
                        }
                    }
                    $snInsert && ShipmentSaleSnModel::query()->insert($snInsert);
                }
            }

            Db::commit();
        } catch (Exception $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }

        // 事务提交成功后，更新另一个数据库的submit_status
        // OutStockQrcodeModel使用的是TCHIP_SALE数据库连接，不在上面的事务范围内
        if (!empty($values['qrcode_id']) && isset($shipmentId)) {
            // 获取当前shipment的所有销售单
            $saleList = ShipmentSaleModel::query()->where('shipment_id', $shipmentId)->get();
            $totalSaleCount = $saleList->count();
            $saleWithSnCount = 0;

            // 统计有SN码的销售单数量
            foreach ($saleList as $sale) {
                $snCount = ShipmentSaleSnModel::query()->where('shipment_sale_id', $sale['id'])->count();
                if ($snCount > 0) {
                    $saleWithSnCount++;
                }
            }

            // 计算submit_status
            $submitStatus = 1; // 默认未提交
            if ($saleWithSnCount == 0) {
                $submitStatus = 1; // 所有销售单SN为空
            } elseif ($saleWithSnCount == $totalSaleCount) {
                $submitStatus = 2; // 所有销售单SN都不为空
            } else {
                $submitStatus = 3; // 部分销售单有SN
            }

            // 更新submit_status (在TCHIP_SALE数据库)
            make(OutStockQrcodeModel::class)::query()->where('id', $values['qrcode_id'])->update(['submit_status' => $submitStatus]);
        }

        return true;
    }

    /**
     * 二维码详情
     * 未提交表单时显示销售系统的信息，提交后显示bi的信息
     * @param int $qrcodeId
     * @return
     */
    public function getQrcodeOverView(int $qrcodeId)
    {
        $shipment = $this->model::query()->where('qrcode_id', $qrcodeId)->first();
        if (!empty($shipment->id)) {
            $result = $this->getOverView($shipment->id);
        } else {
            $result = make(OutStockQrcodeService::class)->getOverView($qrcodeId);
        }
        return $result;
    }

    public function getOverView($id)
    {
        $overView = $this->model::query()->with(['saleData'])->find($id);
        $overView = $overView ? $overView->toArray() : [];
        if (!empty($overView)) {
            $saleData = $overView['sale_data'] ?? [];
            foreach ($saleData as &$sale) {
                $sale['sn_data'] = ShipmentSaleSnModel::query()->where('shipment_sale_id', $sale['id'])->pluck('sn_code')->toArray();
            }
            $overView['sale_data'] = $saleData;
        }
        return $overView;
    }

    /**
     * 获取列表（从BI数据库获取，返回格式与getQrcodeList一致）
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @param int $limit
     * @return mixed
     */
    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        $keywords = !empty($filter['keywords']) ? $filter['keywords'] : '';
        unset($filter['keywords']);

        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);

        // 添加关键词搜索条件
        if ($keywords) {
            $query = $query->where(function ($query) use ($keywords) {
                $query->where('out_stock_no', 'like', '%' . $keywords . '%')
                    ->orWhere('order_no', 'like', '%' . $keywords . '%')
                    ->orWhere('client_name', 'like', '%' . $keywords . '%')
                    ->orWhere('shipment_sale.product_name', 'like', '%' . $keywords . '%')
                    ->orWhere('shipment_sale.product_code', 'like', '%' . $keywords . '%');
            });
        }

        // 关联shipment_sale表用于搜索产品信息
        $query->join('shipment_sale', function ($join) {
            $join->on('shipment_sale.shipment_id', '=', 'shipment.id')
                ->whereIn('shipment_sale.platform_id', ProductionCode::SHIPMENT_PLATFORM_ARR)
                ->whereNull('shipment_sale.deleted_at');
        });

        $query->select('shipment.*');
        $data = $query->with('saleData')->groupBy('shipment.id')->orderBy($sort, $order)->paginate($limit)->toArray();

        if (empty($data['data'])) {
            return $data;
        }

        // 获取SN码统计
        $shipmentIds = array_column($data['data'], 'id');
        $snCounts = ShipmentSaleSnModel::query()
            ->leftJoin('shipment_sale', function ($join) {
                $join->on('shipment_sale.id', '=', 'shipment_sale_sn.shipment_sale_id');
                $join->whereNull('shipment_sale.deleted_at');
            })
            ->selectRaw('bi_shipment_sale.shipment_id, COUNT(bi_shipment_sale_sn.id) as sn_count')
            ->whereIn('shipment_sale.shipment_id', $shipmentIds)
            ->groupBy('shipment_sale.shipment_id')
            ->pluck('sn_count', 'shipment_id')
            ->toArray();

        // 处理数据
        foreach ($data['data'] as &$item) {
            $shipmentId = $item['id'];

            // 处理sale_data，只保留指定平台的数据
            $saleData = $item['sale_data'] ?? [];
            $filteredSaleData = array_filter($saleData, function ($sale) {
                return in_array($sale['platform_id'], ProductionCode::SHIPMENT_PLATFORM_ARR);
            });

            $item['sale_data'] = array_values($filteredSaleData);

            // 计算产品数量总和和汇总产品信息
            $totalProductNum = 0;
            $productData = [];
            foreach ($filteredSaleData as $sale) {
                $totalProductNum += $sale['product_num'] ?? 0;
                $productData[$sale['product_id']] = [
                    'name' => $sale['product_name'],
                    'code' => $sale['product_code'],
                ];
            }

            $item['total_product_num'] = $totalProductNum;
            $item['product_name'] = implode(',', array_column($productData, 'name'));
            $item['product_code'] = implode(',', array_column($productData, 'code'));

            // 添加扫码数量统计
            $item['scan_count'] = $snCounts[$shipmentId] ?? 0;
        }

        return $data;
    }


    /**
     * 获取列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @param int $limit
     * @return mixed
     */
    public function getQrcodeList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10)
    {
        // 检查是否存在 shipment.created_at 过滤条件
        if (!empty($filter['shipment.created_at'])) {
            // 根据 shipment 创建时间过滤获取 qrcode_id 列表
            $sFilter = ['created_at' => $filter['shipment.created_at']];
            $sOp = ['created_at' => $op['shipment.created_at']];
            list($query, $sLimit, $sSort, $sOrder) = $this->buildparams($sFilter, $sOp, 'id', 'desc', 9999);
            $filteredQrcodeIds = $query->pluck('qrcode_id')->toArray();

            if (!empty($filteredQrcodeIds)) {
                $filter['out_stock_qrcode.id'] = implode(',',$filteredQrcodeIds);
                $op['out_stock_qrcode.id'] = 'IN';
            } else {
                // 如果没有找到匹配的 qrcode_id，返回空结果
                return [
                    'data' => [],
                    'total' => 0,
                    'per_page' => $limit,
                    'current_page' => 1,
                    'last_page' => 1
                ];
            }
        }
        // 移除 shipment 相关的过滤条件，添加 qrcode_id 过滤
        unset($filter['shipment.created_at']);
        unset($op['shipment.created_at']);

        $result = make(OutStockQrcodeService::class)->getList($filter, $op, $sort, $order, $limit);

        if (!empty($result['data'])) {
            // 获取所有出库单的ID，用于查询bi系统扫码数量
            $qrcodeIds = array_column($result['data'], 'id');

            // 获取bi系统中对应的扫码数量
            $scanCounts = [];
            $shipments = $this->model::query()
                ->whereIn('qrcode_id', $qrcodeIds)
                ->get(['id', 'qrcode_id', 'created_at']);

            if ($shipments->count() > 0) {
                $shipmentIds = $shipments->pluck('id')->toArray();
                $qrcodeShipmentMap = $shipments->pluck('id', 'qrcode_id')->toArray(); // 反转映射
                $qrcodeCreatedAtMap = $shipments->pluck('created_at', 'qrcode_id')->toArray(); // created_at映射
                // 获取SN码数据
                $snData = ShipmentSaleSnModel::query()
                    ->leftJoin('shipment_sale', function ($join) {
                        $join->on('shipment_sale.id', '=', 'shipment_sale_sn.shipment_sale_id');
                        $join->whereNull('shipment_sale.deleted_at');
                    })
                    ->whereIn('shipment_sale_sn.shipment_id', $shipmentIds)
                    ->select('shipment_sale_sn.shipment_id', 'shipment_sale_sn.shipment_sale_id', 'shipment_sale_sn.sn_code')
                    ->get();

                // 一次性构建所有映射数据
                $saleSnCounts = [];
                $shipmentSnCodes = [];
                $saleSnCodes = [];
                
                foreach ($snData as $item) {
                    $shipmentId = $item->shipment_id;
                    $saleId = $item->shipment_sale_id;
                    $snCode = $item->sn_code;
                    
                    // 统计数量
                    $saleSnCounts[$shipmentId][$saleId] = ($saleSnCounts[$shipmentId][$saleId] ?? 0) + 1;
                    $scanCounts[$shipmentId] = ($scanCounts[$shipmentId] ?? 0) + 1;
                    
                    // 收集SN号码
                    $shipmentSnCodes[$shipmentId][] = $snCode;
                    $saleSnCodes[$shipmentId][$saleId][] = $snCode;
                }
            }

            // 为每个出库单添加统计数据
            foreach ($result['data'] as &$item) {
                $qrcodeId = $item['id'];
                $shipmentId = $qrcodeShipmentMap[$qrcodeId] ?? null;

                $totalProductNum = 0;
                //需要录入sn码的数量
                $needSnNum = 0;
                if (!empty($item['sale_data'])) {
                    foreach ($item['sale_data'] as &$sale) {
                        //保证id不重复
                        $originalSaleId = $sale['id'];
                        $sale['id'] = $item['id'].'_'.$sale['id'];
                        // 为每个销售单添加SN码数量
                        $saleId = $sale['id'] ?? 0;
                        $sale['scan_count'] = $saleSnCounts[$shipmentId][$originalSaleId] ?? 0;
                        // 添加sale级别的SN号码
                        $sale['sn_codes'] = $saleSnCodes[$shipmentId][$originalSaleId] ?? [];

                        // 只统计指定平台的数据
                        if (in_array($sale['platform_id'], [23, 25])) {
                            $needSnNum += $sale['product_num'] ?? 0;
                        }
                        $totalProductNum += $sale['product_num'] ?? 0;
                    }
                }
                $item['need_sn_product_num'] = $needSnNum;
                $item['product_num'] = $totalProductNum;
                $item['scan_count'] = $scanCounts[$shipmentId] ?? 0;
                // 添加shipment级别的SN号码
                $item['sn_codes'] = $shipmentSnCodes[$shipmentId] ?? [];
                $createdAt = $qrcodeCreatedAtMap[$qrcodeId] ?? null;
                $item['shipment_created_at'] = $createdAt ? \Carbon\Carbon::parse($createdAt)->format('Y-m-d H:i') : null;
                $item['submit_status_text']  = ProductionCode::SHIPMENT_COMMIT_STATUS[$item['submit_status']]??''; 
            }
        }

        return $result;
    }

    public function checkSnCode(array $snData, string $productCode)
    {
        $snData = unique_filter($snData);
        if (empty($snData)) return [];
        $exist = SnCodeModel::query()->whereIn('sn_code', $snData)->where('used_product_code', $productCode)->pluck('sn_code')->toArray();
        return array_values(array_diff($snData, $exist));
    }

    /**
     * 导出出库单列表
     * @param array $filter
     * @param array $op
     * @param string $sort
     * @param string $order
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Reader\Exception
     */
    public function exportQrcodeList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC')
    {
        $data = $this->getQrcodeList($filter, $op, $sort, $order, 9999);
        $data = !empty($data['data']) ? $data['data'] : [];
        
        $filename = "出库单列表.xls";
        $sheetName = 'sheet1';
        $excelWriter = new ExcelWriter($filename, $sheetName);
        
        // 统一配置：列名、字段名、列宽、是否合并
        $columns = [
            ['title' => '序号', 'field' => 'index', 'width' => 6, 'merge' => false],
            ['title' => '出库单号', 'field' => 'out_stock_no', 'width' => 18, 'merge' => 'index'],
            ['title' => '产品名称', 'field' => 'product_name', 'width' => 35],
            ['title' => '产品料号', 'field' => 'product_code', 'width' => 20],
            ['title' => '数量', 'field' => 'product_num', 'width' => 8],
            ['title' => '扫码数量', 'field' => 'scan_count', 'width' => 8],
            ['title' => '订单号', 'field' => 'order_no', 'width' => 12, 'merge' => 'out_stock_no'],
            ['title' => '提交状态', 'field' => 'submit_status_text', 'width' => 12, 'merge' => 'out_stock_no'],
            ['title' => '客户名称', 'field' => 'client_name', 'width' => 25, 'merge' => 'out_stock_no'],
            ['title' => '提货时间', 'field' => 'create_time_text', 'width' => 16, 'merge' => 'out_stock_no'],
            ['title' => '出库时间', 'field' => 'shipment_created_at', 'width' => 16, 'merge' => 'out_stock_no'],
            ['title' => 'SN码', 'field' => 'sn_codes', 'width' => 30],
        ];
        
        // 取值配置：定义字段的数据来源
        $fieldSources = [
            'sale' => ['product_name', 'product_code', 'product_num', 'scan_count', 'sn_codes'],
            'item' => ['out_stock_no', 'order_no', 'submit_status_text', 'client_name', 'create_time_text', 'shipment_created_at'],
            'index' => ['index']
        ];
        
        // 自动生成配置
        $option = array_column($columns, 'field', 'title');
        $titleData = [array_column($columns, 'title')];
        $width = array_column($columns, 'width');
        $mergeRowIfSameCol = [];
        
        foreach ($columns as $column) {
            if (isset($column['merge'])) {
                $mergeRowIfSameCol[$column['field']] = $column['merge'];
            }
        }
        
        $excelWriter->setColumnWidthFromList($width);
        
        $title_style = [];
        for($i=0;$i<count($titleData[0]);$i++){
            $title_style[$i] = ['backgroundColor' => 'b4c6e7', 'bold' => true];
        }
        
        $excelWriter->setRowHeight(1, 30);
        $excelWriter->addData($titleData,[],[],[],$title_style);
        
        // 基于配置构建数据行
        $allRows = [];
        $index = 1;
        foreach ($data as $item) {
            $saleData = $item['sale_data'] ?? [];
            
            if (empty($saleData)) {
                // 如果没有销售数据，显示一行基本信息
                $row = [];
                foreach ($columns as $column) {
                    $field = $column['field'];
                    
                    if (in_array($field, $fieldSources['index'])) {
                        $row[$field] = $index++;
                    } elseif (in_array($field, $fieldSources['item'])) {
                        $row[$field] = $item[$field] ?? '';
                    } elseif (in_array($field, $fieldSources['sale'])) {
                        $row[$field] = '';
                    }
                }
                $allRows[] = $row;
            } else {
                // 按销售数据展开
                foreach ($saleData as $sale) {
                    $row = [];
                    foreach ($columns as $column) {
                        $field = $column['field'];
                        
                        if (in_array($field, $fieldSources['index'])) {
                            $row[$field] = $index;
                        } elseif (in_array($field, $fieldSources['item'])) {
                            $row[$field] = $item[$field] ?? '';
                        } elseif (in_array($field, $fieldSources['sale'])) {
                            if ($field === 'sn_codes') {
                                $row[$field] = is_array($sale[$field] ?? []) ? implode(', ', $sale[$field]) : '';
                            } else {
                                $row[$field] = $sale[$field] ?? '';
                            }
                        }
                    }
                    $allRows[] = $row;
                }
                $index++;
            }
        }
        // 一次性添加所有数据并应用合并规则
        $excelWriter->addData($allRows, [], $mergeRowIfSameCol);
        
        $result = $excelWriter->download();
        $excelWriter->close();
        return $result;
    }

    public function updateQrcodeShipment()
    {
        //销售系统的出库单修复更新（已包含TCHIP_SALE数据库事务）
        $qrcodeSaleResult = make(OutStockQrcodeService::class)->fixQrCode();
        $qrcodeSaleResult['fix_shipment_num'] = 0;
        // 如果有修复的数据，在BI数据库事务中更新bi_shipment_sale表
        if (!empty($qrcodeSaleResult['fixed_data'])) {
            Db::transaction(function () use (&$qrcodeSaleResult) {
                foreach ($qrcodeSaleResult['fixed_data'] as $fixedItem) {
                    $saleId = $fixedItem['sale_id'];
                    $newData = $fixedItem['new_data'];
                    
                    // 更新bi_shipment_sale表中对应sale_id的记录
                    ShipmentSaleModel::query()
                        ->where('sale_id', $saleId)
                        ->update([
                            'product_id' => $newData['product_id'],
                            'product_name' => $newData['product_name'],
                            'product_code' => $newData['product_code'],
                            'product_num' => $newData['product_num'],
                        ]);
                    $qrcodeSaleResult['fix_shipment_num']++;   
                }
            });
        }
        Log::get('system', 'system')->info("修复结束",$qrcodeSaleResult);
        return $qrcodeSaleResult;
    }
}
