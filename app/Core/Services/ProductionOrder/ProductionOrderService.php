<?php

namespace App\Core\Services\ProductionOrder;

use App\Constants\AssembleOrderCode;
use App\Constants\ProductionCode;
use App\Constants\ProductionOrderCode;
use App\Constants\StatusCode;
use App\Constants\WorkFlowSceneCode;
use App\Core\Services\BusinessService;
use App\Core\Services\ExceptionRecord\ExceptionRecordService;
use App\Core\Services\MacAddress\MacAddressService;
use App\Core\Services\Notice\NoticeService;
use App\Core\Services\ProductionFactory\ProductionFactoryService;
use App\Core\Services\TchipSale\OrderService;
use App\Core\Services\TchipSale\StockOrderListService;
use App\Core\Services\UserService;
use App\Core\Services\WorkFlow\WorkStatusService;
use App\Core\Utils\Log;
use App\Exception\AppException;
use App\Model\TchipBi\CategoryModel;
use App\Model\TchipBi\MacAddressModel;
use App\Model\TchipBi\ProductionFactoryModel;
use App\Model\TchipBi\ProductionOrderInfoModel;
use App\Model\TchipBi\ProductionOrderModel;
use App\Model\TchipBi\ProductionOrderSummaryModel;
use App\Model\TchipBi\UserModel;
use App\Model\TchipBi\WorkStatusModel;
use App\Model\TchipSale\OrderlistModel;
use App\Model\TchipSale\StockOrderlistModel;
use Exception;
use Hyperf\Database\Model\Builder;
use Hyperf\Database\Model\Model;
use Hyperf\DbConnection\Db;
use Hyperf\Di\Annotation\Inject;
use Hyperf\Utils\Parallel;
use Psr\SimpleCache\CacheInterface;
use Throwable;
use Hyperf\Context\Context;
use App\Core\Services\AuthService;


class   ProductionOrderService extends BusinessService
{
    /**
     * @Inject()
     * @var ProductionOrderModel
     */
    protected $model;

    /**
     * @Inject()
     * @var ProductionOrderInfoService
     */
    protected $infoService;
    /**
     * @Inject()
     * @var MacAddressService
     */
    protected $macService;
    /**
     * @Inject
     * @var CacheInterface
     */
    private $cache;
    /**
     * @Inject()
     * @var AuthService
     */
    protected $authService;

    public function getList(array $filter = [], array $op = [], string $sort = 'id', string $order = 'DESC', int $limit = 10, $field = [])
    {
        $keywords = !empty($filter['keywords']) ? $filter['keywords'] : '';
        unset($filter['keywords']);
        $summaryKeywords = !empty($filter['summary_keywords']) ? $filter['summary_keywords'] : '';
        unset($filter['summary_keywords']);
        $hasMaterialVerification = !empty($filter['hasMaterialVerification']) ? $filter['hasMaterialVerification'] : '';
        unset($filter['hasMaterialVerification']);
        $hasIQC = !empty($filter['hasIQC']) ? $filter['hasIQC'] : '';
        unset($filter['hasIQC']);

        list($query, $limit, $sort, $order) = $this->buildparams($filter, $op, $sort, $order, $limit);
        if ($keywords) {
            $query = $query->where(function ($query) use ($keywords) {
                $query->where('code', 'like', '%' . $keywords . '%')
                    ->orWhere('product_name', 'like', '%' . $keywords . '%')
                    ->orWhere('product_code', 'like', '%' . $keywords . '%');
            });
        }

        // 0:获取全部 1：只获取存在的 2： 获取不存在的
        // 存在关联IQC
        if ($hasIQC !== null) {
            // 0: 获取全部 不采取操作
            // 1: 只获取存在关联IQC的记录
            if ($hasIQC == 1) {
                $query = $query->whereExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('production_order_iqc')
                        ->whereRaw('bi_production_order_iqc.production_order_id = bi_production_order.id')
                        ->whereNull('production_order_iqc.deleted_at');
                });
            }
            // 2: 获取没有关联IQC的记录
            elseif ($hasIQC == 2) {
                $query = $query->whereNotExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('production_order_iqc')
                        ->whereRaw('bi_production_order_iqc.production_order_id = bi_production_order.id')
                        ->whereNull('production_order_iqc.deleted_at');
                });
            }
        }

        // 存在变更物料
        if ($hasMaterialVerification !== null) {
            // 0: 获取全部
            // 1: 只获取存在变更物料记录
            if ($hasMaterialVerification == 1) {
                $query = $query->whereExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('production_order_change_material')
                        ->whereRaw('bi_production_order_change_material.production_order_id = bi_production_order.id');
                });
            }
            // 2: 获取没有变更物料记录
            elseif ($hasMaterialVerification == 2) {
                $query = $query->whereNotExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('production_order_change_material')
                        ->whereRaw('bi_production_order_change_material.production_order_id = bi_production_order.id');
                });
            }
        }
        $role = $this->authService->getRole();  
        $role = array_column($role,'role');
        if(in_array('Factory',$role) || in_array('ProcessingFactory',$role)){
            $factoryList = make(ProductionFactoryService::class)->getFactoryByUser()?:[0];
            $query = $query->whereIn('factory_id', $factoryList);
        }

//         //获取是工厂人员，则只能看到自身工厂相关的数据
//         $factoryList = make(ProductionFactoryService::class)->getFactoryByUser();
//         if($factoryList){
//             $query = $query->whereIn('factory_code', array_column($factoryList, 'code'));
//             //获取资料审核完毕的节点
// //            $dataAuditedSort = WorkStatusModel::query()->where('key', 'data_to_audit')->value('sort') ?: 0;
// //            $query = $query->where('work_status.sort', '<', $dataAuditedSort);
//         }

        $defaultFiled = [
            'production_order.*',
            'production_order_info.production_order_id',
            'production_order_info.work_status_id',
            'production_order_info.order_user_id',
            'production_order_info.approve_status',
            'production_order_info.hardware_user_id',
            'production_order_info.layout_user_id',
            'production_order_info.software_user_id',
            'production_order_info.structure_user_id',
            'production_order_info.approve_date',
            'work_status.name as work_status_name',
            'work_status.key as work_status_key',
            'production_order_info.predict_online_time',
            'production_order_info.is_complete',
            'production_order_summary.patches_excellent_rate',
            'production_order_summary.test_excellent_rate',
            Db::raw("if(bi_production_order_attachment.id is null, 0, 1) as has_repair_file"),
        ];
        $field = $field ?: $defaultFiled;

        $categoryId = CategoryModel::query()->where('keywords','repair_file')->value('id')?:0;
        $query = $query->leftJoin('production_order_info', 'production_order_info.production_order_id', '=', 'production_order.id')
            ->leftJoin('work_status', 'work_status.id', '=', 'production_order_info.work_status_id')
            ->leftJoin('production_order_attachment',function ($join)use($categoryId){
                $join->on('production_order_attachment.production_order_id', '=', 'production_order.id')
                    ->whereNull('production_order_attachment.deleted_at')
                    ->where('production_order_attachment.category_pid', '=', $categoryId);
            })->leftJoin('production_order_summary', 'production_order_summary.production_order_id', '=', 'production_order.id');
        //生产总结关键字查询
        if ($summaryKeywords) {
            $query = $query->where(function ($query) use ($summaryKeywords) {
                $query->where('production_order_summary.production_remark', 'like', '%' . $summaryKeywords . '%')
                    ->orWhere('production_order_summary.test_remark', 'like', '%' . $summaryKeywords . '%');
            })->join('production_order_summary', 'production_order_summary.production_order_id', '=', 'production_order.id');
        }
        $paginate = $query->select($field)
            ->orderBy($sort, $order);

        if ($sort != 'predict_online_time') {
            $paginate = $paginate->orderBy('predict_online_time', 'ASC');
        }

        $paginate = $paginate->groupBy('production_order.id')->paginate($limit)->toArray();
        //获取用户信息
//        $userIdArr = array_unique(array_merge(array_column($paginate['data'], 'hardware_user_id'),
//            array_column($paginate['data'], 'software_user_id')));
        $userIdArr = collectFiledArrFromData($paginate['data'],['hardware_user_id','layout_user_id','software_user_id']);
        $userData = $userIdArr ? UserModel::query()->whereIn('id', $userIdArr)->pluck('name', 'id')->toArray() : [];
        //获取总结信息
        $orderIdArr = array_column($paginate['data'], 'id');
        $summaryData = $orderIdArr ? ProductionOrderSummaryModel::query()->whereIn('production_order_id', $orderIdArr)->get()->toArray() : [];
        $summaryData = array_column($summaryData, null, 'production_order_id');
        foreach ($paginate['data'] as &$item) {
            $item['hardware_user_name']  =$userData[$item['hardware_user_id']]??'';
            $item['layout_user_name']  =$userData[$item['layout_user_id']]??'';
            $item['software_user_name']  =$userData[$item['software_user_id']]??'';
            // 已进行天数
            // $item['order_days'] = calculateDaysBetween($item['delivery_date'], date('Y-m-d'));
            $item['order_days'] = calculateDaysBetween($item['delivery_date'], (!empty($item['approve_date']) ? $item['approve_date'] : date('Y-m-d')));
            //获取工厂简称
            $item['factory_short_name'] = ProductionOrderCode::FACTORY_DEFAULT_INFO[$item['factory_name']]['short_name']??'';
            //todo 统一汇总查询还是建字段存储
            //获取上传审核状态
            $hardwareFileStatus = make(ProductionOrderAttachmentService::class)->getFileStatusByCategory('hardware',$item['id']);
            $softwareFileStatus = make(ProductionOrderAttachmentService::class)->getFileStatusByCategory('software', $item['id']);
            $schematicFileStatus = make(ProductionOrderAttachmentService::class)->getFileStatusByCategory('schematic', $item['id']);
            $layoutFileStatus = make(ProductionOrderAttachmentService::class)->getFileStatusByCategory('layout', $item['id']);
            $item['hardware_upload_status'] = $hardwareFileStatus['upload_status']??0;
            $item['hardware_audit_status'] = $hardwareFileStatus['audit_status']??0;
            $item['software_upload_status'] = $softwareFileStatus['upload_status']??0;
            $item['software_audit_status'] = $softwareFileStatus['audit_status']??0;
            $item['schematic_upload_status'] = $schematicFileStatus['upload_status']??0;
            $item['schematic_audit_status'] = $schematicFileStatus['audit_status']??0;
            $item['layout_upload_status'] = $layoutFileStatus['upload_status']??0;
            $item['layout_audit_status'] = $layoutFileStatus['audit_status']??0;
            //返回工作状态名称的后缀文本
            $item['work_status_append_text'] = '';
            $item['work_status_tips'] = '';
            //资料录入，添加未上传完的分类文本
            if ($item['work_status_key'] == 'data_to_insert') {
                if (!$item['hardware_upload_status'] && !$item['software_upload_status']) {
                    $item['work_status_append_text'] = '待录入';
                    $item['work_status_tips'] = '硬件和软件资料待录入';
                } elseif (!$item['hardware_upload_status'] && $item['software_upload_status']) {
                    $item['work_status_append_text'] = '硬件';
                    $item['work_status_tips'] = '硬件资料待录入';
                } elseif ($item['hardware_upload_status'] && !$item['software_upload_status']) {
                    $item['work_status_append_text'] = '软件';
                    $item['work_status_tips'] = '软件资料待录入';
                }
            }
            //生产中，补充未完成状态
            if ($item['work_status_key'] == 'producing') {
                $summary = $summaryData[$item['id']] ?? [];
                if (empty($summary) || (!$summary['chip_status'] && !$summary['test_status'])) {
                    $item['work_status_append_text'] = '待总结';
                    $item['work_status_tips'] = '贴片和测试产能待填写';
                } elseif ($summary['chip_status'] && !$summary['test_status']) {
                    $item['work_status_append_text'] = '测试';
                    $item['work_status_tips'] = '测试产能待填写';
                } elseif (!$summary['chip_status'] && $summary['test_status']) {
                    $item['work_status_append_text'] = '贴片';
                    $item['work_status_tips'] = '贴片产能待填写';
                }
            }
            $item['production_count'] = $this->getProductionCount($item);
            //计算 pass_rate：(贴片良率 * 测试良率) / 100，保留两位小数
            if (!empty($item['patches_excellent_rate']) && !empty($item['test_excellent_rate'])) {
                $item['pass_rate'] = number_format(($item['patches_excellent_rate'] * $item['test_excellent_rate']) / 100, 2).'%';
            } else {
                $item['pass_rate'] = '--';
            }
        }
        return $paginate;
    }

    public function getOverView($id)
    {
        $overView = $this->model::query()->with(['iqc', 'changeMaterial', 'info', 'outhelpRecord' => function ($query) {
            return $query->where('last', 1);
        }])->find($id);
        $overView = $overView ? $overView->toArray() : [];
        if ($overView) {
            $info = $overView['info'];
            unset($overView['info']);
            $info['production_order_info_id'] = $info['id'];
            $overView = array_merge($info, $overView);
            //获取上次生产总结
            $overView['pre_production_order_id'] = $this->getPreOrderId($overView['product_code'],$overView['id']);
            $overView['pre_summary'] = ProductionOrderSummaryModel::query()->where('production_order_id', $overView['pre_production_order_id'])->first();
            if ($overView['pre_summary']) {
                $overView['pre_summary']['order_code'] = $this->model::query()->where('id', $overView['pre_production_order_id'])->value(
                    'code'
                ) ?: '';
            }
            //计算订单天数,已进行天数
            // $overView['order_days'] = $overView['delivery_date'] ? (int)((time() - strtotime($overView['delivery_date'])) / 86400) : null;
            $overView['order_days'] = calculateDaysBetween($overView['delivery_date'], (!empty($overView['approve_date']) ? $overView['approve_date'] : date('Y-m-d')));
            //获取生产次数
            $overView['production_count'] = $this->getProductionCount($overView) ;
            //获取相关人员
            $userTypeData = [];
            $userIds = [
                'create_user'             => $overView['create_user_id'],
                'hardware_user'           => $overView['hardware_user_id'],
                'layout_user'           => $overView['layout_user_id'],
                'software_user'           => $overView['software_user_id'],
                'structure_user'          => $overView['structure_user_id'],
                'order_user'              => $overView['order_user_id'],
                'production_user'         => $overView['production_user_id'],
                'test_user'               => $overView['test_user_id'],
                'attachments_upload_user' => $overView['attachments_upload_user_id']
            ];
            $userIdsArr = array_unique(array_filter(array_values(($userIds))));
            if (!empty($userIdsArr)) {
                $user_data = UserModel::query()->whereIn('id', $userIdsArr)->pluck('name', 'id')->toArray();
                foreach ($userIds as $userType => $userId) {
                    $userTypeData[$userType] = [
                        'id'   => $userId ?: null,
                        'name' => $user_data[$userId] ?? null,
                    ];
                }
            }
            $overView['user_type_data'] = $userTypeData;

            //获取状态列表
            $overView['work_status_arr'] = make(WorkStatusService::class)->formatStatus($overView['work_status_id']);

            //mac地址
            $overView['mac_address_arr'] = MacAddressModel::query()
                ->where([
                    'used_type' => ProductionCode::CODE_USED_TYPE_PRODUCTION,
                    'used_relate_id'   => $id
                ])
                ->select([
                    'mac_address.id as mac_id',
                    'mac_address.mac_address',
                    'mac_address.mac_decimal'
                ])
                ->get();
            if (!empty($overView['outhelp_record'])) {
                $overView['outhelp_record']['updated_text'] = date('Y-m-d H:i', strtotime($overView['outhelp_record']['updated_at']));
                $overView['outhelp_record']['user_name'] = UserModel::query()->where('id', $overView['outhelp_record']['user_id'])->value('name');
            }

            // 销售订单要求数据获取
            $overView['order_demand'] = !empty($overView['stock_order_code']) ? make(OrderService::class)->orderDemandByStockSn($overView['stock_order_code']) : [];
            //销售系统客户名称获取
            if (!empty($overView['stock_order_code'])) {
                $clientInfo = make(StockOrderListService::class)->getStockOrderClient($overView['stock_order_code']);
                $overView['client_name'] = !empty($clientInfo[$overView['stock_order_code']]['display_name']) ? $clientInfo[$overView['stock_order_code']]['display_name'] : '';
            } else {
                $overView['client_name'] = '';
            }
            
        }
        return $overView;
    }

    /**
     * 获取同个产品上次挂起的订单
     * @param $productCode
     * @return false|\Hyperf\Utils\HigherOrderTapProxy|int|mixed|\Tightenco\Collect\Support\HigherOrderTapProxy
     */
    public function getPreOrderId($productCode,$orderId)
    {
        return ProductionOrderModel::query()
            ->leftJoin('production_order_info as info', 'info.production_order_id', '=', 'production_order.id')
            ->where('product_code', $productCode)
            ->where('production_order.id','<',$orderId)
            ->where('info.summary_finish_status', '=', 2)
            ->orderBy('production_order.created_at', 'desc')
            ->select('production_order.id')
            ->value('id')?:0;
    }


    /**
     * @param int $id 等于【-1】代表是自动同步创建
     * @param array $values
     * @return ProductionOrderModel|array|bool|Builder|Model|int
     */
    public function doEdit(int $id, array $values)
    {
        $result = [];
        $macRelationCode = ProductionCode::CODE_USED_TYPE_PRODUCTION;
        $isChangeCompleted = false;
        Db::beginTransaction();
        try {
            if ($id > 0) {
                $orderId = $id;
                $row = $this->model::query()->find($orderId);
                $info = ProductionOrderInfoModel::query()->where('production_order_id', $orderId)->first();
                if (!$row || !$info) {
                    throw new AppException(StatusCode::ERR_SERVER, __('common.No_results_were_found'));
                }
                if (!empty($values)) {
                    //处理备货单
                    $this->handleStockValue($values);
                    if (isset($values['is_complete']) && $values['is_complete'] == 1 && $values['is_complete'] != $info['is_complete']) {
                        $isChangeCompleted = true;
                    }
                    //如果初始mac改变，先取消原来的关联，再重新分配
                    //如果数量改变，增加分配或者取消分配
                    $rowAmount = $row->woqty;

                    if (empty($info['start_mac_address']) && !empty($values['start_mac_address'])) {
                        //没填写到填写，直接分配
                        $this->macService->assignMacAddress($macRelationCode, $id, $values['woqty'] ?: 0,$values['start_mac_address']);
                    } elseif (!empty($info['start_mac_address']) && empty($values['start_mac_address'])) {
                        //有填写到无填写，直接取消关联
                        $this->macService->reduceByRelationId($id, $macRelationCode, $rowAmount);
                    } elseif (!empty($info['start_mac_address']) && !empty($values['start_mac_address'])) {
                        //都有填写，初始mac改变则取消再分配，数量变化则根据数量大小处理
                        if (isset($values['start_mac_address']) && $values['start_mac_address'] != $info['start_mac_address'] && $rowAmount > 0) {
                            $this->macService->reduceByRelationId($id, $macRelationCode, $values['woqty']);
                            $this->macService->assignMacAddress($macRelationCode, $id, $values['woqty'] ?: 0,$values['start_mac_address']);
                        } elseif (isset($values['woqty']) && is_numeric($values['woqty']) && $values['woqty'] != $rowAmount) {
                            $newAmount = $values['woqty'];
                            //新数量大于旧数量自动分配mac地址
                            if ($newAmount > $rowAmount) {
                                $this->macService->assignMacAddress($macRelationCode, $id, $newAmount - $rowAmount);
                            }
                            //新数量小于旧数量自动取消mac地址关联
                            if ($newAmount < $rowAmount) {
                                //自动取消mac地址
                                $this->macService->reduceByRelationId($id, $macRelationCode, $rowAmount - $newAmount);
                            }
                        }
                    }

                    if(empty($row->create_user_id)){
                        $values['create_user_id'] = auth()->id();
                    }
                    //工厂信息改变
                    if(isset($values['factory_code']) && $values['factory_code'] != $row->factory_code){
                        $values['factory_id'] = make(ProductionFactoryService::class)->findOrCreateFactory(
                            [
                                'code' => $values['factory_code'],
                                'name' => $values['factory_name']
                            ]
                        );
                    }
                    //编辑订单信息
                    $result = $row->update($values);

                    //编辑基本信息
                    unset($values['id']);
                    $this->infoService->doEdit(0, $values);
                }
            } else {
                //是否同步创建
                $isAutoCreated = $id == -1;
                if (empty($values['code'])) {
                    throw new AppException(StatusCode::VALIDATION_ERROR, '参数错误');
                }
                //校验是否已经创建
                if ($this->model::query()->where('code', $values['code'])->exists()) {
                    throw new AppException(StatusCode::VALIDATION_ERROR, '该订单已经生成');
                }
                //处理备货单
                $this->handleStockValue($values);
                $values['create_user_id'] = $isAutoCreated ? 0 : auth()->id();
                //创建类型,手动新增还是同步新增
                $values['create_type'] = $isAutoCreated ? ProductionOrderCode::CREATE_TYPE_AUTO : ProductionOrderCode::CREATE_TYPE_HAND;
                //绑定工厂
                //工厂信息改变
                if(!empty($values['factory_code'])){
                    $values['factory_id'] = make(ProductionFactoryService::class)->findOrCreateFactory(
                        [
                            'code' => $values['factory_code'],
                            'name' => $values['factory_name']??''
                        ]
                    );
                }
                $result = $this->model::create($values);

                if (!empty($result->id)) {
                    $orderId = $result->id;
                    //初始化附件资料
                    make(ProductionOrderAttachmentService::class)->initProductionOrderFile($orderId);
                    //保存基本信息
                    //如果是自动创建，则自动填充原理图负责人和layout负责人
                    $isAutoCreated && $this->autoSetUserBySameCode($values);
                    $infoData = array_merge($values, [
                        'production_order_id' => $orderId
                    ]);
                    $this->infoService->doEdit(0, $infoData,$isAutoCreated);
                    //填写了初始mac才分配mac地址
                    if (!empty($values['start_mac_address'])) {
                        $this->macService->assignMacAddress(ProductionCode::CODE_USED_TYPE_PRODUCTION, $orderId, $values['woqty'], $values['start_mac_address'] ?? null, $isAutoCreated);
                    }

                    //创建订单时，获取该产品的BOM，然后去匹配QC模块中的检验记录
                    //当条件为最后一次不合格时且不是退货的话，主动关联该记录
                    make(ProductionOrderIqcService::class)->autoRelateQc($orderId);

                    if ($isAutoCreated) {
                        Log::get('system', 'system')->info('创建订单成功', ['result' => $result]);
                    } elseif (isset($values['is_complete']) && $values['is_complete'] == 1) {
                        $isChangeCompleted = true;
                    }
                    //订单新建时自动关联异常
                    make(ExceptionRecordService::class)->doRelateException(ProductionCode::EXCEPTION_TYPE_PROUCTION_ORDER, $orderId);
                }
            }
            unset($values);
            //信息完善发送通知
            if ($isChangeCompleted && !empty($orderId)) {
                $this->sendNoticeAfterCreated($orderId);
                $this->sendNoticeAfterCompleted($orderId);
            }
            Db::commit();
            return $result;
        } catch (Exception $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

//    public function doDelete($ids) : int{
//        try {
//            foreach ($ids as $id) {
//                if ($id > 0) {
//                   $this->model::query()->where('id', $id)->delete();
//                }
//            }
//        } catch (Exception $e) {
//            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
//        }
//        return 1;
//}

    /**
     * 生产订单录入企业推送
     * @param $productionOrderId
     * @return void
     */
    public function sendNoticeAfterCreated($productionOrderId)
    {
        $order = $this->model::query()->find($productionOrderId);
        $userIds = $this->getOrderUser($productionOrderId);
        $uploadUserArr = make(UserService::class)->getUsersByRole(ProductionOrderCode::PRODUCTION_FILE_UP_ROLE);
        //生产上传人员不通知（单独通知），当前用户不通知
        $notSendUser = array_merge(array_column($uploadUserArr, 'id'), [auth()->id()]);
        $sendUser = array_diff($userIds, $notSendUser);
        make(NoticeService::class)->createProductionOrder($sendUser, $order);
    }

    /**
     * 订单信息完善通知生产文件上传角色填写资料
     * @param $productionOrderId
     * @return void
     */
    public function sendNoticeAfterCompleted($productionOrderId)
    {
        $order = $this->model::query()->find($productionOrderId);
        if (empty($order)) return;
        $uploadUserArr = make(UserService::class)->getUsersByRole(ProductionOrderCode::PRODUCTION_FILE_UP_ROLE);
        $sendUser = array_column($uploadUserArr, 'id');
        $order['notice_msg'] = '该生产订单信息已经完善，请前往录入资料';
        make(NoticeService::class)->commonProductionOrderNotice($sendUser, $order);
    }

    //获取最大的mac值

    /**
     * 获取工作状态选项列表
     * @return mixed[]
     */
    public function getWorkStatusList()
    {
        return make(WorkStatusModel::class)::query()->where('scene_type', WorkFlowSceneCode::SCENE_PRODUCTION_ORDER)->select([
            'id',
            'name',
            'key'
        ])->get()->toArray();
    }

    public function getMaxMac()
    {
        return MacAddressModel::query()->where('used_type', 1)->max('mac_decimal');
    }

    /**
     * 获取工作状态流
     * @param $id
     * @return array
     */
    public function getWorkFlow($id)
    {
        $workStatusID = ProductionOrderInfoModel::query()->where('production_order_id', $id)->value('work_status_id') ?: 0;
        return make(WorkStatusService::class)->formatStatus($workStatusID);
    }

    /**
     * 删除
     * @param $ids
     * @return int
     */
    public function doDelete($ids): int
    {
        Db::beginTransaction();
        try {
            $ids = !is_array($ids) ? explode(',', $ids) : $ids;
            $orderList = $this->model::query()->whereIn('id', $ids)->get();
            foreach ($orderList as $order) {
                $info = ProductionOrderInfoModel::query()->where('production_order_id',$order->id)->first();
                if(!empty($info['start_mac_address'])){
                    //取消mac绑定
                    $this->macService->reduceByRelationId($order->id, ProductionCode::CODE_USED_TYPE_PRODUCTION, $order->woqty);
                }
                //解绑和qc的关联
                make(ProductionOrderIqcService::class)->deleteRelation($order->id);
            }
            $result = $this->model::destroy($ids);
            Db::commit();
            return $result;
        } catch (Exception $e) {
            Db::rollBack();
            throw new AppException(StatusCode::ERR_SERVER, $e->getMessage());
        }
    }

    /**
     * 同步创建当天的生产订单
     */
    public function syncProductionOrder($json = null)
    {
        $startDate = date('Y-m-d', strtotime('-1 day'));
        // 获取当天checkS完成状态的订单号
        //从昨天开始查询
        $json = $json ?: [
            'filter' => [
                "checkdate" => $startDate,
                "checkS"    => 1
            ],
            'op'     => [
                "checkdate" => ">="
            ]
        ];
        $startTime = microtime(true);
        $erpData = make(\App\Core\Services\TchipSale\SaleBaseService::class)
            ->sendRequest('firefly_erpapi/public/index.php/outhelp/createh/getList', ['json' => $json], 'POST');
        if (!empty($erpData['data'])) {
            $data = [];
            $currentTime = time();
            $endOfDay = strtotime('tomorrow') - 2; // 今天结束的时间戳
            $processedOrders = $this->cache->get('production_processed_orders', []);

            foreach ($erpData['data'] as $order) {
                // 检查该订单号是否已处理
                if (in_array($order['code'], $processedOrders)) {
                    continue;
                }
                if (!empty($order['factoryname'])) {
                    $production_user_id = ProductionOrderCode::FACTORY_DEFAULT_INFO[$order['factoryname']]['production_user_id'] ?? 0;
                    $test_user_id = ProductionOrderCode::FACTORY_DEFAULT_INFO[$order['factoryname']]['test_user_id'] ?? 0;
                } else {
                    $production_user_id = $test_user_id = 0;
                }
                $data[] = [
                    'attachments'        => [],
                    'order_user_id'      => 111,
                    'code'               => $order['code'] ?? '',
                    'wocode'             => $order['wocode'] ?? '',
                    'factory_code'       => $order['factorycode'] ?? '',
                    'factory_name'       => $order['factoryname'] ?? '',
                    'product_name'       => $order['productname'] ?? '',
                    'product_code'       => $order['productcode'] ?? '',
                    'product_spec'       => $order['productspec'] ?? '',
                    'ddr_name'           => $order['ddr_name'] ?? '',
                    'pcb_name'           => $order['pcb_name'] ?? '',
                    'emmc_name'          => $order['emmc_name'] ?? '',
                    'woqty'              => $order['woqty'] ?? '',
                    'delivery_date'      => $order['deliverydate'] ?? '',
                    'production_user_id' => $production_user_id,
                    'test_user_id'       => $test_user_id,
                    'hardware_user_id'   => 0,
                    'layout_user_id'   => 0,
                    'structure_user_id'  => 0,
                    'software_user_id'   => 0,
                    'is_complete'        => 0,
                    //默认未完善
                ];
                // 将已处理的订单号添加到数组中
                $processedOrders[] = $order['code'];
            }

            // 更新缓存中的订单号，并设置过期时间为当天结束
            $this->cache->set('production_processed_orders', $processedOrders, $endOfDay - $currentTime);
            $parallel = new Parallel(5);
            $crontabData = [];
            foreach ($data as $item) {
                $crontabData[] = $item['code'] ?? '';
                // 调用 doEdit 方法，将 id 设置为 -1，并传递 $item 作为 values
                $parallel->add(function () use ($item) {
                    $orderIds = $parallelSuccessData = $parallelFailData = [];
                    try {
                        $result = $this->doEdit(-1, $item);
                        $orderIds[] = $result['id'] ?? 0;
                        $parallelSuccessData[] = $result;
                        $logData = Context::get(ProductionCode::LOG_KEY_TABLE_DATA, []);
                        return [
                            'order_ids'    => $orderIds,
                            'success_data' => $parallelSuccessData,
                            'fail_data'    => [],
                            'log_data'     => $logData
                        ];
                    } catch (Throwable $e) {
                        Log::get('system', 'system')
                            ->info('创建订单失败', [
                                'id'     => -1,
                                'values' => $item,
                                'error'  => $e->getMessage()
                            ]);
                        $parallelFailData[] = [
                            'data' => $item,
                            'msg'  => $e->getMessage()
                        ];
                        return [
                            'order_ids'    => [],
                            'success_data' => [],
                            'fail_data'    => $parallelFailData,
                            'log_data'     => []
                        ];
                    }
                });
            }
            $result = $parallel->wait();
            $orderIds = $successData = $failData = $logData = [];
            foreach ($result as $item) {
                $orderIds = array_merge($orderIds, $item['order_ids'] ?? []);
                $successData = array_merge($successData, $item['success_data'] ?? []);
                $failData = array_merge($failData, $item['fail_data'] ?? []);
                $logData = array_merge($logData, $item['log_data'] ?? []);
            }
            Context::set(ProductionCode::LOG_KEY_TABLE_DATA, $logData);
            //同步完成发生邮件通知
            if ($successData) {
                make(NoticeService::class)->syncProductionOrderSend($successData);
            }
            $endTime = microtime(true);
            Log::get('system', 'system')
                ->info('----------同步用时----------', [$endTime - $startTime]);
            return [
                'order_ids'      => $orderIds,
                'crontab_data'   => $crontabData,
                'crontab_result' => [
                    'success' => $successData,
                    'fail'    => $failData
                ],
                'crontab_status' => empty($failData) ? 1 : 2
            ];
        }
        
        // 如果没有数据，返回空结果
        return [
            'order_ids'      => [],
            'crontab_data'   => [],
            'crontab_result' => [
                'success' => [],
                'fail'    => []
            ],
            'crontab_status' => 1
        ];
    }


    /**
     * 发送生产订单信息
     * @param $productionOrderId
     */
    public function sendOrderMessage($values)
    {

        return make(\App\Core\Services\Notice\NoticeService::class)->sendOrderMessage( $values);
        //make(NoticeService::class)->createProductionOrder($sendUser, $order);
    }

    /**
     * 加锁防止同步并发
     * @param $isCli bool 是否命令执行
     * @return bool
     * @throws InvalidArgumentException
     */
    public function syncStart(bool $isCli = false)
    {
        $cacheKey = 'syncOrder';
        if ($this->cache->has($cacheKey)) {
            if ($isCli) {
                //定时任务执行时，如果有同步执行等待10秒待其结束
                for ($i = 0; $i < 10; $i++) {
                    sleep(1);
                    if (!$this->cache->has($cacheKey)) {
                        break;
                    }
                    if($i==9){//超时不同步
                        return false;
                    }
                }
            } else {
                throw new AppException(StatusCode::ERR_SERVER, __('production.Has_other_sync_task'));
            }
        }
        $this->cache->set($cacheKey, $cacheKey, 300);
        return true;
    }

    /**去锁
     * @return void
     * @throws InvalidArgumentException
     */
    public function syncEnd()
    {
        $cacheKey = 'syncOrder';
        $this->cache->delete($cacheKey);
    }

    /**获取生产订单相关用户以及相关角色用户
     * @param $productionOrderId
     * @return array
     */
    public function getOrderUser($productionOrderId)
    {
        $order = $this->model::query()->find($productionOrderId);
        $info = ProductionOrderInfoModel::query()->where('production_order_id', $productionOrderId)->first();
        if (empty($order) || empty($info)) return [];
        $infoUserArr = array_merge([
            $info['hardware_user_id'],
            $info['layout_user_id'],
            $info['software_user_id'],
            $info['structure_user_id'],
            $info['order_user_id'],
            $info['production_user_id'],
            $info['test_user_id'],
        ], $info['cc_user_ids'] ?: []);
        $productionUserArr = make(UserService::class)->getUsersByRole(ProductionOrderCode::PRODUCTION_ROLE);
        $uploadUserArr = make(UserService::class)->getUsersByRole(ProductionOrderCode::PRODUCTION_FILE_UP_ROLE);
        $factoryUserArr = make(UserService::class)->getUsersByRole(ProductionOrderCode::FACTORY_ROLE);
        return array_unique(array_filter(array_merge(
            $infoUserArr,
            array_column($productionUserArr, 'id'),
            array_column($uploadUserArr, 'id'),
            array_column($factoryUserArr, 'id'),
        )));
    }

    /**
     * 获取当前状态
     * @param $orderId
     * @return string
     */
    public function getCurrentWorkStatus($orderId)
    {
        $info = ProductionOrderInfoModel::query()->where('production_order_id', $orderId)->first();
        return WorkStatusModel::query()->where('id', $info['work_status_id'] ?? 0)->value('key') ?? '';
    }

    /**
     * 获取生产次数
     * @param $overView
     * @return int
     */
    public function getProductionCount($overView){
        return $this->model::query()->where('product_code',$overView['product_code'])
            ->where(function($query)use($overView){
                $query->where('delivery_date','<',$overView['delivery_date'])
                    ->orwhere(function ($query)use($overView){
                        $query->where('delivery_date','=',$overView['delivery_date'])
                            ->where('id','<=',$overView['production_order_id']);
                    });
            })
            ->count();
    }

    /**
     * 处理订单编辑时的备货单信息
     * @param $values
     * @return void
     */
    public function handleStockValue(&$values)
    {
        //校验备货单号
        if (!empty($values['stock_order_code'])) {
            if (!StockOrderlistModel::query()->where('sn', $values['stock_order_code'])->exists()) {
                throw new AppException(StatusCode::ERR_SERVER, __('production.Not_exit_stock_order'));
            }
        }
        //绑定业务员，业务员为空时，默认为文晓东
        if (isset($values['stock_user'])) {
            if (empty($values['stock_user'])) {
                $values['stock_user'] = AssembleOrderCode::DEFAULT_ORDER_USER['order_user_id'];
            }
            $values['stock_user_id'] = UserModel::query()->where('name', 'like', '%' . $values['stock_user'] . '%')->value('id') ?: 0;
        }
    }

    /**
     * 根据相同料号自动设置负责人
     * @param $value
     * @return void
     */
    public function autoSetUserBySameCode(&$value)
    {
        $productCode = $value['product_code'];

        //获取相同料号的订单的id
        $orderId = ProductionOrderModel::query()
            ->where('product_code', $productCode)
            ->pluck('id')->toArray();
        if($orderId){
            $infoList = ProductionOrderInfoModel::query()->whereIn('production_order_id',$orderId)->orderBy('production_order_id','desc')->get();
            foreach($infoList as $item){
                if($item['hardware_user_id']>0 && $value['hardware_user_id']==0){
                    $value['hardware_user_id'] = $item['hardware_user_id'];
                }
                if($item['layout_user_id']>0 && $value['layout_user_id']==0){
                    $value['layout_user_id'] = $item['layout_user_id'];
                }
                if($value['hardware_user_id']>0 && $value['layout_user_id']>0){
                    break;
                }
            }
        }
     }
}