<?php

declare(strict_types=1);
/**
 * This file is part of Hyperf.
 *
 * @link     https://www.hyperf.io
 * @document https://hyperf.wiki
 * @contact  <EMAIL>
 * @license  https://github.com/hyperf/hyperf/blob/master/LICENSE
 */
return [
    'default' => [
        'driver' => env('DB_DRIVER', 'mysql'),
        'host' => env('DB_HOST', 'localhost'),
        'port' => env('DB_PORT', 3306),
        'database' => env('DB_DATABASE', 'hyperf'),
        'username' => env('DB_USERNAME', 'root'),
        'password' => env('DB_PASSWORD', ''),
        'charset' => env('DB_CHARSET', 'utf8mb4'),
        'collation' => env('DB_COLLATION', 'utf8mb4_unicode_ci'),
        'prefix' => env('DB_PREFIX', ''),
        'pool' => [
            'min_connections' => 1,
            'max_connections' => (int) env('DB_MAX_CONNECTIONS', 10),
            'connect_timeout' => 10.0,
            'wait_timeout' => 3.0,
            'heartbeat' => -1,
            'max_idle_time' => (float) env('DB_MAX_IDLE_TIME', 60),
        ],
        'cache' => [
            'handler' => Hyperf\ModelCache\Handler\RedisHandler::class,
            'cache_key' => '{mc:%s:m:%s}:%s:%s',
            'prefix' => 'tchip_bi',
            'ttl' => 3600 * 24,
            'empty_model_ttl' => 600,
            'load_script' => true,
        ],
        'commands' => [
            'gen:model' => [
                'path' => 'app/Model',
                'force_casts' => true,
                'inheritance' => '\App\Model\Model',
                'uses' => '',
                'table_mapping' => [],
                'visitors' => [
                    Hyperf\Database\Commands\Ast\ModelRewriteKeyInfoVisitor::class,
                    Hyperf\Database\Commands\Ast\ModelRewriteSoftDeletesVisitor::class,
                    Hyperf\Database\Commands\Ast\ModelRewriteTimestampsVisitor::class
                ]
            ],
        ],
    ],
    'stationpc_bbscn' => [
        'driver' => env('DB_STATIONPC_BBSCN_DRIVER', 'mysql'),
        'host' => env('DB_STATIONPC_BBSCN_HOST', 'localhost'),
        'port' => env('DB_STATIONPC_BBSCN_PORT', 3306),
        'database' => env('DB_STATIONPC_BBSCN_DATABASE', 'hyperf'),
        'username' => env('DB_STATIONPC_BBSCN_USERNAME', 'root'),
        'password' => env('DB_STATIONPC_BBSCN_PASSWORD', ''),
        'charset' => env('DB_STATIONPC_BBSCN_CHARSET', 'utf8mb4'),
        'collation' => env('DB_STATIONPC_BBSCN_COLLATION', 'utf8mb4_unicode_ci'),
        'prefix' => env('DB_STATIONPC_BBSCN_PREFIX', ''),
        'pool' => [
            'min_connections' => 1,
            'max_connections' => (int) env('DB_MAX_CONNECTIONS', 10),
            'connect_timeout' => 10.0,
            'wait_timeout' => 3.0,
            'heartbeat' => -1,
            'max_idle_time' => (float) env('DB_MAX_IDLE_TIME', 60),
        ],
        'cache' => [
            'handler' => Hyperf\ModelCache\Handler\RedisHandler::class,
            'cache_key' => '{mc:%s:m:%s}:%s:%s',
            'prefix' => 'stationpc_bbscn',
            'ttl' => 3600 * 24,
            'empty_model_ttl' => 600,
            'load_script' => true,
        ],
        'commands' => [
            'gen:model' => [
                'path' => 'app/Model',
                'force_casts' => true,
                'inheritance' => '\App\Model\Model',
                'uses' => '',
                'table_mapping' => [],
                'visitors' => [
                    Hyperf\Database\Commands\Ast\ModelRewriteKeyInfoVisitor::class,
                    Hyperf\Database\Commands\Ast\ModelRewriteSoftDeletesVisitor::class,
                    Hyperf\Database\Commands\Ast\ModelRewriteTimestampsVisitor::class
                ]
            ],
        ],
    ],
    'stationpc_bbsen' => [
        'driver' => env('DB_STATIONPC_BBSEN_DRIVER', 'mysql'),
        'host' => env('DB_STATIONPC_BBSEN_HOST', 'localhost'),
        'port' => env('DB_STATIONPC_BBSEN_PORT', 3306),
        'database' => env('DB_STATIONPC_BBSEN_DATABASE', 'hyperf'),
        'username' => env('DB_STATIONPC_BBSEN_USERNAME', 'root'),
        'password' => env('DB_STATIONPC_BBSEN_PASSWORD', ''),
        'charset' => env('DB_STATIONPC_BBSEN_CHARSET', 'utf8mb4'),
        'collation' => env('DB_STATIONPC_BBSEN_COLLATION', 'utf8mb4_unicode_ci'),
        'prefix' => env('DB_STATIONPC_BBSEN_PREFIX', ''),
        'pool' => [
            'min_connections' => 1,
            'max_connections' => (int) env('DB_MAX_CONNECTIONS', 10),
            'connect_timeout' => 10.0,
            'wait_timeout' => 3.0,
            'heartbeat' => -1,
            'max_idle_time' => (float) env('DB_MAX_IDLE_TIME', 60),
        ],
        'cache' => [
            'handler' => Hyperf\ModelCache\Handler\RedisHandler::class,
            'cache_key' => '{mc:%s:m:%s}:%s:%s',
            'prefix' => 'stationpc_bbsen',
            'ttl' => 3600 * 24,
            'empty_model_ttl' => 600,
            'load_script' => true,
        ],
        'commands' => [
            'gen:model' => [
                'path' => 'app/Model',
                'force_casts' => true,
                'inheritance' => 'Model',
                'uses' => '',
                'table_mapping' => [],
                'visitors' => [
                    Hyperf\Database\Commands\Ast\ModelRewriteKeyInfoVisitor::class,
                    Hyperf\Database\Commands\Ast\ModelRewriteSoftDeletesVisitor::class,
                    Hyperf\Database\Commands\Ast\ModelRewriteTimestampsVisitor::class
                ]
            ],
        ],
    ],
    'stationpc_managercn' => [
        'driver' => env('DB_STATIONPC_MANAGER_DRIVER', 'mysql'),
        'host' => env('DB_STATIONPC_MANAGER_HOST', 'localhost'),
        'port' => env('DB_STATIONPC_MANAGER_PORT', 3306),
        'database' => env('DB_STATIONPC_MANAGER_DATABASE', 'hyperf'),
        'username' => env('DB_STATIONPC_MANAGER_USERNAME', 'root'),
        'password' => env('DB_STATIONPC_MANAGER_PASSWORD', ''),
        'charset' => env('DB_STATIONPC_MANAGER_CHARSET', 'utf8mb4'),
        'collation' => env('DB_STATIONPC_MANAGER_COLLATION', 'utf8mb4_unicode_ci'),
        'prefix' => env('DB_STATIONPC_MANAGER_PREFIX', ''),
        'pool' => [
            'min_connections' => 1,
            'max_connections' => (int) env('DB_MAX_CONNECTIONS', 10),
            'connect_timeout' => 10.0,
            'wait_timeout' => 3.0,
            'heartbeat' => -1,
            'max_idle_time' => (float) env('DB_MAX_IDLE_TIME', 60),
        ],
        'cache' => [
            'handler' => Hyperf\ModelCache\Handler\RedisHandler::class,
            'cache_key' => '{mc:%s:m:%s}:%s:%s',
            'prefix' => 'stationpc_managercn',
            'ttl' => 3600 * 24,
            'empty_model_ttl' => 600,
            'load_script' => true,
        ],
        'commands' => [
            'gen:model' => [
                'path' => 'app/Model',
                'force_casts' => true,
                'inheritance' => '\App\Model\Model',
                'uses' => '',
                'table_mapping' => [],
                'visitors' => [
                    Hyperf\Database\Commands\Ast\ModelRewriteKeyInfoVisitor::class,
                    Hyperf\Database\Commands\Ast\ModelRewriteSoftDeletesVisitor::class,
                    Hyperf\Database\Commands\Ast\ModelRewriteTimestampsVisitor::class
                ]
            ],
        ],
    ],
    'stationpc_manageren' => [
        'driver' => env('DB_STATIONPC_MANAGEREN_DRIVER', 'mysql'),
        'host' => env('DB_STATIONPC_MANAGEREN_HOST', 'localhost'),
        'port' => env('DB_STATIONPC_MANAGEREN_PORT', 3306),
        'database' => env('DB_STATIONPC_MANAGEREN_DATABASE', 'hyperf'),
        'username' => env('DB_STATIONPC_MANAGEREN_USERNAME', 'root'),
        'password' => env('DB_STATIONPC_MANAGEREN_PASSWORD', ''),
        'charset' => env('DB_STATIONPC_MANAGEREN_CHARSET', 'utf8mb4'),
        'collation' => env('DB_STATIONPC_MANAGEREN_COLLATION', 'utf8mb4_unicode_ci'),
        'prefix' => env('DB_STATIONPC_MANAGEREN_PREFIX', ''),
        'pool' => [
            'min_connections' => 1,
            'max_connections' => (int) env('DB_MAX_CONNECTIONS', 10),
            'connect_timeout' => 10.0,
            'wait_timeout' => 3.0,
            'heartbeat' => -1,
            'max_idle_time' => (float) env('DB_MAX_IDLE_TIME', 60),
        ],
        'cache' => [
            'handler' => Hyperf\ModelCache\Handler\RedisHandler::class,
            'cache_key' => '{mc:%s:m:%s}:%s:%s',
            'prefix' => 'stationpc_manageren',
            'ttl' => 3600 * 24,
            'empty_model_ttl' => 600,
            'load_script' => true,
        ],
        'commands' => [
            'gen:model' => [
                'path' => 'app/Model',
                'force_casts' => true,
                'inheritance' => '\App\Model\Model',
                'uses' => '',
                'table_mapping' => [],
                'visitors' => [
                    Hyperf\Database\Commands\Ast\ModelRewriteKeyInfoVisitor::class,
                    Hyperf\Database\Commands\Ast\ModelRewriteSoftDeletesVisitor::class,
                    Hyperf\Database\Commands\Ast\ModelRewriteTimestampsVisitor::class
                ]
            ],
        ],
    ],
    'tchip_redmine' => [
        'driver' => env('DB_TCHIP_REDMINE_DRIVER', 'mysql'),
        'host' => env('DB_TCHIP_REDMINE_HOST', 'localhost'),
        'port' => env('DB_TCHIP_REDMINE_PORT', 3306),
        'database' => env('DB_TCHIP_REDMINE_DATABASE', 'hyperf'),
        'username' => env('DB_TCHIP_REDMINE_USERNAME', 'root'),
        'password' => env('DB_TCHIP_REDMINE_PASSWORD', ''),
        'charset' => env('DB_TCHIP_REDMINE_CHARSET', 'utf8mb4'),
        'collation' => env('DB_TCHIP_REDMINE_COLLATION', 'utf8mb4_unicode_ci'),
        'prefix' => env('DB_TCHIP_REDMINE_PREFIX', ''),
        'pool' => [
            'min_connections' => 1,
            'max_connections' => (int) env('DB_MAX_CONNECTIONS', 10),
            'connect_timeout' => 10.0,
            'wait_timeout' => 3.0,
            'heartbeat' => -1,
            'max_idle_time' => (float) env('DB_MAX_IDLE_TIME', 60),
        ],
        'cache' => [
            'handler' => Hyperf\ModelCache\Handler\RedisHandler::class,
            'cache_key' => '{mc:%s:m:%s}:%s:%s',
            'prefix' => 'tchip_redmine',
            'ttl' => 3600 * 24,
            'empty_model_ttl' => 600,
            'load_script' => true,
        ],
        'commands' => [
            'gen:model' => [
                'path' => 'app/Model',
                'force_casts' => true,
                'inheritance' => '\App\Model\Redmine\RedmineBaseModel',
                'uses' => '',
                'table_mapping' => [],
                'visitors' => [
                    Hyperf\Database\Commands\Ast\ModelRewriteKeyInfoVisitor::class,
                    Hyperf\Database\Commands\Ast\ModelRewriteSoftDeletesVisitor::class,
                ]
            ],
        ],
    ],
    'tchip_sale' => [
        'driver' => env('DB_TCHIP_SALE_DRIVER', 'mysql'),
        'host' => env('DB_TCHIP_SALE_HOST', 'localhost'),
        'port' => env('DB_TCHIP_SALE_PORT', 3306),
        'database' => env('DB_TCHIP_SALE_DATABASE', 'hyperf'),
        'username' => env('DB_TCHIP_SALE_USERNAME', 'root'),
        'password' => env('DB_TCHIP_SALE_PASSWORD', ''),
        'charset' => env('DB_TCHIP_SALE_CHARSET', 'utf8'),
        'collation' => env('DB_TCHIP_SALE_COLLATION', 'utf8_unicode_ci'),
        'prefix' => env('DB_TCHIP_SALE_PREFIX', ''),
        'pool' => [
            'min_connections' => 1,
            'max_connections' => (int) env('DB_MAX_CONNECTIONS', 10),
            'connect_timeout' => 10.0,
            'wait_timeout' => 3.0,
            'heartbeat' => -1,
            'max_idle_time' => (float) env('DB_MAX_IDLE_TIME', 60),
        ],
        'cache' => [
            'handler' => Hyperf\ModelCache\Handler\RedisHandler::class,
            'cache_key' => '{mc:%s:m:%s}:%s:%s',
            'prefix' => 'tchip_redmine',
            'ttl' => 3600 * 24,
            'empty_model_ttl' => 600,
            'load_script' => true,
        ],
        'commands' => [
            'gen:model' => [
                'path' => 'app/Model',
                'force_casts' => true,
                'inheritance' => '\App\Model\TchipSale\SaleBaseModel',
                'uses' => '',
                'table_mapping' => [],
                'visitors' => [
                    Hyperf\Database\Commands\Ast\ModelRewriteKeyInfoVisitor::class,
                    Hyperf\Database\Commands\Ast\ModelRewriteSoftDeletesVisitor::class,
                ]
            ],
        ],
    ],
    'tchip_oa' => [
        'driver' => env('DB_TCHIP_OA_DRIVER', 'mysql'),
        'host' => env('DB_TCHIP_OA_HOST', 'localhost'),
        'port' => env('DB_TCHIP_OA_PORT', 3306),
        'database' => env('DB_TCHIP_OA_DATABASE', 'hyperf'),
        'username' => env('DB_TCHIP_OA_USERNAME', 'root'),
        'password' => env('DB_TCHIP_OA_PASSWORD', ''),
        'charset' => env('DB_TCHIP_OA_CHARSET', 'utf8'),
        'collation' => env('DB_TCHIP_OA_COLLATION', 'utf8_unicode_ci'),
        'prefix' => env('DB_TCHIP_OA_PREFIX', ''),
        'pool' => [
            'min_connections' => 1,
            'max_connections' => (int) env('DB_MAX_CONNECTIONS', 10),
            'connect_timeout' => 10.0,
            'wait_timeout' => 3.0,
            'heartbeat' => -1,
            'max_idle_time' => (float) env('DB_MAX_IDLE_TIME', 60),
        ],
        'cache' => [
            'handler' => Hyperf\ModelCache\Handler\RedisHandler::class,
            'cache_key' => '{mc:%s:m:%s}:%s:%s',
            'prefix' => 'tchip_oa',
            'ttl' => 3600 * 24,
            'empty_model_ttl' => 600,
            'load_script' => true,
        ],
        'commands' => [
            'gen:model' => [
                'path' => 'app/Model',
                'force_casts' => true,
                'inheritance' => '\App\Model\TchipOA\OABaseModel',
                'uses' => '',
                'table_mapping' => [],
                'visitors' => [
                    Hyperf\Database\Commands\Ast\ModelRewriteKeyInfoVisitor::class,
                    Hyperf\Database\Commands\Ast\ModelRewriteSoftDeletesVisitor::class,
                ]
            ],
        ],
    ],
    'tchip_bbs' => [
        'driver' => env('DB_TCHIP_OA_DRIVER', 'mysql'),
        'host' => env('DB_TCHIP_BBS_HOST', 'localhost'),
        'port' => env('DB_TCHIP_OA_PORT', 3306),
        'database' => env('DB_TCHIP_BBS_DATABASE', 'hyperf'),
        'username' => env('DB_TCHIP_BBS_USERNAME', 'root'),
        'password' => env('DB_TCHIP_BBS_PASSWORD', ''),
        'charset' => env('DB_TCHIP_BBS_CHARSET', 'utf8'),
        'collation' => env('DB_TCHIP_BBS_COLLATION', 'utf8_unicode_ci'),
        'prefix' => env('DB_TCHIP_BBS_PREFIX', ''),
        'pool' => [
            'min_connections' => 1,
            'max_connections' => (int) env('DB_MAX_CONNECTIONS', 10),
            'connect_timeout' => 10.0,
            'wait_timeout' => 3.0,
            'heartbeat' => -1,
            'max_idle_time' => (float) env('DB_MAX_IDLE_TIME', 60),
        ],
        'cache' => [
            'handler' => Hyperf\ModelCache\Handler\RedisHandler::class,
            'cache_key' => '{mc:%s:m:%s}:%s:%s',
            'prefix' => 'tchip_oa',
            'ttl' => 3600 * 24,
            'empty_model_ttl' => 600,
            'load_script' => true,
        ],
        'commands' => [
            'gen:model' => [
                'path' => 'app/Model',
                'force_casts' => true,
                'inheritance' => '\App\Model\TchipBbs\BbsBaseModel',
                'uses' => '',
                'table_mapping' => [],
                'visitors' => [
                    Hyperf\Database\Commands\Ast\ModelRewriteKeyInfoVisitor::class,
                    Hyperf\Database\Commands\Ast\ModelRewriteSoftDeletesVisitor::class,
                ]
            ],
        ],
    ],
    'tchip_ucenter' => [
        'driver' => env('DB_TCHIP_UCENTER_DRIVER', 'mysql'),
        'host' => env('DB_TCHIP_UCENTER_HOST', 'localhost'),
        'port' => env('DB_TCHIP_UCENTER_PORT', 3306),
        'database' => env('DB_TCHIP_UCENTER_DATABASE', 'hyperf'),
        'username' => env('DB_TCHIP_UCENTER_USERNAME', 'root'),
        'password' => env('DB_TCHIP_UCENTER_PASSWORD', ''),
        'charset' => env('DB_TCHIP_UCENTER_CHARSET', 'utf8'),
        'collation' => env('DB_TCHIP_UCENTER_COLLATION', 'utf8_unicode_ci'),
        'prefix' => env('DB_TCHIP_UCENTER_PREFIX', ''),
        'pool' => [
            'min_connections' => 1,
            'max_connections' => (int) env('DB_MAX_CONNECTIONS', 10),
            'connect_timeout' => 10.0,
            'wait_timeout' => 3.0,
            'heartbeat' => -1,
            'max_idle_time' => (float) env('DB_MAX_IDLE_TIME', 60),
        ],
        'cache' => [
            'handler' => Hyperf\ModelCache\Handler\RedisHandler::class,
            'cache_key' => '{mc:%s:m:%s}:%s:%s',
            'prefix' => 'tchip_oa',
            'ttl' => 3600 * 24,
            'empty_model_ttl' => 600,
            'load_script' => true,
        ],
        'commands' => [
            'gen:model' => [
                'path' => 'app/Model',
                'force_casts' => true,
                'inheritance' => '\App\Model\TchipBbs\BbsBaseModel',
                'uses' => '',
                'table_mapping' => [],
                'visitors' => [
                    Hyperf\Database\Commands\Ast\ModelRewriteKeyInfoVisitor::class,
                    Hyperf\Database\Commands\Ast\ModelRewriteSoftDeletesVisitor::class,
                ]
            ],
        ],
    ],
    'firefly_service' => [
        'driver' => env('DB_DRIVER', 'mysql'),
        'host' => env('DB_FIREFLY_SERVICE_HOST', 'localhost'),
        'port' => env('DB_PORT', 3306),
        'database' => env('DB_FIREFLY_SERVICE_DATABASE', 'firefly_service'),
        'username' => env('DB_FIREFLY_SERVICE_USERNAME', 'root'),
        'password' => env('DB_FIREFLY_SERVICE_PASSWORD', ''),
        'charset' => env('DB_CHARSET', 'utf8mb4'),
        'collation' => env('DB_COLLATION', 'utf8mb4_unicode_ci'),
        'prefix' => env('DB_FIREFLY_SERVICE_PREFIX', ''),
        'pool' => [
            'min_connections' => 1,
            'max_connections' => 10,
            'connect_timeout' => 10.0,
            'wait_timeout' => 3.0,
            'heartbeat' => -1,
            'max_idle_time' => (float) env('DB_MAX_IDLE_TIME', 60),
        ],
        'cache' => [
            'handler' => Hyperf\ModelCache\Handler\RedisHandler::class,
            'cache_key' => '{mc:%s:m:%s}:%s:%s',
            'prefix' => 'firefly_service',
            'ttl' => 3600 * 24,
            'empty_model_ttl' => 600,
            'load_script' => true,
        ],
        'commands' => [
            'gen:model' => [
                'path' => 'app/Model',
                'force_casts' => true,
                'inheritance' => '\App\Model\FireflyService\FireflyServiceBaseModel',
                'uses' => '',
                'table_mapping' => [],
                'visitors' => [
                    Hyperf\Database\Commands\Ast\ModelRewriteKeyInfoVisitor::class,
                    Hyperf\Database\Commands\Ast\ModelRewriteSoftDeletesVisitor::class,
                ]
            ],
        ],
    ],
];
