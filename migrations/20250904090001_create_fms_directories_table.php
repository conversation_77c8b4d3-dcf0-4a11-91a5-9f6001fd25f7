<?php

use Hyperf\Database\Schema\Schema;
use Hyperf\Database\Schema\Blueprint;
use Hyperf\Database\Migrations\Migration;

class CreateFmsDirectoriesTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fms_directories', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('parent_id')->nullable()->comment('父目录ID');
            $table->string('name', 255)->comment('目录名称');
            $table->text('path')->nullable()->comment('完整路径');
            $table->string('description', 500)->nullable()->comment('目录描述');
            $table->bigInteger('owner_id')->comment('所有者ID');
            $table->string('owner_type', 50)->default('user')->comment('所有者类型：user/dept/role');
            $table->enum('visibility', ['public', 'private', 'internal'])->default('public')->comment('可见性：public/private/internal');
            $table->integer('sort_order')->default(0)->comment('排序权重');
            $table->bigInteger('created_by')->comment('创建者ID');
            $table->bigInteger('updated_by')->nullable()->comment('更新者ID');
            $table->dateTime('created_at')->nullable()->comment('创建时间');
            $table->dateTime('updated_at')->nullable()->comment('更新时间');
            $table->dateTime('deleted_at')->nullable()->comment('删除时间');
            $table->bigInteger('deleted_by')->nullable()->comment('删除者ID');
            
            // 索引
            $table->index(['parent_id'], 'idx_fms_directories_parent_id');
            $table->index(['owner_type', 'owner_id'], 'idx_fms_directories_owner');
            $table->index(['visibility'], 'idx_fms_directories_visibility');
            $table->index(['deleted_at'], 'idx_fms_directories_deleted_at');
            $table->index(['created_at'], 'idx_fms_directories_created_at');
            $table->index(['sort_order'], 'idx_fms_directories_sort_order');
            
            // 外键约束
            $table->foreign('parent_id')->references('id')->on('fms_directories')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fms_directories');
    }
}
